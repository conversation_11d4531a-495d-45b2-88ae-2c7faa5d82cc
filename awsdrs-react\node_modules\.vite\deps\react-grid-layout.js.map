{"version": 3, "sources": ["../../fast-equals/src/utils.ts", "../../fast-equals/src/comparator.ts", "../../fast-equals/src/arrays.ts", "../../fast-equals/src/dates.ts", "../../fast-equals/src/maps.ts", "../../fast-equals/src/objects.ts", "../../fast-equals/src/regexps.ts", "../../fast-equals/src/sets.ts", "../../fast-equals/src/index.ts", "../../clsx/dist/clsx.js", "../../react-grid-layout/build/fastRGLPropsEqual.js", "../../react-grid-layout/build/utils.js", "../../react-grid-layout/build/calculateUtils.js", "../../prop-types/node_modules/react-is/cjs/react-is.development.js", "../../prop-types/node_modules/react-is/index.js", "../../object-assign/index.js", "../../prop-types/lib/ReactPropTypesSecret.js", "../../prop-types/lib/has.js", "../../prop-types/checkPropTypes.js", "../../prop-types/factoryWithTypeCheckers.js", "../../prop-types/index.js", "../../react-draggable/build/cjs/utils/shims.js", "../../react-draggable/build/cjs/utils/getPrefix.js", "../../react-draggable/build/cjs/utils/domFns.js", "../../react-draggable/build/cjs/utils/positionFns.js", "../../react-draggable/build/cjs/utils/log.js", "../../react-draggable/build/cjs/DraggableCore.js", "../../react-draggable/build/cjs/Draggable.js", "../../react-draggable/build/cjs/cjs.js", "../../react-resizable/build/utils.js", "../../react-resizable/build/propTypes.js", "../../react-resizable/build/Resizable.js", "../../react-resizable/build/ResizableBox.js", "../../react-resizable/index.js", "../../react-grid-layout/build/ReactGridLayoutPropTypes.js", "../../react-grid-layout/build/GridItem.js", "../../react-grid-layout/build/ReactGridLayout.js", "../../react-grid-layout/build/responsiveUtils.js", "../../react-grid-layout/build/ResponsiveReactGridLayout.js", "../../resize-observer-polyfill/dist/ResizeObserver.es.js", "../../react-grid-layout/build/components/WidthProvider.js", "../../react-grid-layout/index.js"], "sourcesContent": ["import {\n  EqualityComparator,\n  InternalEqualityComparator,\n  TypeEqualityComparator,\n} from '../index.d';\n\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nexport function createDefaultIsNestedEqual<Meta>(\n  comparator: EqualityComparator<Meta>,\n): InternalEqualityComparator<Meta> {\n  return function isEqual<A, B>(\n    a: A,\n    b: B,\n    _indexOrKeyA: any,\n    _indexOrKeyB: any,\n    _parentA: any,\n    _parentB: any,\n    meta: Meta,\n  ) {\n    return comparator(a, b, meta);\n  };\n}\n\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular cache, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nexport function createIsCircular<\n  AreItemsEqual extends TypeEqualityComparator<any, any>,\n>(areItemsEqual: AreItemsEqual): AreItemsEqual {\n  return function isCircular(\n    a: any,\n    b: any,\n    isEqual: InternalEqualityComparator<WeakMap<any, any>>,\n    cache: WeakMap<any, any>,\n  ) {\n    if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n      return areItemsEqual(a, b, isEqual, cache);\n    }\n\n    const cachedA = cache.get(a);\n    const cachedB = cache.get(b);\n\n    if (cachedA && cachedB) {\n      return cachedA === b && cachedB === a;\n    }\n\n    cache.set(a, b);\n    cache.set(b, a);\n\n    const result = areItemsEqual(a, b, isEqual, cache);\n\n    cache.delete(a);\n    cache.delete(b);\n\n    return result;\n  } as AreItemsEqual;\n}\n\n/**\n * Targeted shallow merge of two objects.\n *\n * @NOTE\n * This exists as a tinier compiled version of the `__assign` helper that\n * `tsc` injects in case of `Object.assign` not being present.\n */\nexport function merge<A extends object, B extends object>(a: A, b: B): A & B {\n  const merged: Record<string, any> = {};\n\n  for (const key in a) {\n    merged[key] = a[key];\n  }\n\n  for (const key in b) {\n    merged[key] = b[key];\n  }\n\n  return merged as A & B;\n}\n\n/**\n * Whether the value is a plain object.\n *\n * @NOTE\n * This is a same-realm compariosn only.\n */\nexport function isPlainObject(value: any): boolean {\n  return value.constructor === Object || value.constructor == null;\n}\n\n/**\n * When the value is `Promise`-like, aka \"then-able\".\n */\nexport function isPromiseLike(value: any): boolean {\n  return typeof value.then === 'function';\n}\n\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nexport function sameValueZeroEqual(a: any, b: any): boolean {\n  return a === b || (a !== a && b !== b);\n}\n", "import { isPlainObject, isPromiseLike, sameValueZeroEqual } from './utils';\n\nimport type {\n  CreateComparatorCreatorOptions,\n  EqualityComparator,\n} from '../index.d';\n\nconst ARGUMENTS_TAG = '[object Arguments]';\nconst BOOLEAN_TAG = '[object Boolean]';\nconst DATE_TAG = '[object Date]';\nconst REG_EXP_TAG = '[object RegExp]';\nconst MAP_TAG = '[object Map]';\nconst NUMBER_TAG = '[object Number]';\nconst OBJECT_TAG = '[object Object]';\nconst SET_TAG = '[object Set]';\nconst STRING_TAG = '[object String]';\n\nconst { toString } = Object.prototype;\n\nexport function createComparator<Meta>({\n  areArraysEqual,\n  areDatesEqual,\n  areMapsEqual,\n  areObjectsEqual,\n  areRegExpsEqual,\n  areSetsEqual,\n  createIsNestedEqual,\n}: CreateComparatorCreatorOptions<Meta>): EqualityComparator<Meta> {\n  const isEqual = createIsNestedEqual(comparator as EqualityComparator<Meta>);\n\n  /**\n   * compare the value of the two objects and return true if they are equivalent in values\n   */\n  function comparator(a: any, b: any, meta: Meta): boolean {\n    // If the items are strictly equal, no need to do a value comparison.\n    if (a === b) {\n      return true;\n    }\n\n    // If the items are not non-nullish objects, then the only possibility\n    // of them being equal but not strictly is if they are both `NaN`. Since\n    // `NaN` is uniquely not equal to itself, we can use self-comparison of\n    // both objects, which is faster than `isNaN()`.\n    if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n      return a !== a && b !== b;\n    }\n\n    // Checks are listed in order of commonality of use-case:\n    //   1. Common complex object types (plain object, array)\n    //   2. Common data values (date, regexp)\n    //   3. Less-common complex object types (map, set)\n    //   4. Less-common data values (promise, primitive wrappers)\n    // Inherently this is both subjective and assumptive, however\n    // when reviewing comparable libraries in the wild this order\n    // appears to be generally consistent.\n\n    // `isPlainObject` only checks against the object's own realm. Cross-realm\n    // comparisons are rare, and will be handled in the ultimate fallback, so\n    // we can avoid the `toString.call()` cost unless necessary.\n    if (isPlainObject(a) && isPlainObject(b)) {\n      return areObjectsEqual(a, b, isEqual, meta);\n    }\n\n    // `isArray()` works on subclasses and is cross-realm, so we can again avoid\n    // the `toString.call()` cost unless necessary by just checking if either\n    // and then both are arrays.\n    const aArray = Array.isArray(a);\n    const bArray = Array.isArray(b);\n\n    if (aArray || bArray) {\n      return aArray === bArray && areArraysEqual(a, b, isEqual, meta);\n    }\n\n    // Since this is a custom object, use the classic `toString.call()` to get its\n    // type. This is reasonably performant in modern environments like v8 and\n    // SpiderMonkey, and allows for cross-realm comparison when other checks like\n    // `instanceof` do not.\n    const aTag = toString.call(a);\n\n    if (aTag !== toString.call(b)) {\n      return false;\n    }\n\n    if (aTag === DATE_TAG) {\n      // `getTime()` showed better results compared to alternatives like `valueOf()`\n      // or the unary `+` operator.\n      return areDatesEqual(a, b, isEqual, meta);\n    }\n\n    if (aTag === REG_EXP_TAG) {\n      return areRegExpsEqual(a, b, isEqual, meta);\n    }\n\n    if (aTag === MAP_TAG) {\n      return areMapsEqual(a, b, isEqual, meta);\n    }\n\n    if (aTag === SET_TAG) {\n      return areSetsEqual(a, b, isEqual, meta);\n    }\n\n    // If a simple object tag, then we can prioritize a simple object comparison because\n    // it is likely a custom class. If an arguments tag, it should be treated as a standard\n    // object.\n    if (aTag === OBJECT_TAG || aTag === ARGUMENTS_TAG) {\n      // The exception for value comparison is `Promise`-like contracts. These should be\n      // treated the same as standard `Promise` objects, which means strict equality.\n      return isPromiseLike(a) || isPromiseLike(b)\n        ? false\n        : areObjectsEqual(a, b, isEqual, meta);\n    }\n\n    // As the penultimate fallback, check if the values passed are primitive wrappers. This\n    // is very rare in modern JS, which is why it is deprioritized compared to all other object\n    // types.\n    if (aTag === BOOLEAN_TAG || aTag === NUMBER_TAG || aTag === STRING_TAG) {\n      return sameValueZeroEqual(a.valueOf(), b.valueOf());\n    }\n\n    // If not matching any tags that require a specific type of comparison, then we hard-code false because\n    // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n    //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n    //     comparison that can be made.\n    //   - For types that can be introspected, but rarely have requirements to be compared\n    //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n    //     use-cases (may be included in a future release, if requested enough).\n    //   - For types that can be introspected but do not have an objective definition of what\n    //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n    // In all cases, these decisions should be reevaluated based on changes to the language and\n    // common development practices.\n    return false;\n  }\n\n  return comparator as EqualityComparator<Meta>;\n}\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\n/**\n * Whether the arrays are equal in value.\n */\nexport function areArraysEqual(\n  a: any[],\n  b: any[],\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    if (!isEqual(a[index], b[index], index, index, a, b, meta)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the arrays are equal in value, including circular references.\n */\nexport const areArraysEqualCircular = createIsCircular(areArraysEqual);\n", "import { sameValueZeroEqual } from './utils';\n\n/**\n * Whether the dates passed are equal in value.\n *\n * @NOTE\n * This is a standalone function instead of done inline in the comparator\n * to allow for overrides.\n */\nexport function areDatesEqual(a: Date, b: Date): boolean {\n  return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\n/**\n * Whether the `Map`s are equal in value.\n */\nexport function areMapsEqual(\n  a: Map<any, any>,\n  b: Map<any, any>,\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  let isValueEqual = a.size === b.size;\n\n  if (!isValueEqual) {\n    return false;\n  }\n\n  if (!a.size) {\n    return true;\n  }\n\n  // The use of `forEach()` is to avoid the transpilation cost of `for...of` comparisons, and\n  // the inability to control the performance of the resulting code. It also avoids excessive\n  // iteration compared to doing comparisons of `keys()` and `values()`. As a result, though,\n  // we cannot short-circuit the iterations; bookkeeping must be done to short-circuit the\n  // equality checks themselves.\n\n  const matchedIndices: Record<number, true> = {};\n\n  let indexA = 0;\n\n  a.forEach((aValue, aKey) => {\n    if (!isValueEqual) {\n      return;\n    }\n\n    let hasMatch = false;\n    let matchIndexB = 0;\n\n    b.forEach((bValue, bKey) => {\n      if (\n        !hasMatch &&\n        !matchedIndices[matchIndexB] &&\n        (hasMatch =\n          isEqual(aKey, bKey, indexA, matchIndexB, a, b, meta) &&\n          isEqual(aValue, bValue, aKey, bKey, a, b, meta))\n      ) {\n        matchedIndices[matchIndexB] = true;\n      }\n\n      matchIndexB++;\n    });\n\n    indexA++;\n    isValueEqual = hasMatch;\n  });\n\n  return isValueEqual;\n}\n\n/**\n * Whether the `Map`s are equal in value, including circular references.\n */\nexport const areMapsEqualCircular = createIsCircular(areMapsEqual);\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\ninterface Dictionary<Value> {\n  [key: string]: Value;\n  $$typeof?: any;\n}\n\nconst OWNER = '_owner';\nconst { hasOwnProperty } = Object.prototype;\n\n/**\n * Whether the objects are equal in value.\n */\nexport function areObjectsEqual(\n  a: Dictionary<any>,\n  b: Dictionary<any>,\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  const keysA = Object.keys(a);\n\n  let index = keysA.length;\n\n  if (Object.keys(b).length !== index) {\n    return false;\n  }\n\n  let key: string;\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    key = keysA[index];\n\n    if (key === OWNER) {\n      const reactElementA = !!a.$$typeof;\n      const reactElementB = !!b.$$typeof;\n\n      if ((reactElementA || reactElementB) && reactElementA !== reactElementB) {\n        return false;\n      }\n    }\n\n    if (\n      !hasOwnProperty.call(b, key) ||\n      !isEqual(a[key], b[key], key, key, a, b, meta)\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the objects are equal in value, including circular references.\n */\nexport const areObjectsEqualCircular = createIsCircular(areObjectsEqual);\n", "/**\n * Whether the regexps passed are equal in value.\n *\n * @NOTE\n * This is a standalone function instead of done inline in the comparator\n * to allow for overrides. An example of this would be supporting a\n * pre-ES2015 environment where the `flags` property is not available.\n */\nexport function areRegExpsEqual(a: RegExp, b: RegExp): boolean {\n  return a.source === b.source && a.flags === b.flags;\n}\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\n/**\n * Whether the `Set`s are equal in value.\n */\nexport function areSetsEqual(\n  a: Set<any>,\n  b: Set<any>,\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  let isValueEqual = a.size === b.size;\n\n  if (!isValueEqual) {\n    return false;\n  }\n\n  if (!a.size) {\n    return true;\n  }\n\n  // The use of `forEach()` is to avoid the transpilation cost of `for...of` comparisons, and\n  // the inability to control the performance of the resulting code. It also avoids excessive\n  // iteration compared to doing comparisons of `keys()` and `values()`. As a result, though,\n  // we cannot short-circuit the iterations; bookkeeping must be done to short-circuit the\n  // equality checks themselves.\n\n  const matchedIndices: Record<number, true> = {};\n\n  a.forEach((aValue, aKey) => {\n    if (!isValueEqual) {\n      return;\n    }\n\n    let hasMatch = false;\n    let matchIndex = 0;\n\n    b.forEach((bValue, bKey) => {\n      if (\n        !hasMatch &&\n        !matchedIndices[matchIndex] &&\n        (hasMatch = isEqual(aValue, bValue, aKey, bKey, a, b, meta))\n      ) {\n        matchedIndices[matchIndex] = true;\n      }\n\n      matchIndex++;\n    });\n\n    isValueEqual = hasMatch;\n  });\n\n  return isValueEqual;\n}\n\n/**\n * Whether the `Set`s are equal in value, including circular references.\n */\nexport const areSetsEqualCircular = createIsCircular(areSetsEqual);\n", "import { createComparator } from './comparator';\nimport { areArraysEqual, areArraysEqualCircular } from './arrays';\nimport { areDatesEqual } from './dates';\nimport { areMapsEqual, areMapsEqualCircular } from './maps';\nimport { areObjectsEqual, areObjectsEqualCircular } from './objects';\nimport { areRegExpsEqual } from './regexps';\nimport { areSetsEqual, areSetsEqualCircular } from './sets';\nimport { createDefaultIsNestedEqual, merge, sameValueZeroEqual } from './utils';\n\nimport type {\n  BaseCircularMeta,\n  CreateComparatorCreatorOptions,\n  EqualityComparator,\n  GetComparatorOptions,\n} from '../index.d';\n\nexport { sameValueZeroEqual };\n\nconst DEFAULT_CONFIG: CreateComparatorCreatorOptions<undefined> = Object.freeze(\n  {\n    areArraysEqual,\n    areDatesEqual,\n    areMapsEqual,\n    areObjectsEqual,\n    areRegExpsEqual,\n    areSetsEqual,\n    createIsNestedEqual: createDefaultIsNestedEqual,\n  },\n);\nconst DEFAULT_CIRCULAR_CONFIG: CreateComparatorCreatorOptions<BaseCircularMeta> =\n  Object.freeze({\n    areArraysEqual: areArraysEqualCircular,\n    areDatesEqual,\n    areMapsEqual: areMapsEqualCircular,\n    areObjectsEqual: areObjectsEqualCircular,\n    areRegExpsEqual,\n    areSetsEqual: areSetsEqualCircular,\n    createIsNestedEqual: createDefaultIsNestedEqual,\n  });\n\nconst isDeepEqual = createComparator(DEFAULT_CONFIG);\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nexport function deepEqual<A, B>(a: A, b: B): boolean {\n  return isDeepEqual(a, b, undefined);\n}\n\nconst isShallowEqual = createComparator(\n  merge(DEFAULT_CONFIG, { createIsNestedEqual: () => sameValueZeroEqual }),\n);\n\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nexport function shallowEqual<A, B>(a: A, b: B): boolean {\n  return isShallowEqual(a, b, undefined);\n}\n\nconst isCircularDeepEqual = createComparator(DEFAULT_CIRCULAR_CONFIG);\n\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nexport function circularDeepEqual<A, B>(a: A, b: B): boolean {\n  return isCircularDeepEqual(a, b, new WeakMap());\n}\n\nconst isCircularShallowEqual = createComparator(\n  merge(DEFAULT_CIRCULAR_CONFIG, {\n    createIsNestedEqual: () => sameValueZeroEqual,\n  }),\n);\n\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nexport function circularShallowEqual<A, B>(a: A, b: B): boolean {\n  return isCircularShallowEqual(a, b, new WeakMap());\n}\n\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nexport function createCustomEqual<Meta = undefined>(\n  getComparatorOptions: GetComparatorOptions<Meta>,\n): EqualityComparator<Meta> {\n  return createComparator<Meta>(\n    merge(DEFAULT_CONFIG, getComparatorOptions(DEFAULT_CONFIG as any)),\n  );\n}\n\n/**\n * Create a custom equality comparison method that handles circular references. This is very\n * similar to `createCustomEqual`, with the only difference being that `meta` expects to be\n * populated with a `WeakMap`-like contract.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `WeakMap` out of the box.\n */\nexport function createCustomCircularEqual<\n  Meta extends BaseCircularMeta = WeakMap<any, any>,\n>(getComparatorOptions: GetComparatorOptions<Meta>): EqualityComparator<Meta> {\n  const comparator = createComparator<Meta>(\n    merge(\n      DEFAULT_CIRCULAR_CONFIG,\n      getComparatorOptions(DEFAULT_CIRCULAR_CONFIG as any),\n    ),\n  );\n\n  return ((a: any, b: any, meta: any = new WeakMap()) =>\n    comparator(a, b, meta)) as EqualityComparator<Meta>;\n}\n", "function r(e){var o,t,f=\"\";if(\"string\"==typeof e||\"number\"==typeof e)f+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var n=e.length;for(o=0;o<n;o++)e[o]&&(t=r(e[o]))&&(f&&(f+=\" \"),f+=t)}else for(t in e)e[t]&&(f&&(f+=\" \"),f+=t);return f}function e(){for(var e,o,t=0,f=\"\",n=arguments.length;t<n;t++)(e=arguments[t])&&(o=r(e))&&(f&&(f+=\" \"),f+=o);return f}module.exports=e,module.exports.clsx=e;", "// this file was prevaled\nmodule.exports = function fastRGLPropsEqual(a, b, isEqualImpl) {\n  if (a === b) return true;\n  return a.className === b.className && isEqualImpl(a.style, b.style) && a.width === b.width && a.autoSize === b.autoSize && a.cols === b.cols && a.draggableCancel === b.draggableCancel && a.draggableHandle === b.draggableHandle && isEqualImpl(a.verticalCompact, b.verticalCompact) && isEqualImpl(a.compactType, b.compactType) && isEqualImpl(a.layout, b.layout) && isEqualImpl(a.margin, b.margin) && isEqualImpl(a.containerPadding, b.containerPadding) && a.rowHeight === b.rowHeight && a.maxRows === b.maxRows && a.isBounded === b.isBounded && a.isDraggable === b.isDraggable && a.isResizable === b.isResizable && a.allowOverlap === b.allowOverlap && a.preventCollision === b.preventCollision && a.useCSSTransforms === b.useCSSTransforms && a.transformScale === b.transformScale && a.isDroppable === b.isDroppable && isEqualImpl(a.resizeHandles, b.resizeHandles) && isEqualImpl(a.resizeHandle, b.resizeHandle) && a.onLayoutChange === b.onLayoutChange && a.onDragStart === b.onDragStart && a.onDrag === b.onDrag && a.onDragStop === b.onDragStop && a.onResizeStart === b.onResizeStart && a.onResize === b.onResize && a.onResizeStop === b.onResizeStop && a.onDrop === b.onDrop && isEqualImpl(a.droppingItem, b.droppingItem) && isEqualImpl(a.innerRef, b.innerRef);\n};", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.bottom = bottom;\nexports.childrenEqual = childrenEqual;\nexports.cloneLayout = cloneLayout;\nexports.cloneLayoutItem = cloneLayoutItem;\nexports.collides = collides;\nexports.compact = compact;\nexports.compactItem = compactItem;\nexports.compactType = compactType;\nexports.correctBounds = correctBounds;\nexports.fastPositionEqual = fastPositionEqual;\nexports.fastRGLPropsEqual = void 0;\nexports.getAllCollisions = getAllCollisions;\nexports.getFirstCollision = getFirstCollision;\nexports.getLayoutItem = getLayoutItem;\nexports.getStatics = getStatics;\nexports.modifyLayout = modifyLayout;\nexports.moveElement = moveElement;\nexports.moveElementAwayFromCollision = moveElementAwayFromCollision;\nexports.noop = void 0;\nexports.perc = perc;\nexports.resizeItemInDirection = resizeItemInDirection;\nexports.setTopLeft = setTopLeft;\nexports.setTransform = setTransform;\nexports.sortLayoutItems = sortLayoutItems;\nexports.sortLayoutItemsByColRow = sortLayoutItemsByColRow;\nexports.sortLayoutItemsByRowCol = sortLayoutItemsByRowCol;\nexports.synchronizeLayoutWithChildren = synchronizeLayoutWithChildren;\nexports.validateLayout = validateLayout;\nexports.withLayoutItem = withLayoutItem;\nvar _fastEquals = require(\"fast-equals\");\nvar _react = _interopRequireDefault(require(\"react\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n/*:: import type {\n  ChildrenArray as ReactChildrenArray,\n  Element as ReactElement\n} from \"react\";*/\n/*:: export type ResizeHandleAxis =\n  | \"s\"\n  | \"w\"\n  | \"e\"\n  | \"n\"\n  | \"sw\"\n  | \"nw\"\n  | \"se\"\n  | \"ne\";*/\n/*:: export type LayoutItem = {\n  w: number,\n  h: number,\n  x: number,\n  y: number,\n  i: string,\n  minW?: number,\n  minH?: number,\n  maxW?: number,\n  maxH?: number,\n  moved?: boolean,\n  static?: boolean,\n  isDraggable?: ?boolean,\n  isResizable?: ?boolean,\n  resizeHandles?: Array<ResizeHandleAxis>,\n  isBounded?: ?boolean\n};*/\n/*:: export type Layout = $ReadOnlyArray<LayoutItem>;*/\n/*:: export type Position = {\n  left: number,\n  top: number,\n  width: number,\n  height: number\n};*/\n/*:: export type ReactDraggableCallbackData = {\n  node: HTMLElement,\n  x?: number,\n  y?: number,\n  deltaX: number,\n  deltaY: number,\n  lastX?: number,\n  lastY?: number\n};*/\n/*:: export type PartialPosition = { left: number, top: number };*/\n/*:: export type DroppingPosition = { left: number, top: number, e: Event };*/\n/*:: export type Size = { width: number, height: number };*/\n/*:: export type GridDragEvent = {\n  e: Event,\n  node: HTMLElement,\n  newPosition: PartialPosition\n};*/\n/*:: export type GridResizeEvent = {\n  e: Event,\n  node: HTMLElement,\n  size: Size,\n  handle: string\n};*/\n/*:: export type DragOverEvent = MouseEvent & {\n  nativeEvent: {\n    layerX: number,\n    layerY: number,\n    ...Event\n  }\n};*/\n/*:: export type Pick<FromType, Properties: { [string]: 0 }> = $Exact<\n  $ObjMapi<Properties, <K, V>(k: K, v: V) => $ElementType<FromType, K>>\n>;*/\n// Helpful port from TS\n/*:: type REl = ReactElement<any>;*/\n/*:: export type ReactChildren = ReactChildrenArray<REl>;*/\n/*:: export type EventCallback = (\n  Layout,\n  oldItem: ?LayoutItem,\n  newItem: ?LayoutItem,\n  placeholder: ?LayoutItem,\n  Event,\n  ?HTMLElement\n) => void;*/\n// All callbacks are of the signature (layout, oldItem, newItem, placeholder, e).\n/*:: export type CompactType = ?(\"horizontal\" | \"vertical\");*/\nconst isProduction = process.env.NODE_ENV === \"production\";\nconst DEBUG = false;\n\n/**\n * Return the bottom coordinate of the layout.\n *\n * @param  {Array} layout Layout array.\n * @return {Number}       Bottom coordinate.\n */\nfunction bottom(layout /*: Layout*/) /*: number*/{\n  let max = 0,\n    bottomY;\n  for (let i = 0, len = layout.length; i < len; i++) {\n    bottomY = layout[i].y + layout[i].h;\n    if (bottomY > max) max = bottomY;\n  }\n  return max;\n}\nfunction cloneLayout(layout /*: Layout*/) /*: Layout*/{\n  const newLayout = Array(layout.length);\n  for (let i = 0, len = layout.length; i < len; i++) {\n    newLayout[i] = cloneLayoutItem(layout[i]);\n  }\n  return newLayout;\n}\n\n// Modify a layoutItem inside a layout. Returns a new Layout,\n// does not mutate. Carries over all other LayoutItems unmodified.\nfunction modifyLayout(layout /*: Layout*/, layoutItem /*: LayoutItem*/) /*: Layout*/{\n  const newLayout = Array(layout.length);\n  for (let i = 0, len = layout.length; i < len; i++) {\n    if (layoutItem.i === layout[i].i) {\n      newLayout[i] = layoutItem;\n    } else {\n      newLayout[i] = layout[i];\n    }\n  }\n  return newLayout;\n}\n\n// Function to be called to modify a layout item.\n// Does defensive clones to ensure the layout is not modified.\nfunction withLayoutItem(layout /*: Layout*/, itemKey /*: string*/, cb /*: LayoutItem => LayoutItem*/) /*: [Layout, ?LayoutItem]*/{\n  let item = getLayoutItem(layout, itemKey);\n  if (!item) return [layout, null];\n  item = cb(cloneLayoutItem(item)); // defensive clone then modify\n  // FIXME could do this faster if we already knew the index\n  layout = modifyLayout(layout, item);\n  return [layout, item];\n}\n\n// Fast path to cloning, since this is monomorphic\nfunction cloneLayoutItem(layoutItem /*: LayoutItem*/) /*: LayoutItem*/{\n  return {\n    w: layoutItem.w,\n    h: layoutItem.h,\n    x: layoutItem.x,\n    y: layoutItem.y,\n    i: layoutItem.i,\n    minW: layoutItem.minW,\n    maxW: layoutItem.maxW,\n    minH: layoutItem.minH,\n    maxH: layoutItem.maxH,\n    moved: Boolean(layoutItem.moved),\n    static: Boolean(layoutItem.static),\n    // These can be null/undefined\n    isDraggable: layoutItem.isDraggable,\n    isResizable: layoutItem.isResizable,\n    resizeHandles: layoutItem.resizeHandles,\n    isBounded: layoutItem.isBounded\n  };\n}\n\n/**\n * Comparing React `children` is a bit difficult. This is a good way to compare them.\n * This will catch differences in keys, order, and length.\n */\nfunction childrenEqual(a /*: ReactChildren*/, b /*: ReactChildren*/) /*: boolean*/{\n  return (0, _fastEquals.deepEqual)(_react.default.Children.map(a, c => c?.key), _react.default.Children.map(b, c => c?.key)) && (0, _fastEquals.deepEqual)(_react.default.Children.map(a, c => c?.props[\"data-grid\"]), _react.default.Children.map(b, c => c?.props[\"data-grid\"]));\n}\n\n/**\n * See `fastRGLPropsEqual.js`.\n * We want this to run as fast as possible - it is called often - and to be\n * resilient to new props that we add. So rather than call lodash.isEqual,\n * which isn't suited to comparing props very well, we use this specialized\n * function in conjunction with preval to generate the fastest possible comparison\n * function, tuned for exactly our props.\n */\n/*:: type FastRGLPropsEqual = (Object, Object, Function) => boolean;*/\nconst fastRGLPropsEqual /*: FastRGLPropsEqual*/ = exports.fastRGLPropsEqual = require(\"./fastRGLPropsEqual\");\n\n// Like the above, but a lot simpler.\nfunction fastPositionEqual(a /*: Position*/, b /*: Position*/) /*: boolean*/{\n  return a.left === b.left && a.top === b.top && a.width === b.width && a.height === b.height;\n}\n\n/**\n * Given two layoutitems, check if they collide.\n */\nfunction collides(l1 /*: LayoutItem*/, l2 /*: LayoutItem*/) /*: boolean*/{\n  if (l1.i === l2.i) return false; // same element\n  if (l1.x + l1.w <= l2.x) return false; // l1 is left of l2\n  if (l1.x >= l2.x + l2.w) return false; // l1 is right of l2\n  if (l1.y + l1.h <= l2.y) return false; // l1 is above l2\n  if (l1.y >= l2.y + l2.h) return false; // l1 is below l2\n  return true; // boxes overlap\n}\n\n/**\n * Given a layout, compact it. This involves going down each y coordinate and removing gaps\n * between items.\n *\n * Does not modify layout items (clones). Creates a new layout array.\n *\n * @param  {Array} layout Layout.\n * @param  {Boolean} verticalCompact Whether or not to compact the layout\n *   vertically.\n * @param  {Boolean} allowOverlap When `true`, allows overlapping grid items.\n * @return {Array}       Compacted Layout.\n */\nfunction compact(layout /*: Layout*/, compactType /*: CompactType*/, cols /*: number*/, allowOverlap /*: ?boolean*/) /*: Layout*/{\n  // Statics go in the compareWith array right away so items flow around them.\n  const compareWith = getStatics(layout);\n  // We go through the items by row and column.\n  const sorted = sortLayoutItems(layout, compactType);\n  // Holding for new items.\n  const out = Array(layout.length);\n  for (let i = 0, len = sorted.length; i < len; i++) {\n    let l = cloneLayoutItem(sorted[i]);\n\n    // Don't move static elements\n    if (!l.static) {\n      l = compactItem(compareWith, l, compactType, cols, sorted, allowOverlap);\n\n      // Add to comparison array. We only collide with items before this one.\n      // Statics are already in this array.\n      compareWith.push(l);\n    }\n\n    // Add to output array to make sure they still come out in the right order.\n    out[layout.indexOf(sorted[i])] = l;\n\n    // Clear moved flag, if it exists.\n    l.moved = false;\n  }\n  return out;\n}\nconst heightWidth = {\n  x: \"w\",\n  y: \"h\"\n};\n/**\n * Before moving item down, it will check if the movement will cause collisions and move those items down before.\n */\nfunction resolveCompactionCollision(layout /*: Layout*/, item /*: LayoutItem*/, moveToCoord /*: number*/, axis /*: \"x\" | \"y\"*/) {\n  const sizeProp = heightWidth[axis];\n  item[axis] += 1;\n  const itemIndex = layout.map(layoutItem => {\n    return layoutItem.i;\n  }).indexOf(item.i);\n\n  // Go through each item we collide with.\n  for (let i = itemIndex + 1; i < layout.length; i++) {\n    const otherItem = layout[i];\n    // Ignore static items\n    if (otherItem.static) continue;\n\n    // Optimization: we can break early if we know we're past this el\n    // We can do this b/c it's a sorted layout\n    if (otherItem.y > item.y + item.h) break;\n    if (collides(item, otherItem)) {\n      resolveCompactionCollision(layout, otherItem, moveToCoord + item[sizeProp], axis);\n    }\n  }\n  item[axis] = moveToCoord;\n}\n\n/**\n * Compact an item in the layout.\n *\n * Modifies item.\n *\n */\nfunction compactItem(compareWith /*: Layout*/, l /*: LayoutItem*/, compactType /*: CompactType*/, cols /*: number*/, fullLayout /*: Layout*/, allowOverlap /*: ?boolean*/) /*: LayoutItem*/{\n  const compactV = compactType === \"vertical\";\n  const compactH = compactType === \"horizontal\";\n  if (compactV) {\n    // Bottom 'y' possible is the bottom of the layout.\n    // This allows you to do nice stuff like specify {y: Infinity}\n    // This is here because the layout must be sorted in order to get the correct bottom `y`.\n    l.y = Math.min(bottom(compareWith), l.y);\n    // Move the element up as far as it can go without colliding.\n    while (l.y > 0 && !getFirstCollision(compareWith, l)) {\n      l.y--;\n    }\n  } else if (compactH) {\n    // Move the element left as far as it can go without colliding.\n    while (l.x > 0 && !getFirstCollision(compareWith, l)) {\n      l.x--;\n    }\n  }\n\n  // Move it down, and keep moving it down if it's colliding.\n  let collides;\n  // Checking the compactType null value to avoid breaking the layout when overlapping is allowed.\n  while ((collides = getFirstCollision(compareWith, l)) && !(compactType === null && allowOverlap)) {\n    if (compactH) {\n      resolveCompactionCollision(fullLayout, l, collides.x + collides.w, \"x\");\n    } else {\n      resolveCompactionCollision(fullLayout, l, collides.y + collides.h, \"y\");\n    }\n    // Since we can't grow without bounds horizontally, if we've overflown, let's move it down and try again.\n    if (compactH && l.x + l.w > cols) {\n      l.x = cols - l.w;\n      l.y++;\n      // ALso move element as left as we can\n      while (l.x > 0 && !getFirstCollision(compareWith, l)) {\n        l.x--;\n      }\n    }\n  }\n\n  // Ensure that there are no negative positions\n  l.y = Math.max(l.y, 0);\n  l.x = Math.max(l.x, 0);\n  return l;\n}\n\n/**\n * Given a layout, make sure all elements fit within its bounds.\n *\n * Modifies layout items.\n *\n * @param  {Array} layout Layout array.\n * @param  {Number} bounds Number of columns.\n */\nfunction correctBounds(layout /*: Layout*/, bounds /*: { cols: number }*/) /*: Layout*/{\n  const collidesWith = getStatics(layout);\n  for (let i = 0, len = layout.length; i < len; i++) {\n    const l = layout[i];\n    // Overflows right\n    if (l.x + l.w > bounds.cols) l.x = bounds.cols - l.w;\n    // Overflows left\n    if (l.x < 0) {\n      l.x = 0;\n      l.w = bounds.cols;\n    }\n    if (!l.static) collidesWith.push(l);else {\n      // If this is static and collides with other statics, we must move it down.\n      // We have to do something nicer than just letting them overlap.\n      while (getFirstCollision(collidesWith, l)) {\n        l.y++;\n      }\n    }\n  }\n  return layout;\n}\n\n/**\n * Get a layout item by ID. Used so we can override later on if necessary.\n *\n * @param  {Array}  layout Layout array.\n * @param  {String} id     ID\n * @return {LayoutItem}    Item at ID.\n */\nfunction getLayoutItem(layout /*: Layout*/, id /*: string*/) /*: ?LayoutItem*/{\n  for (let i = 0, len = layout.length; i < len; i++) {\n    if (layout[i].i === id) return layout[i];\n  }\n}\n\n/**\n * Returns the first item this layout collides with.\n * It doesn't appear to matter which order we approach this from, although\n * perhaps that is the wrong thing to do.\n *\n * @param  {Object} layoutItem Layout item.\n * @return {Object|undefined}  A colliding layout item, or undefined.\n */\nfunction getFirstCollision(layout /*: Layout*/, layoutItem /*: LayoutItem*/) /*: ?LayoutItem*/{\n  for (let i = 0, len = layout.length; i < len; i++) {\n    if (collides(layout[i], layoutItem)) return layout[i];\n  }\n}\nfunction getAllCollisions(layout /*: Layout*/, layoutItem /*: LayoutItem*/) /*: Array<LayoutItem>*/{\n  return layout.filter(l => collides(l, layoutItem));\n}\n\n/**\n * Get all static elements.\n * @param  {Array} layout Array of layout objects.\n * @return {Array}        Array of static layout items..\n */\nfunction getStatics(layout /*: Layout*/) /*: Array<LayoutItem>*/{\n  return layout.filter(l => l.static);\n}\n\n/**\n * Move an element. Responsible for doing cascading movements of other elements.\n *\n * Modifies layout items.\n *\n * @param  {Array}      layout            Full layout to modify.\n * @param  {LayoutItem} l                 element to move.\n * @param  {Number}     [x]               X position in grid units.\n * @param  {Number}     [y]               Y position in grid units.\n */\nfunction moveElement(layout /*: Layout*/, l /*: LayoutItem*/, x /*: ?number*/, y /*: ?number*/, isUserAction /*: ?boolean*/, preventCollision /*: ?boolean*/, compactType /*: CompactType*/, cols /*: number*/, allowOverlap /*: ?boolean*/) /*: Layout*/{\n  // If this is static and not explicitly enabled as draggable,\n  // no move is possible, so we can short-circuit this immediately.\n  if (l.static && l.isDraggable !== true) return layout;\n\n  // Short-circuit if nothing to do.\n  if (l.y === y && l.x === x) return layout;\n  log(`Moving element ${l.i} to [${String(x)},${String(y)}] from [${l.x},${l.y}]`);\n  const oldX = l.x;\n  const oldY = l.y;\n\n  // This is quite a bit faster than extending the object\n  if (typeof x === \"number\") l.x = x;\n  if (typeof y === \"number\") l.y = y;\n  l.moved = true;\n\n  // If this collides with anything, move it.\n  // When doing this comparison, we have to sort the items we compare with\n  // to ensure, in the case of multiple collisions, that we're getting the\n  // nearest collision.\n  let sorted = sortLayoutItems(layout, compactType);\n  const movingUp = compactType === \"vertical\" && typeof y === \"number\" ? oldY >= y : compactType === \"horizontal\" && typeof x === \"number\" ? oldX >= x : false;\n  // $FlowIgnore acceptable modification of read-only array as it was recently cloned\n  if (movingUp) sorted = sorted.reverse();\n  const collisions = getAllCollisions(sorted, l);\n  const hasCollisions = collisions.length > 0;\n\n  // We may have collisions. We can short-circuit if we've turned off collisions or\n  // allowed overlap.\n  if (hasCollisions && allowOverlap) {\n    // Easy, we don't need to resolve collisions. But we *did* change the layout,\n    // so clone it on the way out.\n    return cloneLayout(layout);\n  } else if (hasCollisions && preventCollision) {\n    // If we are preventing collision but not allowing overlap, we need to\n    // revert the position of this element so it goes to where it came from, rather\n    // than the user's desired location.\n    log(`Collision prevented on ${l.i}, reverting.`);\n    l.x = oldX;\n    l.y = oldY;\n    l.moved = false;\n    return layout; // did not change so don't clone\n  }\n\n  // Move each item that collides away from this element.\n  for (let i = 0, len = collisions.length; i < len; i++) {\n    const collision = collisions[i];\n    log(`Resolving collision between ${l.i} at [${l.x},${l.y}] and ${collision.i} at [${collision.x},${collision.y}]`);\n\n    // Short circuit so we can't infinite loop\n    if (collision.moved) continue;\n\n    // Don't move static items - we have to move *this* element away\n    if (collision.static) {\n      layout = moveElementAwayFromCollision(layout, collision, l, isUserAction, compactType, cols);\n    } else {\n      layout = moveElementAwayFromCollision(layout, l, collision, isUserAction, compactType, cols);\n    }\n  }\n  return layout;\n}\n\n/**\n * This is where the magic needs to happen - given a collision, move an element away from the collision.\n * We attempt to move it up if there's room, otherwise it goes below.\n *\n * @param  {Array} layout            Full layout to modify.\n * @param  {LayoutItem} collidesWith Layout item we're colliding with.\n * @param  {LayoutItem} itemToMove   Layout item we're moving.\n */\nfunction moveElementAwayFromCollision(layout /*: Layout*/, collidesWith /*: LayoutItem*/, itemToMove /*: LayoutItem*/, isUserAction /*: ?boolean*/, compactType /*: CompactType*/, cols /*: number*/) /*: Layout*/{\n  const compactH = compactType === \"horizontal\";\n  // Compact vertically if not set to horizontal\n  const compactV = compactType === \"vertical\";\n  const preventCollision = collidesWith.static; // we're already colliding (not for static items)\n\n  // If there is enough space above the collision to put this element, move it there.\n  // We only do this on the main collision as this can get funky in cascades and cause\n  // unwanted swapping behavior.\n  if (isUserAction) {\n    // Reset isUserAction flag because we're not in the main collision anymore.\n    isUserAction = false;\n\n    // Make a mock item so we don't modify the item here, only modify in moveElement.\n    const fakeItem /*: LayoutItem*/ = {\n      x: compactH ? Math.max(collidesWith.x - itemToMove.w, 0) : itemToMove.x,\n      y: compactV ? Math.max(collidesWith.y - itemToMove.h, 0) : itemToMove.y,\n      w: itemToMove.w,\n      h: itemToMove.h,\n      i: \"-1\"\n    };\n    const firstCollision = getFirstCollision(layout, fakeItem);\n    const collisionNorth = firstCollision && firstCollision.y + firstCollision.h > collidesWith.y;\n    const collisionWest = firstCollision && collidesWith.x + collidesWith.w > firstCollision.x;\n\n    // No collision? If so, we can go up there; otherwise, we'll end up moving down as normal\n    if (!firstCollision) {\n      log(`Doing reverse collision on ${itemToMove.i} up to [${fakeItem.x},${fakeItem.y}].`);\n      return moveElement(layout, itemToMove, compactH ? fakeItem.x : undefined, compactV ? fakeItem.y : undefined, isUserAction, preventCollision, compactType, cols);\n    } else if (collisionNorth && compactV) {\n      return moveElement(layout, itemToMove, undefined, collidesWith.y + 1, isUserAction, preventCollision, compactType, cols);\n    } else if (collisionNorth && compactType == null) {\n      collidesWith.y = itemToMove.y;\n      itemToMove.y = itemToMove.y + itemToMove.h;\n      return layout;\n    } else if (collisionWest && compactH) {\n      return moveElement(layout, collidesWith, itemToMove.x, undefined, isUserAction, preventCollision, compactType, cols);\n    }\n  }\n  const newX = compactH ? itemToMove.x + 1 : undefined;\n  const newY = compactV ? itemToMove.y + 1 : undefined;\n  if (newX == null && newY == null) {\n    return layout;\n  }\n  return moveElement(layout, itemToMove, compactH ? itemToMove.x + 1 : undefined, compactV ? itemToMove.y + 1 : undefined, isUserAction, preventCollision, compactType, cols);\n}\n\n/**\n * Helper to convert a number to a percentage string.\n *\n * @param  {Number} num Any number\n * @return {String}     That number as a percentage.\n */\nfunction perc(num /*: number*/) /*: string*/{\n  return num * 100 + \"%\";\n}\n\n/**\n * Helper functions to constrain dimensions of a GridItem\n */\nconst constrainWidth = (left /*: number*/, currentWidth /*: number*/, newWidth /*: number*/, containerWidth /*: number*/) => {\n  return left + newWidth > containerWidth ? currentWidth : newWidth;\n};\nconst constrainHeight = (top /*: number*/, currentHeight /*: number*/, newHeight /*: number*/) => {\n  return top < 0 ? currentHeight : newHeight;\n};\nconst constrainLeft = (left /*: number*/) => Math.max(0, left);\nconst constrainTop = (top /*: number*/) => Math.max(0, top);\nconst resizeNorth = (currentSize, _ref, _containerWidth) => {\n  let {\n    left,\n    height,\n    width\n  } = _ref;\n  const top = currentSize.top - (height - currentSize.height);\n  return {\n    left,\n    width,\n    height: constrainHeight(top, currentSize.height, height),\n    top: constrainTop(top)\n  };\n};\nconst resizeEast = (currentSize, _ref2, containerWidth) => {\n  let {\n    top,\n    left,\n    height,\n    width\n  } = _ref2;\n  return {\n    top,\n    height,\n    width: constrainWidth(currentSize.left, currentSize.width, width, containerWidth),\n    left: constrainLeft(left)\n  };\n};\nconst resizeWest = (currentSize, _ref3, containerWidth) => {\n  let {\n    top,\n    height,\n    width\n  } = _ref3;\n  const left = currentSize.left - (width - currentSize.width);\n  return {\n    height,\n    width: left < 0 ? currentSize.width : constrainWidth(currentSize.left, currentSize.width, width, containerWidth),\n    top: constrainTop(top),\n    left: constrainLeft(left)\n  };\n};\nconst resizeSouth = (currentSize, _ref4, containerWidth) => {\n  let {\n    top,\n    left,\n    height,\n    width\n  } = _ref4;\n  return {\n    width,\n    left,\n    height: constrainHeight(top, currentSize.height, height),\n    top: constrainTop(top)\n  };\n};\nconst resizeNorthEast = function () {\n  return resizeNorth(arguments.length <= 0 ? undefined : arguments[0], resizeEast(...arguments), arguments.length <= 2 ? undefined : arguments[2]);\n};\nconst resizeNorthWest = function () {\n  return resizeNorth(arguments.length <= 0 ? undefined : arguments[0], resizeWest(...arguments), arguments.length <= 2 ? undefined : arguments[2]);\n};\nconst resizeSouthEast = function () {\n  return resizeSouth(arguments.length <= 0 ? undefined : arguments[0], resizeEast(...arguments), arguments.length <= 2 ? undefined : arguments[2]);\n};\nconst resizeSouthWest = function () {\n  return resizeSouth(arguments.length <= 0 ? undefined : arguments[0], resizeWest(...arguments), arguments.length <= 2 ? undefined : arguments[2]);\n};\nconst ordinalResizeHandlerMap = {\n  n: resizeNorth,\n  ne: resizeNorthEast,\n  e: resizeEast,\n  se: resizeSouthEast,\n  s: resizeSouth,\n  sw: resizeSouthWest,\n  w: resizeWest,\n  nw: resizeNorthWest\n};\n\n/**\n * Helper for clamping width and position when resizing an item.\n */\nfunction resizeItemInDirection(direction /*: ResizeHandleAxis*/, currentSize /*: Position*/, newSize /*: Position*/, containerWidth /*: number*/) /*: Position*/{\n  const ordinalHandler = ordinalResizeHandlerMap[direction];\n  // Shouldn't be possible given types; that said, don't fail hard\n  if (!ordinalHandler) return newSize;\n  return ordinalHandler(currentSize, {\n    ...currentSize,\n    ...newSize\n  }, containerWidth);\n}\nfunction setTransform(_ref5 /*:: */) /*: Object*/{\n  let {\n    top,\n    left,\n    width,\n    height\n  } /*: Position*/ = _ref5 /*: Position*/;\n  // Replace unitless items with px\n  const translate = `translate(${left}px,${top}px)`;\n  return {\n    transform: translate,\n    WebkitTransform: translate,\n    MozTransform: translate,\n    msTransform: translate,\n    OTransform: translate,\n    width: `${width}px`,\n    height: `${height}px`,\n    position: \"absolute\"\n  };\n}\nfunction setTopLeft(_ref6 /*:: */) /*: Object*/{\n  let {\n    top,\n    left,\n    width,\n    height\n  } /*: Position*/ = _ref6 /*: Position*/;\n  return {\n    top: `${top}px`,\n    left: `${left}px`,\n    width: `${width}px`,\n    height: `${height}px`,\n    position: \"absolute\"\n  };\n}\n\n/**\n * Get layout items sorted from top left to right and down.\n *\n * @return {Array} Array of layout objects.\n * @return {Array}        Layout, sorted static items first.\n */\nfunction sortLayoutItems(layout /*: Layout*/, compactType /*: CompactType*/) /*: Layout*/{\n  if (compactType === \"horizontal\") return sortLayoutItemsByColRow(layout);\n  if (compactType === \"vertical\") return sortLayoutItemsByRowCol(layout);else return layout;\n}\n\n/**\n * Sort layout items by row ascending and column ascending.\n *\n * Does not modify Layout.\n */\nfunction sortLayoutItemsByRowCol(layout /*: Layout*/) /*: Layout*/{\n  // Slice to clone array as sort modifies\n  return layout.slice(0).sort(function (a, b) {\n    if (a.y > b.y || a.y === b.y && a.x > b.x) {\n      return 1;\n    } else if (a.y === b.y && a.x === b.x) {\n      // Without this, we can get different sort results in IE vs. Chrome/FF\n      return 0;\n    }\n    return -1;\n  });\n}\n\n/**\n * Sort layout items by column ascending then row ascending.\n *\n * Does not modify Layout.\n */\nfunction sortLayoutItemsByColRow(layout /*: Layout*/) /*: Layout*/{\n  return layout.slice(0).sort(function (a, b) {\n    if (a.x > b.x || a.x === b.x && a.y > b.y) {\n      return 1;\n    }\n    return -1;\n  });\n}\n\n/**\n * Generate a layout using the initialLayout and children as a template.\n * Missing entries will be added, extraneous ones will be truncated.\n *\n * Does not modify initialLayout.\n *\n * @param  {Array}  initialLayout Layout passed in through props.\n * @param  {String} breakpoint    Current responsive breakpoint.\n * @param  {?String} compact      Compaction option.\n * @return {Array}                Working layout.\n */\nfunction synchronizeLayoutWithChildren(initialLayout /*: Layout*/, children /*: ReactChildren*/, cols /*: number*/, compactType /*: CompactType*/, allowOverlap /*: ?boolean*/) /*: Layout*/{\n  initialLayout = initialLayout || [];\n\n  // Generate one layout item per child.\n  const layout /*: LayoutItem[]*/ = [];\n  _react.default.Children.forEach(children, (child /*: ReactElement<any>*/) => {\n    // Child may not exist\n    if (child?.key == null) return;\n    const exists = getLayoutItem(initialLayout, String(child.key));\n    const g = child.props[\"data-grid\"];\n    // Don't overwrite the layout item if it's already in the initial layout.\n    // If it has a `data-grid` property, prefer that over what's in the layout.\n    if (exists && g == null) {\n      layout.push(cloneLayoutItem(exists));\n    } else {\n      // Hey, this item has a data-grid property, use it.\n      if (g) {\n        if (!isProduction) {\n          validateLayout([g], \"ReactGridLayout.children\");\n        }\n        // FIXME clone not really necessary here\n        layout.push(cloneLayoutItem({\n          ...g,\n          i: child.key\n        }));\n      } else {\n        // Nothing provided: ensure this is added to the bottom\n        // FIXME clone not really necessary here\n        layout.push(cloneLayoutItem({\n          w: 1,\n          h: 1,\n          x: 0,\n          y: bottom(layout),\n          i: String(child.key)\n        }));\n      }\n    }\n  });\n\n  // Correct the layout.\n  const correctedLayout = correctBounds(layout, {\n    cols: cols\n  });\n  return allowOverlap ? correctedLayout : compact(correctedLayout, compactType, cols);\n}\n\n/**\n * Validate a layout. Throws errors.\n *\n * @param  {Array}  layout        Array of layout items.\n * @param  {String} [contextName] Context name for errors.\n * @throw  {Error}                Validation error.\n */\nfunction validateLayout(layout /*: Layout*/) /*: void*/{\n  let contextName /*: string*/ = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"Layout\";\n  const subProps = [\"x\", \"y\", \"w\", \"h\"];\n  if (!Array.isArray(layout)) throw new Error(contextName + \" must be an array!\");\n  for (let i = 0, len = layout.length; i < len; i++) {\n    const item = layout[i];\n    for (let j = 0; j < subProps.length; j++) {\n      const key = subProps[j];\n      const value = item[key];\n      if (typeof value !== \"number\" || Number.isNaN(value)) {\n        throw new Error(`ReactGridLayout: ${contextName}[${i}].${key} must be a number! Received: ${value} (${typeof value})`);\n      }\n    }\n    if (typeof item.i !== \"undefined\" && typeof item.i !== \"string\") {\n      throw new Error(`ReactGridLayout: ${contextName}[${i}].i must be a string! Received: ${item.i} (${typeof item.i})`);\n    }\n  }\n}\n\n// Legacy support for verticalCompact: false\nfunction compactType(props /*: ?{ verticalCompact: boolean, compactType: CompactType }*/) /*: CompactType*/{\n  const {\n    verticalCompact,\n    compactType\n  } = props || {};\n  return verticalCompact === false ? null : compactType;\n}\nfunction log() {\n  if (!DEBUG) return;\n  // eslint-disable-next-line no-console\n  console.log(...arguments);\n}\nconst noop = () => {};\nexports.noop = noop;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.calcGridColWidth = calcGridColWidth;\nexports.calcGridItemPosition = calcGridItemPosition;\nexports.calcGridItemWHPx = calcGridItemWHPx;\nexports.calcWH = calcWH;\nexports.calcXY = calcXY;\nexports.clamp = clamp;\n/*:: import type { Position } from \"./utils\";*/\n/*:: export type PositionParams = {\n  margin: [number, number],\n  containerPadding: [number, number],\n  containerWidth: number,\n  cols: number,\n  rowHeight: number,\n  maxRows: number\n};*/\n// Helper for generating column width\nfunction calcGridColWidth(positionParams /*: PositionParams*/) /*: number*/{\n  const {\n    margin,\n    containerPadding,\n    containerWidth,\n    cols\n  } = positionParams;\n  return (containerWidth - margin[0] * (cols - 1) - containerPadding[0] * 2) / cols;\n}\n\n// This can either be called:\n// calcGridItemWHPx(w, colWidth, margin[0])\n// or\n// calcGridItemWHPx(h, rowHeight, margin[1])\nfunction calcGridItemWHPx(gridUnits /*: number*/, colOrRowSize /*: number*/, marginPx /*: number*/) /*: number*/{\n  // 0 * Infinity === NaN, which causes problems with resize contraints\n  if (!Number.isFinite(gridUnits)) return gridUnits;\n  return Math.round(colOrRowSize * gridUnits + Math.max(0, gridUnits - 1) * marginPx);\n}\n\n/**\n * Return position on the page given an x, y, w, h.\n * left, top, width, height are all in pixels.\n * @param  {PositionParams} positionParams  Parameters of grid needed for coordinates calculations.\n * @param  {Number}  x                      X coordinate in grid units.\n * @param  {Number}  y                      Y coordinate in grid units.\n * @param  {Number}  w                      W coordinate in grid units.\n * @param  {Number}  h                      H coordinate in grid units.\n * @return {Position}                       Object containing coords.\n */\nfunction calcGridItemPosition(positionParams /*: PositionParams*/, x /*: number*/, y /*: number*/, w /*: number*/, h /*: number*/, state /*: ?Object*/) /*: Position*/{\n  const {\n    margin,\n    containerPadding,\n    rowHeight\n  } = positionParams;\n  const colWidth = calcGridColWidth(positionParams);\n  const out = {};\n\n  // If resizing, use the exact width and height as returned from resizing callbacks.\n  if (state && state.resizing) {\n    out.width = Math.round(state.resizing.width);\n    out.height = Math.round(state.resizing.height);\n  }\n  // Otherwise, calculate from grid units.\n  else {\n    out.width = calcGridItemWHPx(w, colWidth, margin[0]);\n    out.height = calcGridItemWHPx(h, rowHeight, margin[1]);\n  }\n\n  // If dragging, use the exact width and height as returned from dragging callbacks.\n  if (state && state.dragging) {\n    out.top = Math.round(state.dragging.top);\n    out.left = Math.round(state.dragging.left);\n  } else if (state && state.resizing && typeof state.resizing.top === \"number\" && typeof state.resizing.left === \"number\") {\n    out.top = Math.round(state.resizing.top);\n    out.left = Math.round(state.resizing.left);\n  }\n  // Otherwise, calculate from grid units.\n  else {\n    out.top = Math.round((rowHeight + margin[1]) * y + containerPadding[1]);\n    out.left = Math.round((colWidth + margin[0]) * x + containerPadding[0]);\n  }\n  return out;\n}\n\n/**\n * Translate x and y coordinates from pixels to grid units.\n * @param  {PositionParams} positionParams  Parameters of grid needed for coordinates calculations.\n * @param  {Number} top                     Top position (relative to parent) in pixels.\n * @param  {Number} left                    Left position (relative to parent) in pixels.\n * @param  {Number} w                       W coordinate in grid units.\n * @param  {Number} h                       H coordinate in grid units.\n * @return {Object}                         x and y in grid units.\n */\nfunction calcXY(positionParams /*: PositionParams*/, top /*: number*/, left /*: number*/, w /*: number*/, h /*: number*/) /*: { x: number, y: number }*/{\n  const {\n    margin,\n    containerPadding,\n    cols,\n    rowHeight,\n    maxRows\n  } = positionParams;\n  const colWidth = calcGridColWidth(positionParams);\n\n  // left = containerPaddingX + x * (colWidth + marginX)\n  // x * (colWidth + marginX) = left - containerPaddingX\n  // x = (left - containerPaddingX) / (colWidth + marginX)\n  let x = Math.round((left - containerPadding[0]) / (colWidth + margin[0]));\n  let y = Math.round((top - containerPadding[1]) / (rowHeight + margin[1]));\n\n  // Capping\n  x = clamp(x, 0, cols - w);\n  y = clamp(y, 0, maxRows - h);\n  return {\n    x,\n    y\n  };\n}\n\n/**\n * Given a height and width in pixel values, calculate grid units.\n * @param  {PositionParams} positionParams  Parameters of grid needed for coordinates calcluations.\n * @param  {Number} height                  Height in pixels.\n * @param  {Number} width                   Width in pixels.\n * @param  {Number} x                       X coordinate in grid units.\n * @param  {Number} y                       Y coordinate in grid units.\n * @param {String} handle Resize Handle.\n * @return {Object}                         w, h as grid units.\n */\nfunction calcWH(positionParams /*: PositionParams*/, width /*: number*/, height /*: number*/, x /*: number*/, y /*: number*/, handle /*: string*/) /*: { w: number, h: number }*/{\n  const {\n    margin,\n    maxRows,\n    cols,\n    rowHeight\n  } = positionParams;\n  const colWidth = calcGridColWidth(positionParams);\n\n  // width = colWidth * w - (margin * (w - 1))\n  // ...\n  // w = (width + margin) / (colWidth + margin)\n  let w = Math.round((width + margin[0]) / (colWidth + margin[0]));\n  let h = Math.round((height + margin[1]) / (rowHeight + margin[1]));\n\n  // Capping\n  let _w = clamp(w, 0, cols - x);\n  let _h = clamp(h, 0, maxRows - y);\n  if ([\"sw\", \"w\", \"nw\"].indexOf(handle) !== -1) {\n    _w = clamp(w, 0, cols);\n  }\n  if ([\"nw\", \"n\", \"ne\"].indexOf(handle) !== -1) {\n    _h = clamp(h, 0, maxRows);\n  }\n  return {\n    w: _w,\n    h: _h\n  };\n}\n\n// Similar to _.clamp\nfunction clamp(num /*: number*/, lowerBound /*: number*/, upperBound /*: number*/) /*: number*/{\n  return Math.max(Math.min(num, upperBound), lowerBound);\n}", "/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.dontSetMe = dontSetMe;\nexports.findInArray = findInArray;\nexports.int = int;\nexports.isFunction = isFunction;\nexports.isNum = isNum;\n// @credits https://gist.github.com/rogozhnikoff/a43cfed27c41e4e68cdc\nfunction findInArray(array /*: Array<any> | TouchList*/, callback /*: Function*/) /*: any*/{\n  for (let i = 0, length = array.length; i < length; i++) {\n    if (callback.apply(callback, [array[i], i, array])) return array[i];\n  }\n}\nfunction isFunction(func /*: any*/) /*: boolean %checks*/{\n  // $FlowIgnore[method-unbinding]\n  return typeof func === 'function' || Object.prototype.toString.call(func) === '[object Function]';\n}\nfunction isNum(num /*: any*/) /*: boolean %checks*/{\n  return typeof num === 'number' && !isNaN(num);\n}\nfunction int(a /*: string*/) /*: number*/{\n  return parseInt(a, 10);\n}\nfunction dontSetMe(props /*: Object*/, propName /*: string*/, componentName /*: string*/) /*: ?Error*/{\n  if (props[propName]) {\n    return new Error(`Invalid prop ${propName} passed to ${componentName} - do not set this, set it on the child.`);\n  }\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.browserPrefixToKey = browserPrefixToKey;\nexports.browserPrefixToStyle = browserPrefixToStyle;\nexports.default = void 0;\nexports.getPrefix = getPrefix;\nconst prefixes = ['Moz', 'Webkit', 'O', 'ms'];\nfunction getPrefix() /*: string*/{\n  let prop /*: string*/ = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'transform';\n  // Ensure we're running in an environment where there is actually a global\n  // `window` obj\n  if (typeof window === 'undefined') return '';\n\n  // If we're in a pseudo-browser server-side environment, this access\n  // path may not exist, so bail out if it doesn't.\n  const style = window.document?.documentElement?.style;\n  if (!style) return '';\n  if (prop in style) return '';\n  for (let i = 0; i < prefixes.length; i++) {\n    if (browserPrefixToKey(prop, prefixes[i]) in style) return prefixes[i];\n  }\n  return '';\n}\nfunction browserPrefixToKey(prop /*: string*/, prefix /*: string*/) /*: string*/{\n  return prefix ? `${prefix}${kebabToTitleCase(prop)}` : prop;\n}\nfunction browserPrefixToStyle(prop /*: string*/, prefix /*: string*/) /*: string*/{\n  return prefix ? `-${prefix.toLowerCase()}-${prop}` : prop;\n}\nfunction kebabToTitleCase(str /*: string*/) /*: string*/{\n  let out = '';\n  let shouldCapitalize = true;\n  for (let i = 0; i < str.length; i++) {\n    if (shouldCapitalize) {\n      out += str[i].toUpperCase();\n      shouldCapitalize = false;\n    } else if (str[i] === '-') {\n      shouldCapitalize = true;\n    } else {\n      out += str[i];\n    }\n  }\n  return out;\n}\n\n// Default export is the prefix itself, like 'Moz', 'Webkit', etc\n// Note that you may have to re-test for certain things; for instance, Chrome 50\n// can handle unprefixed `transform`, but not unprefixed `user-select`\nvar _default = exports.default = (getPrefix() /*: string*/);", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.addClassName = addClassName;\nexports.addEvent = addEvent;\nexports.addUserSelectStyles = addUserSelectStyles;\nexports.createCSSTransform = createCSSTransform;\nexports.createSVGTransform = createSVGTransform;\nexports.getTouch = getTouch;\nexports.getTouchIdentifier = getTouchIdentifier;\nexports.getTranslation = getTranslation;\nexports.innerHeight = innerHeight;\nexports.innerWidth = innerWidth;\nexports.matchesSelector = matchesSelector;\nexports.matchesSelectorAndParentsTo = matchesSelectorAndParentsTo;\nexports.offsetXYFromParent = offsetXYFromParent;\nexports.outerHeight = outerHeight;\nexports.outerWidth = outerWidth;\nexports.removeClassName = removeClassName;\nexports.removeEvent = removeEvent;\nexports.scheduleRemoveUserSelectStyles = scheduleRemoveUserSelectStyles;\nvar _shims = require(\"./shims\");\nvar _getPrefix = _interopRequireWildcard(require(\"./getPrefix\"));\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n/*:: import type {ControlPosition, PositionOffsetControlPosition, MouseTouchEvent} from './types';*/\nlet matchesSelectorFunc = '';\nfunction matchesSelector(el /*: Node*/, selector /*: string*/) /*: boolean*/{\n  if (!matchesSelectorFunc) {\n    matchesSelectorFunc = (0, _shims.findInArray)(['matches', 'webkitMatchesSelector', 'mozMatchesSelector', 'msMatchesSelector', 'oMatchesSelector'], function (method) {\n      // $FlowIgnore: Doesn't think elements are indexable\n      return (0, _shims.isFunction)(el[method]);\n    });\n  }\n\n  // Might not be found entirely (not an Element?) - in that case, bail\n  // $FlowIgnore: Doesn't think elements are indexable\n  if (!(0, _shims.isFunction)(el[matchesSelectorFunc])) return false;\n\n  // $FlowIgnore: Doesn't think elements are indexable\n  return el[matchesSelectorFunc](selector);\n}\n\n// Works up the tree to the draggable itself attempting to match selector.\nfunction matchesSelectorAndParentsTo(el /*: Node*/, selector /*: string*/, baseNode /*: Node*/) /*: boolean*/{\n  let node = el;\n  do {\n    if (matchesSelector(node, selector)) return true;\n    if (node === baseNode) return false;\n    // $FlowIgnore[incompatible-type]\n    node = node.parentNode;\n  } while (node);\n  return false;\n}\nfunction addEvent(el /*: ?Node*/, event /*: string*/, handler /*: Function*/, inputOptions /*: Object*/) /*: void*/{\n  if (!el) return;\n  const options = {\n    capture: true,\n    ...inputOptions\n  };\n  // $FlowIgnore[method-unbinding]\n  if (el.addEventListener) {\n    el.addEventListener(event, handler, options);\n  } else if (el.attachEvent) {\n    el.attachEvent('on' + event, handler);\n  } else {\n    // $FlowIgnore: Doesn't think elements are indexable\n    el['on' + event] = handler;\n  }\n}\nfunction removeEvent(el /*: ?Node*/, event /*: string*/, handler /*: Function*/, inputOptions /*: Object*/) /*: void*/{\n  if (!el) return;\n  const options = {\n    capture: true,\n    ...inputOptions\n  };\n  // $FlowIgnore[method-unbinding]\n  if (el.removeEventListener) {\n    el.removeEventListener(event, handler, options);\n  } else if (el.detachEvent) {\n    el.detachEvent('on' + event, handler);\n  } else {\n    // $FlowIgnore: Doesn't think elements are indexable\n    el['on' + event] = null;\n  }\n}\nfunction outerHeight(node /*: HTMLElement*/) /*: number*/{\n  // This is deliberately excluding margin for our calculations, since we are using\n  // offsetTop which is including margin. See getBoundPosition\n  let height = node.clientHeight;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  height += (0, _shims.int)(computedStyle.borderTopWidth);\n  height += (0, _shims.int)(computedStyle.borderBottomWidth);\n  return height;\n}\nfunction outerWidth(node /*: HTMLElement*/) /*: number*/{\n  // This is deliberately excluding margin for our calculations, since we are using\n  // offsetLeft which is including margin. See getBoundPosition\n  let width = node.clientWidth;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  width += (0, _shims.int)(computedStyle.borderLeftWidth);\n  width += (0, _shims.int)(computedStyle.borderRightWidth);\n  return width;\n}\nfunction innerHeight(node /*: HTMLElement*/) /*: number*/{\n  let height = node.clientHeight;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  height -= (0, _shims.int)(computedStyle.paddingTop);\n  height -= (0, _shims.int)(computedStyle.paddingBottom);\n  return height;\n}\nfunction innerWidth(node /*: HTMLElement*/) /*: number*/{\n  let width = node.clientWidth;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  width -= (0, _shims.int)(computedStyle.paddingLeft);\n  width -= (0, _shims.int)(computedStyle.paddingRight);\n  return width;\n}\n/*:: interface EventWithOffset {\n  clientX: number, clientY: number\n}*/\n// Get from offsetParent\nfunction offsetXYFromParent(evt /*: EventWithOffset*/, offsetParent /*: HTMLElement*/, scale /*: number*/) /*: ControlPosition*/{\n  const isBody = offsetParent === offsetParent.ownerDocument.body;\n  const offsetParentRect = isBody ? {\n    left: 0,\n    top: 0\n  } : offsetParent.getBoundingClientRect();\n  const x = (evt.clientX + offsetParent.scrollLeft - offsetParentRect.left) / scale;\n  const y = (evt.clientY + offsetParent.scrollTop - offsetParentRect.top) / scale;\n  return {\n    x,\n    y\n  };\n}\nfunction createCSSTransform(controlPos /*: ControlPosition*/, positionOffset /*: PositionOffsetControlPosition*/) /*: Object*/{\n  const translation = getTranslation(controlPos, positionOffset, 'px');\n  return {\n    [(0, _getPrefix.browserPrefixToKey)('transform', _getPrefix.default)]: translation\n  };\n}\nfunction createSVGTransform(controlPos /*: ControlPosition*/, positionOffset /*: PositionOffsetControlPosition*/) /*: string*/{\n  const translation = getTranslation(controlPos, positionOffset, '');\n  return translation;\n}\nfunction getTranslation(_ref /*:: */, positionOffset /*: PositionOffsetControlPosition*/, unitSuffix /*: string*/) /*: string*/{\n  let {\n    x,\n    y\n  } /*: ControlPosition*/ = _ref /*: ControlPosition*/;\n  let translation = `translate(${x}${unitSuffix},${y}${unitSuffix})`;\n  if (positionOffset) {\n    const defaultX = `${typeof positionOffset.x === 'string' ? positionOffset.x : positionOffset.x + unitSuffix}`;\n    const defaultY = `${typeof positionOffset.y === 'string' ? positionOffset.y : positionOffset.y + unitSuffix}`;\n    translation = `translate(${defaultX}, ${defaultY})` + translation;\n  }\n  return translation;\n}\nfunction getTouch(e /*: MouseTouchEvent*/, identifier /*: number*/) /*: ?{clientX: number, clientY: number}*/{\n  return e.targetTouches && (0, _shims.findInArray)(e.targetTouches, t => identifier === t.identifier) || e.changedTouches && (0, _shims.findInArray)(e.changedTouches, t => identifier === t.identifier);\n}\nfunction getTouchIdentifier(e /*: MouseTouchEvent*/) /*: ?number*/{\n  if (e.targetTouches && e.targetTouches[0]) return e.targetTouches[0].identifier;\n  if (e.changedTouches && e.changedTouches[0]) return e.changedTouches[0].identifier;\n}\n\n// User-select Hacks:\n//\n// Useful for preventing blue highlights all over everything when dragging.\n\n// Note we're passing `document` b/c we could be iframed\nfunction addUserSelectStyles(doc /*: ?Document*/) {\n  if (!doc) return;\n  let styleEl = doc.getElementById('react-draggable-style-el');\n  if (!styleEl) {\n    styleEl = doc.createElement('style');\n    styleEl.type = 'text/css';\n    styleEl.id = 'react-draggable-style-el';\n    styleEl.innerHTML = '.react-draggable-transparent-selection *::-moz-selection {all: inherit;}\\n';\n    styleEl.innerHTML += '.react-draggable-transparent-selection *::selection {all: inherit;}\\n';\n    doc.getElementsByTagName('head')[0].appendChild(styleEl);\n  }\n  if (doc.body) addClassName(doc.body, 'react-draggable-transparent-selection');\n}\nfunction scheduleRemoveUserSelectStyles(doc /*: ?Document*/) {\n  // Prevent a possible \"forced reflow\"\n  if (window.requestAnimationFrame) {\n    window.requestAnimationFrame(() => {\n      removeUserSelectStyles(doc);\n    });\n  } else {\n    removeUserSelectStyles(doc);\n  }\n}\nfunction removeUserSelectStyles(doc /*: ?Document*/) {\n  if (!doc) return;\n  try {\n    if (doc.body) removeClassName(doc.body, 'react-draggable-transparent-selection');\n    // $FlowIgnore: IE\n    if (doc.selection) {\n      // $FlowIgnore: IE\n      doc.selection.empty();\n    } else {\n      // Remove selection caused by scroll, unless it's a focused input\n      // (we use doc.defaultView in case we're in an iframe)\n      const selection = (doc.defaultView || window).getSelection();\n      if (selection && selection.type !== 'Caret') {\n        selection.removeAllRanges();\n      }\n    }\n  } catch (e) {\n    // probably IE\n  }\n}\nfunction addClassName(el /*: HTMLElement*/, className /*: string*/) {\n  if (el.classList) {\n    el.classList.add(className);\n  } else {\n    if (!el.className.match(new RegExp(`(?:^|\\\\s)${className}(?!\\\\S)`))) {\n      el.className += ` ${className}`;\n    }\n  }\n}\nfunction removeClassName(el /*: HTMLElement*/, className /*: string*/) {\n  if (el.classList) {\n    el.classList.remove(className);\n  } else {\n    el.className = el.className.replace(new RegExp(`(?:^|\\\\s)${className}(?!\\\\S)`, 'g'), '');\n  }\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.canDragX = canDragX;\nexports.canDragY = canDragY;\nexports.createCoreData = createCoreData;\nexports.createDraggableData = createDraggableData;\nexports.getBoundPosition = getBoundPosition;\nexports.getControlPosition = getControlPosition;\nexports.snapToGrid = snapToGrid;\nvar _shims = require(\"./shims\");\nvar _domFns = require(\"./domFns\");\n/*:: import type Draggable from '../Draggable';*/\n/*:: import type {Bounds, ControlPosition, DraggableData, MouseTouchEvent} from './types';*/\n/*:: import type DraggableCore from '../DraggableCore';*/\nfunction getBoundPosition(draggable /*: Draggable*/, x /*: number*/, y /*: number*/) /*: [number, number]*/{\n  // If no bounds, short-circuit and move on\n  if (!draggable.props.bounds) return [x, y];\n\n  // Clone new bounds\n  let {\n    bounds\n  } = draggable.props;\n  bounds = typeof bounds === 'string' ? bounds : cloneBounds(bounds);\n  const node = findDOMNode(draggable);\n  if (typeof bounds === 'string') {\n    const {\n      ownerDocument\n    } = node;\n    const ownerWindow = ownerDocument.defaultView;\n    let boundNode;\n    if (bounds === 'parent') {\n      boundNode = node.parentNode;\n    } else {\n      // Flow assigns the wrong return type (Node) for getRootNode(),\n      // so we cast it to one of the correct types (Element).\n      // The others are Document and ShadowRoot.\n      // All three implement querySelector() so it's safe to call.\n      const rootNode = ((node.getRootNode() /*: any*/) /*: Element*/);\n      boundNode = rootNode.querySelector(bounds);\n    }\n    if (!(boundNode instanceof ownerWindow.HTMLElement)) {\n      throw new Error('Bounds selector \"' + bounds + '\" could not find an element.');\n    }\n    const boundNodeEl /*: HTMLElement*/ = boundNode; // for Flow, can't seem to refine correctly\n    const nodeStyle = ownerWindow.getComputedStyle(node);\n    const boundNodeStyle = ownerWindow.getComputedStyle(boundNodeEl);\n    // Compute bounds. This is a pain with padding and offsets but this gets it exactly right.\n    bounds = {\n      left: -node.offsetLeft + (0, _shims.int)(boundNodeStyle.paddingLeft) + (0, _shims.int)(nodeStyle.marginLeft),\n      top: -node.offsetTop + (0, _shims.int)(boundNodeStyle.paddingTop) + (0, _shims.int)(nodeStyle.marginTop),\n      right: (0, _domFns.innerWidth)(boundNodeEl) - (0, _domFns.outerWidth)(node) - node.offsetLeft + (0, _shims.int)(boundNodeStyle.paddingRight) - (0, _shims.int)(nodeStyle.marginRight),\n      bottom: (0, _domFns.innerHeight)(boundNodeEl) - (0, _domFns.outerHeight)(node) - node.offsetTop + (0, _shims.int)(boundNodeStyle.paddingBottom) - (0, _shims.int)(nodeStyle.marginBottom)\n    };\n  }\n\n  // Keep x and y below right and bottom limits...\n  if ((0, _shims.isNum)(bounds.right)) x = Math.min(x, bounds.right);\n  if ((0, _shims.isNum)(bounds.bottom)) y = Math.min(y, bounds.bottom);\n\n  // But above left and top limits.\n  if ((0, _shims.isNum)(bounds.left)) x = Math.max(x, bounds.left);\n  if ((0, _shims.isNum)(bounds.top)) y = Math.max(y, bounds.top);\n  return [x, y];\n}\nfunction snapToGrid(grid /*: [number, number]*/, pendingX /*: number*/, pendingY /*: number*/) /*: [number, number]*/{\n  const x = Math.round(pendingX / grid[0]) * grid[0];\n  const y = Math.round(pendingY / grid[1]) * grid[1];\n  return [x, y];\n}\nfunction canDragX(draggable /*: Draggable*/) /*: boolean*/{\n  return draggable.props.axis === 'both' || draggable.props.axis === 'x';\n}\nfunction canDragY(draggable /*: Draggable*/) /*: boolean*/{\n  return draggable.props.axis === 'both' || draggable.props.axis === 'y';\n}\n\n// Get {x, y} positions from event.\nfunction getControlPosition(e /*: MouseTouchEvent*/, touchIdentifier /*: ?number*/, draggableCore /*: DraggableCore*/) /*: ?ControlPosition*/{\n  const touchObj = typeof touchIdentifier === 'number' ? (0, _domFns.getTouch)(e, touchIdentifier) : null;\n  if (typeof touchIdentifier === 'number' && !touchObj) return null; // not the right touch\n  const node = findDOMNode(draggableCore);\n  // User can provide an offsetParent if desired.\n  const offsetParent = draggableCore.props.offsetParent || node.offsetParent || node.ownerDocument.body;\n  return (0, _domFns.offsetXYFromParent)(touchObj || e, offsetParent, draggableCore.props.scale);\n}\n\n// Create an data object exposed by <DraggableCore>'s events\nfunction createCoreData(draggable /*: DraggableCore*/, x /*: number*/, y /*: number*/) /*: DraggableData*/{\n  const isStart = !(0, _shims.isNum)(draggable.lastX);\n  const node = findDOMNode(draggable);\n  if (isStart) {\n    // If this is our first move, use the x and y as last coords.\n    return {\n      node,\n      deltaX: 0,\n      deltaY: 0,\n      lastX: x,\n      lastY: y,\n      x,\n      y\n    };\n  } else {\n    // Otherwise calculate proper values.\n    return {\n      node,\n      deltaX: x - draggable.lastX,\n      deltaY: y - draggable.lastY,\n      lastX: draggable.lastX,\n      lastY: draggable.lastY,\n      x,\n      y\n    };\n  }\n}\n\n// Create an data exposed by <Draggable>'s events\nfunction createDraggableData(draggable /*: Draggable*/, coreData /*: DraggableData*/) /*: DraggableData*/{\n  const scale = draggable.props.scale;\n  return {\n    node: coreData.node,\n    x: draggable.state.x + coreData.deltaX / scale,\n    y: draggable.state.y + coreData.deltaY / scale,\n    deltaX: coreData.deltaX / scale,\n    deltaY: coreData.deltaY / scale,\n    lastX: draggable.state.x,\n    lastY: draggable.state.y\n  };\n}\n\n// A lot faster than stringify/parse\nfunction cloneBounds(bounds /*: Bounds*/) /*: Bounds*/{\n  return {\n    left: bounds.left,\n    top: bounds.top,\n    right: bounds.right,\n    bottom: bounds.bottom\n  };\n}\nfunction findDOMNode(draggable /*: Draggable | DraggableCore*/) /*: HTMLElement*/{\n  const node = draggable.findDOMNode();\n  if (!node) {\n    throw new Error('<DraggableCore>: Unmounted during event!');\n  }\n  // $FlowIgnore we can't assert on HTMLElement due to tests... FIXME\n  return node;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = log;\n/*eslint no-console:0*/\nfunction log() {\n  if (undefined) console.log(...arguments);\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _domFns = require(\"./utils/domFns\");\nvar _positionFns = require(\"./utils/positionFns\");\nvar _shims = require(\"./utils/shims\");\nvar _log = _interopRequireDefault(require(\"./utils/log\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/*:: import type {EventHandler, MouseTouchEvent} from './utils/types';*/\n/*:: import type {Element as ReactElement} from 'react';*/\n// Simple abstraction for dragging events names.\nconst eventsFor = {\n  touch: {\n    start: 'touchstart',\n    move: 'touchmove',\n    stop: 'touchend'\n  },\n  mouse: {\n    start: 'mousedown',\n    move: 'mousemove',\n    stop: 'mouseup'\n  }\n};\n\n// Default to mouse events.\nlet dragEventFor = eventsFor.mouse;\n/*:: export type DraggableData = {\n  node: HTMLElement,\n  x: number, y: number,\n  deltaX: number, deltaY: number,\n  lastX: number, lastY: number,\n};*/\n/*:: export type DraggableEventHandler = (e: MouseEvent, data: DraggableData) => void | false;*/\n/*:: export type ControlPosition = {x: number, y: number};*/\n/*:: export type PositionOffsetControlPosition = {x: number|string, y: number|string};*/\n/*:: export type DraggableCoreDefaultProps = {\n  allowAnyClick: boolean,\n  allowMobileScroll: boolean,\n  disabled: boolean,\n  enableUserSelectHack: boolean,\n  onStart: DraggableEventHandler,\n  onDrag: DraggableEventHandler,\n  onStop: DraggableEventHandler,\n  onMouseDown: (e: MouseEvent) => void,\n  scale: number,\n};*/\n/*:: export type DraggableCoreProps = {\n  ...DraggableCoreDefaultProps,\n  cancel: string,\n  children: ReactElement<any>,\n  offsetParent: HTMLElement,\n  grid: [number, number],\n  handle: string,\n  nodeRef?: ?React.ElementRef<any>,\n};*/\n//\n// Define <DraggableCore>.\n//\n// <DraggableCore> is for advanced usage of <Draggable>. It maintains minimal internal state so it can\n// work well with libraries that require more control over the element.\n//\n\nclass DraggableCore extends React.Component /*:: <DraggableCoreProps>*/{\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"dragging\", false);\n    // Used while dragging to determine deltas.\n    _defineProperty(this, \"lastX\", NaN);\n    _defineProperty(this, \"lastY\", NaN);\n    _defineProperty(this, \"touchIdentifier\", null);\n    _defineProperty(this, \"mounted\", false);\n    _defineProperty(this, \"handleDragStart\", e => {\n      // Make it possible to attach event handlers on top of this one.\n      this.props.onMouseDown(e);\n\n      // Only accept left-clicks.\n      if (!this.props.allowAnyClick && typeof e.button === 'number' && e.button !== 0) return false;\n\n      // Get nodes. Be sure to grab relative document (could be iframed)\n      const thisNode = this.findDOMNode();\n      if (!thisNode || !thisNode.ownerDocument || !thisNode.ownerDocument.body) {\n        throw new Error('<DraggableCore> not mounted on DragStart!');\n      }\n      const {\n        ownerDocument\n      } = thisNode;\n\n      // Short circuit if handle or cancel prop was provided and selector doesn't match.\n      if (this.props.disabled || !(e.target instanceof ownerDocument.defaultView.Node) || this.props.handle && !(0, _domFns.matchesSelectorAndParentsTo)(e.target, this.props.handle, thisNode) || this.props.cancel && (0, _domFns.matchesSelectorAndParentsTo)(e.target, this.props.cancel, thisNode)) {\n        return;\n      }\n\n      // Prevent scrolling on mobile devices, like ipad/iphone.\n      // Important that this is after handle/cancel.\n      if (e.type === 'touchstart' && !this.props.allowMobileScroll) e.preventDefault();\n\n      // Set touch identifier in component state if this is a touch event. This allows us to\n      // distinguish between individual touches on multitouch screens by identifying which\n      // touchpoint was set to this element.\n      const touchIdentifier = (0, _domFns.getTouchIdentifier)(e);\n      this.touchIdentifier = touchIdentifier;\n\n      // Get the current drag point from the event. This is used as the offset.\n      const position = (0, _positionFns.getControlPosition)(e, touchIdentifier, this);\n      if (position == null) return; // not possible but satisfies flow\n      const {\n        x,\n        y\n      } = position;\n\n      // Create an event object with all the data parents need to make a decision here.\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n      (0, _log.default)('DraggableCore: handleDragStart: %j', coreEvent);\n\n      // Call event handler. If it returns explicit false, cancel.\n      (0, _log.default)('calling', this.props.onStart);\n      const shouldUpdate = this.props.onStart(e, coreEvent);\n      if (shouldUpdate === false || this.mounted === false) return;\n\n      // Add a style to the body to disable user-select. This prevents text from\n      // being selected all over the page.\n      if (this.props.enableUserSelectHack) (0, _domFns.addUserSelectStyles)(ownerDocument);\n\n      // Initiate dragging. Set the current x and y as offsets\n      // so we know how much we've moved during the drag. This allows us\n      // to drag elements around even if they have been moved, without issue.\n      this.dragging = true;\n      this.lastX = x;\n      this.lastY = y;\n\n      // Add events to the document directly so we catch when the user's mouse/touch moves outside of\n      // this element. We use different events depending on whether or not we have detected that this\n      // is a touch-capable device.\n      (0, _domFns.addEvent)(ownerDocument, dragEventFor.move, this.handleDrag);\n      (0, _domFns.addEvent)(ownerDocument, dragEventFor.stop, this.handleDragStop);\n    });\n    _defineProperty(this, \"handleDrag\", e => {\n      // Get the current drag point from the event. This is used as the offset.\n      const position = (0, _positionFns.getControlPosition)(e, this.touchIdentifier, this);\n      if (position == null) return;\n      let {\n        x,\n        y\n      } = position;\n\n      // Snap to grid if prop has been provided\n      if (Array.isArray(this.props.grid)) {\n        let deltaX = x - this.lastX,\n          deltaY = y - this.lastY;\n        [deltaX, deltaY] = (0, _positionFns.snapToGrid)(this.props.grid, deltaX, deltaY);\n        if (!deltaX && !deltaY) return; // skip useless drag\n        x = this.lastX + deltaX, y = this.lastY + deltaY;\n      }\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n      (0, _log.default)('DraggableCore: handleDrag: %j', coreEvent);\n\n      // Call event handler. If it returns explicit false, trigger end.\n      const shouldUpdate = this.props.onDrag(e, coreEvent);\n      if (shouldUpdate === false || this.mounted === false) {\n        try {\n          // $FlowIgnore\n          this.handleDragStop(new MouseEvent('mouseup'));\n        } catch (err) {\n          // Old browsers\n          const event = ((document.createEvent('MouseEvents') /*: any*/) /*: MouseTouchEvent*/);\n          // I see why this insanity was deprecated\n          // $FlowIgnore\n          event.initMouseEvent('mouseup', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);\n          this.handleDragStop(event);\n        }\n        return;\n      }\n      this.lastX = x;\n      this.lastY = y;\n    });\n    _defineProperty(this, \"handleDragStop\", e => {\n      if (!this.dragging) return;\n      const position = (0, _positionFns.getControlPosition)(e, this.touchIdentifier, this);\n      if (position == null) return;\n      let {\n        x,\n        y\n      } = position;\n\n      // Snap to grid if prop has been provided\n      if (Array.isArray(this.props.grid)) {\n        let deltaX = x - this.lastX || 0;\n        let deltaY = y - this.lastY || 0;\n        [deltaX, deltaY] = (0, _positionFns.snapToGrid)(this.props.grid, deltaX, deltaY);\n        x = this.lastX + deltaX, y = this.lastY + deltaY;\n      }\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n\n      // Call event handler\n      const shouldContinue = this.props.onStop(e, coreEvent);\n      if (shouldContinue === false || this.mounted === false) return false;\n      const thisNode = this.findDOMNode();\n      if (thisNode) {\n        // Remove user-select hack\n        if (this.props.enableUserSelectHack) (0, _domFns.scheduleRemoveUserSelectStyles)(thisNode.ownerDocument);\n      }\n      (0, _log.default)('DraggableCore: handleDragStop: %j', coreEvent);\n\n      // Reset the el.\n      this.dragging = false;\n      this.lastX = NaN;\n      this.lastY = NaN;\n      if (thisNode) {\n        // Remove event handlers\n        (0, _log.default)('DraggableCore: Removing handlers');\n        (0, _domFns.removeEvent)(thisNode.ownerDocument, dragEventFor.move, this.handleDrag);\n        (0, _domFns.removeEvent)(thisNode.ownerDocument, dragEventFor.stop, this.handleDragStop);\n      }\n    });\n    _defineProperty(this, \"onMouseDown\", e => {\n      dragEventFor = eventsFor.mouse; // on touchscreen laptops we could switch back to mouse\n\n      return this.handleDragStart(e);\n    });\n    _defineProperty(this, \"onMouseUp\", e => {\n      dragEventFor = eventsFor.mouse;\n      return this.handleDragStop(e);\n    });\n    // Same as onMouseDown (start drag), but now consider this a touch device.\n    _defineProperty(this, \"onTouchStart\", e => {\n      // We're on a touch device now, so change the event handlers\n      dragEventFor = eventsFor.touch;\n      return this.handleDragStart(e);\n    });\n    _defineProperty(this, \"onTouchEnd\", e => {\n      // We're on a touch device now, so change the event handlers\n      dragEventFor = eventsFor.touch;\n      return this.handleDragStop(e);\n    });\n  }\n  componentDidMount() {\n    this.mounted = true;\n    // Touch handlers must be added with {passive: false} to be cancelable.\n    // https://developers.google.com/web/updates/2017/01/scrolling-intervention\n    const thisNode = this.findDOMNode();\n    if (thisNode) {\n      (0, _domFns.addEvent)(thisNode, eventsFor.touch.start, this.onTouchStart, {\n        passive: false\n      });\n    }\n  }\n  componentWillUnmount() {\n    this.mounted = false;\n    // Remove any leftover event handlers. Remove both touch and mouse handlers in case\n    // some browser quirk caused a touch event to fire during a mouse move, or vice versa.\n    const thisNode = this.findDOMNode();\n    if (thisNode) {\n      const {\n        ownerDocument\n      } = thisNode;\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.mouse.move, this.handleDrag);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.touch.move, this.handleDrag);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.mouse.stop, this.handleDragStop);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.touch.stop, this.handleDragStop);\n      (0, _domFns.removeEvent)(thisNode, eventsFor.touch.start, this.onTouchStart, {\n        passive: false\n      });\n      if (this.props.enableUserSelectHack) (0, _domFns.scheduleRemoveUserSelectStyles)(ownerDocument);\n    }\n  }\n\n  // React Strict Mode compatibility: if `nodeRef` is passed, we will use it instead of trying to find\n  // the underlying DOM node ourselves. See the README for more information.\n  findDOMNode() /*: ?HTMLElement*/{\n    return this.props?.nodeRef ? this.props?.nodeRef?.current : _reactDom.default.findDOMNode(this);\n  }\n  render() /*: React.Element<any>*/{\n    // Reuse the child provided\n    // This makes it flexible to use whatever element is wanted (div, ul, etc)\n    return /*#__PURE__*/React.cloneElement(React.Children.only(this.props.children), {\n      // Note: mouseMove handler is attached to document so it will still function\n      // when the user drags quickly and leaves the bounds of the element.\n      onMouseDown: this.onMouseDown,\n      onMouseUp: this.onMouseUp,\n      // onTouchStart is added on `componentDidMount` so they can be added with\n      // {passive: false}, which allows it to cancel. See\n      // https://developers.google.com/web/updates/2017/01/scrolling-intervention\n      onTouchEnd: this.onTouchEnd\n    });\n  }\n}\nexports.default = DraggableCore;\n_defineProperty(DraggableCore, \"displayName\", 'DraggableCore');\n_defineProperty(DraggableCore, \"propTypes\", {\n  /**\n   * `allowAnyClick` allows dragging using any mouse button.\n   * By default, we only accept the left button.\n   *\n   * Defaults to `false`.\n   */\n  allowAnyClick: _propTypes.default.bool,\n  /**\n   * `allowMobileScroll` turns off cancellation of the 'touchstart' event\n   * on mobile devices. Only enable this if you are having trouble with click\n   * events. Prefer using 'handle' / 'cancel' instead.\n   *\n   * Defaults to `false`.\n   */\n  allowMobileScroll: _propTypes.default.bool,\n  children: _propTypes.default.node.isRequired,\n  /**\n   * `disabled`, if true, stops the <Draggable> from dragging. All handlers,\n   * with the exception of `onMouseDown`, will not fire.\n   */\n  disabled: _propTypes.default.bool,\n  /**\n   * By default, we add 'user-select:none' attributes to the document body\n   * to prevent ugly text selection during drag. If this is causing problems\n   * for your app, set this to `false`.\n   */\n  enableUserSelectHack: _propTypes.default.bool,\n  /**\n   * `offsetParent`, if set, uses the passed DOM node to compute drag offsets\n   * instead of using the parent node.\n   */\n  offsetParent: function (props /*: DraggableCoreProps*/, propName /*: $Keys<DraggableCoreProps>*/) {\n    if (props[propName] && props[propName].nodeType !== 1) {\n      throw new Error('Draggable\\'s offsetParent must be a DOM Node.');\n    }\n  },\n  /**\n   * `grid` specifies the x and y that dragging should snap to.\n   */\n  grid: _propTypes.default.arrayOf(_propTypes.default.number),\n  /**\n   * `handle` specifies a selector to be used as the handle that initiates drag.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *         return (\n   *            <Draggable handle=\".handle\">\n   *              <div>\n   *                  <div className=\"handle\">Click me to drag</div>\n   *                  <div>This is some other content</div>\n   *              </div>\n   *           </Draggable>\n   *         );\n   *       }\n   *   });\n   * ```\n   */\n  handle: _propTypes.default.string,\n  /**\n   * `cancel` specifies a selector to be used to prevent drag initialization.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *           return(\n   *               <Draggable cancel=\".cancel\">\n   *                   <div>\n   *                     <div className=\"cancel\">You can't drag from here</div>\n   *                     <div>Dragging here works fine</div>\n   *                   </div>\n   *               </Draggable>\n   *           );\n   *       }\n   *   });\n   * ```\n   */\n  cancel: _propTypes.default.string,\n  /* If running in React Strict mode, ReactDOM.findDOMNode() is deprecated.\n   * Unfortunately, in order for <Draggable> to work properly, we need raw access\n   * to the underlying DOM node. If you want to avoid the warning, pass a `nodeRef`\n   * as in this example:\n   *\n   * function MyComponent() {\n   *   const nodeRef = React.useRef(null);\n   *   return (\n   *     <Draggable nodeRef={nodeRef}>\n   *       <div ref={nodeRef}>Example Target</div>\n   *     </Draggable>\n   *   );\n   * }\n   *\n   * This can be used for arbitrarily nested components, so long as the ref ends up\n   * pointing to the actual child DOM node and not a custom component.\n   */\n  nodeRef: _propTypes.default.object,\n  /**\n   * Called when dragging starts.\n   * If this function returns the boolean false, dragging will be canceled.\n   */\n  onStart: _propTypes.default.func,\n  /**\n   * Called while dragging.\n   * If this function returns the boolean false, dragging will be canceled.\n   */\n  onDrag: _propTypes.default.func,\n  /**\n   * Called when dragging stops.\n   * If this function returns the boolean false, the drag will remain active.\n   */\n  onStop: _propTypes.default.func,\n  /**\n   * A workaround option which can be passed if onMouseDown needs to be accessed,\n   * since it'll always be blocked (as there is internal use of onMouseDown)\n   */\n  onMouseDown: _propTypes.default.func,\n  /**\n   * `scale`, if set, applies scaling while dragging an element\n   */\n  scale: _propTypes.default.number,\n  /**\n   * These properties should be defined on the child, not here.\n   */\n  className: _shims.dontSetMe,\n  style: _shims.dontSetMe,\n  transform: _shims.dontSetMe\n});\n_defineProperty(DraggableCore, \"defaultProps\", {\n  allowAnyClick: false,\n  // by default only accept left click\n  allowMobileScroll: false,\n  disabled: false,\n  enableUserSelectHack: true,\n  onStart: function () {},\n  onDrag: function () {},\n  onStop: function () {},\n  onMouseDown: function () {},\n  scale: 1\n});", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"DraggableCore\", {\n  enumerable: true,\n  get: function () {\n    return _DraggableCore.default;\n  }\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _clsx = require(\"clsx\");\nvar _domFns = require(\"./utils/domFns\");\nvar _positionFns = require(\"./utils/positionFns\");\nvar _shims = require(\"./utils/shims\");\nvar _DraggableCore = _interopRequireDefault(require(\"./DraggableCore\"));\nvar _log = _interopRequireDefault(require(\"./utils/log\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); } /*:: import type {ControlPosition, PositionOffsetControlPosition, DraggableCoreProps, DraggableCoreDefaultProps} from './DraggableCore';*/\n/*:: import type {Bounds, DraggableEventHandler} from './utils/types';*/\n/*:: import type {Element as ReactElement} from 'react';*/\n/*:: type DraggableState = {\n  dragging: boolean,\n  dragged: boolean,\n  x: number, y: number,\n  slackX: number, slackY: number,\n  isElementSVG: boolean,\n  prevPropsPosition: ?ControlPosition,\n};*/\n/*:: export type DraggableDefaultProps = {\n  ...DraggableCoreDefaultProps,\n  axis: 'both' | 'x' | 'y' | 'none',\n  bounds: Bounds | string | false,\n  defaultClassName: string,\n  defaultClassNameDragging: string,\n  defaultClassNameDragged: string,\n  defaultPosition: ControlPosition,\n  scale: number,\n};*/\n/*:: export type DraggableProps = {\n  ...DraggableCoreProps,\n  ...DraggableDefaultProps,\n  positionOffset: PositionOffsetControlPosition,\n  position: ControlPosition,\n};*/\n//\n// Define <Draggable>\n//\nclass Draggable extends React.Component /*:: <DraggableProps, DraggableState>*/{\n  // React 16.3+\n  // Arity (props, state)\n  static getDerivedStateFromProps(_ref /*:: */, _ref2 /*:: */) /*: ?Partial<DraggableState>*/{\n    let {\n      position\n    } /*: DraggableProps*/ = _ref /*: DraggableProps*/;\n    let {\n      prevPropsPosition\n    } /*: DraggableState*/ = _ref2 /*: DraggableState*/;\n    // Set x/y if a new position is provided in props that is different than the previous.\n    if (position && (!prevPropsPosition || position.x !== prevPropsPosition.x || position.y !== prevPropsPosition.y)) {\n      (0, _log.default)('Draggable: getDerivedStateFromProps %j', {\n        position,\n        prevPropsPosition\n      });\n      return {\n        x: position.x,\n        y: position.y,\n        prevPropsPosition: {\n          ...position\n        }\n      };\n    }\n    return null;\n  }\n  constructor(props /*: DraggableProps*/) {\n    super(props);\n    _defineProperty(this, \"onDragStart\", (e, coreData) => {\n      (0, _log.default)('Draggable: onDragStart: %j', coreData);\n\n      // Short-circuit if user's callback killed it.\n      const shouldStart = this.props.onStart(e, (0, _positionFns.createDraggableData)(this, coreData));\n      // Kills start event on core as well, so move handlers are never bound.\n      if (shouldStart === false) return false;\n      this.setState({\n        dragging: true,\n        dragged: true\n      });\n    });\n    _defineProperty(this, \"onDrag\", (e, coreData) => {\n      if (!this.state.dragging) return false;\n      (0, _log.default)('Draggable: onDrag: %j', coreData);\n      const uiData = (0, _positionFns.createDraggableData)(this, coreData);\n      const newState = {\n        x: uiData.x,\n        y: uiData.y,\n        slackX: 0,\n        slackY: 0\n      };\n\n      // Keep within bounds.\n      if (this.props.bounds) {\n        // Save original x and y.\n        const {\n          x,\n          y\n        } = newState;\n\n        // Add slack to the values used to calculate bound position. This will ensure that if\n        // we start removing slack, the element won't react to it right away until it's been\n        // completely removed.\n        newState.x += this.state.slackX;\n        newState.y += this.state.slackY;\n\n        // Get bound position. This will ceil/floor the x and y within the boundaries.\n        const [newStateX, newStateY] = (0, _positionFns.getBoundPosition)(this, newState.x, newState.y);\n        newState.x = newStateX;\n        newState.y = newStateY;\n\n        // Recalculate slack by noting how much was shaved by the boundPosition handler.\n        newState.slackX = this.state.slackX + (x - newState.x);\n        newState.slackY = this.state.slackY + (y - newState.y);\n\n        // Update the event we fire to reflect what really happened after bounds took effect.\n        uiData.x = newState.x;\n        uiData.y = newState.y;\n        uiData.deltaX = newState.x - this.state.x;\n        uiData.deltaY = newState.y - this.state.y;\n      }\n\n      // Short-circuit if user's callback killed it.\n      const shouldUpdate = this.props.onDrag(e, uiData);\n      if (shouldUpdate === false) return false;\n      this.setState(newState);\n    });\n    _defineProperty(this, \"onDragStop\", (e, coreData) => {\n      if (!this.state.dragging) return false;\n\n      // Short-circuit if user's callback killed it.\n      const shouldContinue = this.props.onStop(e, (0, _positionFns.createDraggableData)(this, coreData));\n      if (shouldContinue === false) return false;\n      (0, _log.default)('Draggable: onDragStop: %j', coreData);\n      const newState /*: Partial<DraggableState>*/ = {\n        dragging: false,\n        slackX: 0,\n        slackY: 0\n      };\n\n      // If this is a controlled component, the result of this operation will be to\n      // revert back to the old position. We expect a handler on `onDragStop`, at the least.\n      const controlled = Boolean(this.props.position);\n      if (controlled) {\n        const {\n          x,\n          y\n        } = this.props.position;\n        newState.x = x;\n        newState.y = y;\n      }\n      this.setState(newState);\n    });\n    this.state = {\n      // Whether or not we are currently dragging.\n      dragging: false,\n      // Whether or not we have been dragged before.\n      dragged: false,\n      // Current transform x and y.\n      x: props.position ? props.position.x : props.defaultPosition.x,\n      y: props.position ? props.position.y : props.defaultPosition.y,\n      prevPropsPosition: {\n        ...props.position\n      },\n      // Used for compensating for out-of-bounds drags\n      slackX: 0,\n      slackY: 0,\n      // Can only determine if SVG after mounting\n      isElementSVG: false\n    };\n    if (props.position && !(props.onDrag || props.onStop)) {\n      // eslint-disable-next-line no-console\n      console.warn('A `position` was applied to this <Draggable>, without drag handlers. This will make this ' + 'component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the ' + '`position` of this element.');\n    }\n  }\n  componentDidMount() {\n    // Check to see if the element passed is an instanceof SVGElement\n    if (typeof window.SVGElement !== 'undefined' && this.findDOMNode() instanceof window.SVGElement) {\n      this.setState({\n        isElementSVG: true\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.dragging) {\n      this.setState({\n        dragging: false\n      }); // prevents invariant if unmounted while dragging\n    }\n  }\n\n  // React Strict Mode compatibility: if `nodeRef` is passed, we will use it instead of trying to find\n  // the underlying DOM node ourselves. See the README for more information.\n  findDOMNode() /*: ?HTMLElement*/{\n    return this.props?.nodeRef?.current ?? _reactDom.default.findDOMNode(this);\n  }\n  render() /*: ReactElement<any>*/{\n    const {\n      axis,\n      bounds,\n      children,\n      defaultPosition,\n      defaultClassName,\n      defaultClassNameDragging,\n      defaultClassNameDragged,\n      position,\n      positionOffset,\n      scale,\n      ...draggableCoreProps\n    } = this.props;\n    let style = {};\n    let svgTransform = null;\n\n    // If this is controlled, we don't want to move it - unless it's dragging.\n    const controlled = Boolean(position);\n    const draggable = !controlled || this.state.dragging;\n    const validPosition = position || defaultPosition;\n    const transformOpts = {\n      // Set left if horizontal drag is enabled\n      x: (0, _positionFns.canDragX)(this) && draggable ? this.state.x : validPosition.x,\n      // Set top if vertical drag is enabled\n      y: (0, _positionFns.canDragY)(this) && draggable ? this.state.y : validPosition.y\n    };\n\n    // If this element was SVG, we use the `transform` attribute.\n    if (this.state.isElementSVG) {\n      svgTransform = (0, _domFns.createSVGTransform)(transformOpts, positionOffset);\n    } else {\n      // Add a CSS transform to move the element around. This allows us to move the element around\n      // without worrying about whether or not it is relatively or absolutely positioned.\n      // If the item you are dragging already has a transform set, wrap it in a <span> so <Draggable>\n      // has a clean slate.\n      style = (0, _domFns.createCSSTransform)(transformOpts, positionOffset);\n    }\n\n    // Mark with class while dragging\n    const className = (0, _clsx.clsx)(children.props.className || '', defaultClassName, {\n      [defaultClassNameDragging]: this.state.dragging,\n      [defaultClassNameDragged]: this.state.dragged\n    });\n\n    // Reuse the child provided\n    // This makes it flexible to use whatever element is wanted (div, ul, etc)\n    return /*#__PURE__*/React.createElement(_DraggableCore.default, _extends({}, draggableCoreProps, {\n      onStart: this.onDragStart,\n      onDrag: this.onDrag,\n      onStop: this.onDragStop\n    }), /*#__PURE__*/React.cloneElement(React.Children.only(children), {\n      className: className,\n      style: {\n        ...children.props.style,\n        ...style\n      },\n      transform: svgTransform\n    }));\n  }\n}\nexports.default = Draggable;\n_defineProperty(Draggable, \"displayName\", 'Draggable');\n_defineProperty(Draggable, \"propTypes\", {\n  // Accepts all props <DraggableCore> accepts.\n  ..._DraggableCore.default.propTypes,\n  /**\n   * `axis` determines which axis the draggable can move.\n   *\n   *  Note that all callbacks will still return data as normal. This only\n   *  controls flushing to the DOM.\n   *\n   * 'both' allows movement horizontally and vertically.\n   * 'x' limits movement to horizontal axis.\n   * 'y' limits movement to vertical axis.\n   * 'none' limits all movement.\n   *\n   * Defaults to 'both'.\n   */\n  axis: _propTypes.default.oneOf(['both', 'x', 'y', 'none']),\n  /**\n   * `bounds` determines the range of movement available to the element.\n   * Available values are:\n   *\n   * 'parent' restricts movement within the Draggable's parent node.\n   *\n   * Alternatively, pass an object with the following properties, all of which are optional:\n   *\n   * {left: LEFT_BOUND, right: RIGHT_BOUND, bottom: BOTTOM_BOUND, top: TOP_BOUND}\n   *\n   * All values are in px.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *         return (\n   *            <Draggable bounds={{right: 300, bottom: 300}}>\n   *              <div>Content</div>\n   *           </Draggable>\n   *         );\n   *       }\n   *   });\n   * ```\n   */\n  bounds: _propTypes.default.oneOfType([_propTypes.default.shape({\n    left: _propTypes.default.number,\n    right: _propTypes.default.number,\n    top: _propTypes.default.number,\n    bottom: _propTypes.default.number\n  }), _propTypes.default.string, _propTypes.default.oneOf([false])]),\n  defaultClassName: _propTypes.default.string,\n  defaultClassNameDragging: _propTypes.default.string,\n  defaultClassNameDragged: _propTypes.default.string,\n  /**\n   * `defaultPosition` specifies the x and y that the dragged item should start at\n   *\n   * Example:\n   *\n   * ```jsx\n   *      let App = React.createClass({\n   *          render: function () {\n   *              return (\n   *                  <Draggable defaultPosition={{x: 25, y: 25}}>\n   *                      <div>I start with transformX: 25px and transformY: 25px;</div>\n   *                  </Draggable>\n   *              );\n   *          }\n   *      });\n   * ```\n   */\n  defaultPosition: _propTypes.default.shape({\n    x: _propTypes.default.number,\n    y: _propTypes.default.number\n  }),\n  positionOffset: _propTypes.default.shape({\n    x: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.string]),\n    y: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.string])\n  }),\n  /**\n   * `position`, if present, defines the current position of the element.\n   *\n   *  This is similar to how form elements in React work - if no `position` is supplied, the component\n   *  is uncontrolled.\n   *\n   * Example:\n   *\n   * ```jsx\n   *      let App = React.createClass({\n   *          render: function () {\n   *              return (\n   *                  <Draggable position={{x: 25, y: 25}}>\n   *                      <div>I start with transformX: 25px and transformY: 25px;</div>\n   *                  </Draggable>\n   *              );\n   *          }\n   *      });\n   * ```\n   */\n  position: _propTypes.default.shape({\n    x: _propTypes.default.number,\n    y: _propTypes.default.number\n  }),\n  /**\n   * These properties should be defined on the child, not here.\n   */\n  className: _shims.dontSetMe,\n  style: _shims.dontSetMe,\n  transform: _shims.dontSetMe\n});\n_defineProperty(Draggable, \"defaultProps\", {\n  ..._DraggableCore.default.defaultProps,\n  axis: 'both',\n  bounds: false,\n  defaultClassName: 'react-draggable',\n  defaultClassNameDragging: 'react-draggable-dragging',\n  defaultClassNameDragged: 'react-draggable-dragged',\n  defaultPosition: {\n    x: 0,\n    y: 0\n  },\n  scale: 1\n});", "\"use strict\";\n\nconst {\n  default: Draggable,\n  DraggableCore\n} = require('./Draggable');\n\n// Previous versions of this lib exported <Draggable> as the root export. As to no-// them, or TypeScript, we export *both* as the root and as 'default'.\n// See https://github.com/mzabriskie/react-draggable/pull/254\n// and https://github.com/mzabriskie/react-draggable/issues/266\nmodule.exports = Draggable;\nmodule.exports.default = Draggable;\nmodule.exports.DraggableCore = DraggableCore;", "\"use strict\";\n\nexports.__esModule = true;\nexports.cloneElement = cloneElement;\nvar _react = _interopRequireDefault(require(\"react\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n// React.addons.cloneWithProps look-alike that merges style & className.\nfunction cloneElement(element, props) {\n  if (props.style && element.props.style) {\n    props.style = _objectSpread(_objectSpread({}, element.props.style), props.style);\n  }\n  if (props.className && element.props.className) {\n    props.className = element.props.className + \" \" + props.className;\n  }\n  return /*#__PURE__*/_react.default.cloneElement(element, props);\n}", "\"use strict\";\n\nexports.__esModule = true;\nexports.resizableProps = void 0;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _reactDraggable = require(\"react-draggable\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nvar resizableProps = {\n  /*\n  * Restricts resizing to a particular axis (default: 'both')\n  * 'both' - allows resizing by width or height\n  * 'x' - only allows the width to be changed\n  * 'y' - only allows the height to be changed\n  * 'none' - disables resizing altogether\n  * */\n  axis: _propTypes.default.oneOf(['both', 'x', 'y', 'none']),\n  className: _propTypes.default.string,\n  /*\n  * Require that one and only one child be present.\n  * */\n  children: _propTypes.default.element.isRequired,\n  /*\n  * These will be passed wholesale to react-draggable's DraggableCore\n  * */\n  draggableOpts: _propTypes.default.shape({\n    allowAnyClick: _propTypes.default.bool,\n    cancel: _propTypes.default.string,\n    children: _propTypes.default.node,\n    disabled: _propTypes.default.bool,\n    enableUserSelectHack: _propTypes.default.bool,\n    offsetParent: _propTypes.default.node,\n    grid: _propTypes.default.arrayOf(_propTypes.default.number),\n    handle: _propTypes.default.string,\n    nodeRef: _propTypes.default.object,\n    onStart: _propTypes.default.func,\n    onDrag: _propTypes.default.func,\n    onStop: _propTypes.default.func,\n    onMouseDown: _propTypes.default.func,\n    scale: _propTypes.default.number\n  }),\n  /*\n  * Initial height\n  * */\n  height: function height() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var props = args[0];\n    // Required if resizing height or both\n    if (props.axis === 'both' || props.axis === 'y') {\n      var _PropTypes$number;\n      return (_PropTypes$number = _propTypes.default.number).isRequired.apply(_PropTypes$number, args);\n    }\n    return _propTypes.default.number.apply(_propTypes.default, args);\n  },\n  /*\n  * Customize cursor resize handle\n  * */\n  handle: _propTypes.default.oneOfType([_propTypes.default.node, _propTypes.default.func]),\n  /*\n  * If you change this, be sure to update your css\n  * */\n  handleSize: _propTypes.default.arrayOf(_propTypes.default.number),\n  lockAspectRatio: _propTypes.default.bool,\n  /*\n  * Max X & Y measure\n  * */\n  maxConstraints: _propTypes.default.arrayOf(_propTypes.default.number),\n  /*\n  * Min X & Y measure\n  * */\n  minConstraints: _propTypes.default.arrayOf(_propTypes.default.number),\n  /*\n  * Called on stop resize event\n  * */\n  onResizeStop: _propTypes.default.func,\n  /*\n  * Called on start resize event\n  * */\n  onResizeStart: _propTypes.default.func,\n  /*\n  * Called on resize event\n  * */\n  onResize: _propTypes.default.func,\n  /*\n  * Defines which resize handles should be rendered (default: 'se')\n  * 's' - South handle (bottom-center)\n  * 'w' - West handle (left-center)\n  * 'e' - East handle (right-center)\n  * 'n' - North handle (top-center)\n  * 'sw' - Southwest handle (bottom-left)\n  * 'nw' - Northwest handle (top-left)\n  * 'se' - Southeast handle (bottom-right)\n  * 'ne' - Northeast handle (top-center)\n  * */\n  resizeHandles: _propTypes.default.arrayOf(_propTypes.default.oneOf(['s', 'w', 'e', 'n', 'sw', 'nw', 'se', 'ne'])),\n  /*\n  * If `transform: scale(n)` is set on the parent, this should be set to `n`.\n  * */\n  transformScale: _propTypes.default.number,\n  /*\n   * Initial width\n   */\n  width: function width() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    var props = args[0];\n    // Required if resizing width or both\n    if (props.axis === 'both' || props.axis === 'x') {\n      var _PropTypes$number2;\n      return (_PropTypes$number2 = _propTypes.default.number).isRequired.apply(_PropTypes$number2, args);\n    }\n    return _propTypes.default.number.apply(_propTypes.default, args);\n  }\n};\nexports.resizableProps = resizableProps;", "\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _reactDraggable = require(\"react-draggable\");\nvar _utils = require(\"./utils\");\nvar _propTypes = require(\"./propTypes\");\nvar _excluded = [\"children\", \"className\", \"draggableOpts\", \"width\", \"height\", \"handle\", \"handleSize\", \"lockAspectRatio\", \"axis\", \"minConstraints\", \"maxConstraints\", \"onResize\", \"onResizeStop\", \"onResizeStart\", \"resizeHandles\", \"transformScale\"];\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n// The base <Resizable> component.\n// This component does not have state and relies on the parent to set its props based on callback data.\nvar Resizable = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Resizable, _React$Component);\n  function Resizable() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.handleRefs = {};\n    _this.lastHandleRect = null;\n    _this.slack = null;\n    return _this;\n  }\n  var _proto = Resizable.prototype;\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.resetData();\n  };\n  _proto.resetData = function resetData() {\n    this.lastHandleRect = this.slack = null;\n  }\n\n  // Clamp width and height within provided constraints\n  ;\n  _proto.runConstraints = function runConstraints(width, height) {\n    var _this$props = this.props,\n      minConstraints = _this$props.minConstraints,\n      maxConstraints = _this$props.maxConstraints,\n      lockAspectRatio = _this$props.lockAspectRatio;\n    // short circuit\n    if (!minConstraints && !maxConstraints && !lockAspectRatio) return [width, height];\n\n    // If constraining to min and max, we need to also fit width and height to aspect ratio.\n    if (lockAspectRatio) {\n      var ratio = this.props.width / this.props.height;\n      var deltaW = width - this.props.width;\n      var deltaH = height - this.props.height;\n\n      // Find which coordinate was greater and should push the other toward it.\n      // E.g.:\n      // ratio = 1, deltaW = 10, deltaH = 5, deltaH should become 10.\n      // ratio = 2, deltaW = 10, deltaH = 6, deltaW should become 12.\n      if (Math.abs(deltaW) > Math.abs(deltaH * ratio)) {\n        height = width / ratio;\n      } else {\n        width = height * ratio;\n      }\n    }\n    var oldW = width,\n      oldH = height;\n\n    // Add slack to the values used to calculate bound position. This will ensure that if\n    // we start removing slack, the element won't react to it right away until it's been\n    // completely removed.\n    var _ref = this.slack || [0, 0],\n      slackW = _ref[0],\n      slackH = _ref[1];\n    width += slackW;\n    height += slackH;\n    if (minConstraints) {\n      width = Math.max(minConstraints[0], width);\n      height = Math.max(minConstraints[1], height);\n    }\n    if (maxConstraints) {\n      width = Math.min(maxConstraints[0], width);\n      height = Math.min(maxConstraints[1], height);\n    }\n\n    // If the width or height changed, we must have introduced some slack. Record it for the next iteration.\n    this.slack = [slackW + (oldW - width), slackH + (oldH - height)];\n    return [width, height];\n  }\n\n  /**\n   * Wrapper around drag events to provide more useful data.\n   *\n   * @param  {String} handlerName Handler name to wrap.\n   * @return {Function}           Handler function.\n   */;\n  _proto.resizeHandler = function resizeHandler(handlerName, axis) {\n    var _this2 = this;\n    return function (e, _ref2) {\n      var node = _ref2.node,\n        deltaX = _ref2.deltaX,\n        deltaY = _ref2.deltaY;\n      // Reset data in case it was left over somehow (should not be possible)\n      if (handlerName === 'onResizeStart') _this2.resetData();\n\n      // Axis restrictions\n      var canDragX = (_this2.props.axis === 'both' || _this2.props.axis === 'x') && axis !== 'n' && axis !== 's';\n      var canDragY = (_this2.props.axis === 'both' || _this2.props.axis === 'y') && axis !== 'e' && axis !== 'w';\n      // No dragging possible.\n      if (!canDragX && !canDragY) return;\n\n      // Decompose axis for later use\n      var axisV = axis[0];\n      var axisH = axis[axis.length - 1]; // intentionally not axis[1], so that this catches axis === 'w' for example\n\n      // Track the element being dragged to account for changes in position.\n      // If a handle's position is changed between callbacks, we need to factor this in to the next callback.\n      // Failure to do so will cause the element to \"skip\" when resized upwards or leftwards.\n      var handleRect = node.getBoundingClientRect();\n      if (_this2.lastHandleRect != null) {\n        // If the handle has repositioned on either axis since last render,\n        // we need to increase our callback values by this much.\n        // Only checking 'n', 'w' since resizing by 's', 'w' won't affect the overall position on page,\n        if (axisH === 'w') {\n          var deltaLeftSinceLast = handleRect.left - _this2.lastHandleRect.left;\n          deltaX += deltaLeftSinceLast;\n        }\n        if (axisV === 'n') {\n          var deltaTopSinceLast = handleRect.top - _this2.lastHandleRect.top;\n          deltaY += deltaTopSinceLast;\n        }\n      }\n      // Storage of last rect so we know how much it has really moved.\n      _this2.lastHandleRect = handleRect;\n\n      // Reverse delta if using top or left drag handles.\n      if (axisH === 'w') deltaX = -deltaX;\n      if (axisV === 'n') deltaY = -deltaY;\n\n      // Update w/h by the deltas. Also factor in transformScale.\n      var width = _this2.props.width + (canDragX ? deltaX / _this2.props.transformScale : 0);\n      var height = _this2.props.height + (canDragY ? deltaY / _this2.props.transformScale : 0);\n\n      // Run user-provided constraints.\n      var _this2$runConstraints = _this2.runConstraints(width, height);\n      width = _this2$runConstraints[0];\n      height = _this2$runConstraints[1];\n      var dimensionsChanged = width !== _this2.props.width || height !== _this2.props.height;\n\n      // Call user-supplied callback if present.\n      var cb = typeof _this2.props[handlerName] === 'function' ? _this2.props[handlerName] : null;\n      // Don't call 'onResize' if dimensions haven't changed.\n      var shouldSkipCb = handlerName === 'onResize' && !dimensionsChanged;\n      if (cb && !shouldSkipCb) {\n        e.persist == null ? void 0 : e.persist();\n        cb(e, {\n          node: node,\n          size: {\n            width: width,\n            height: height\n          },\n          handle: axis\n        });\n      }\n\n      // Reset internal data\n      if (handlerName === 'onResizeStop') _this2.resetData();\n    };\n  }\n\n  // Render a resize handle given an axis & DOM ref. Ref *must* be attached for\n  // the underlying draggable library to work properly.\n  ;\n  _proto.renderResizeHandle = function renderResizeHandle(handleAxis, ref) {\n    var handle = this.props.handle;\n    // No handle provided, make the default\n    if (!handle) {\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: \"react-resizable-handle react-resizable-handle-\" + handleAxis,\n        ref: ref\n      });\n    }\n    // Handle is a function, such as:\n    // `handle={(handleAxis) => <span className={...} />}`\n    if (typeof handle === 'function') {\n      return handle(handleAxis, ref);\n    }\n    // Handle is a React component (composite or DOM).\n    var isDOMElement = typeof handle.type === 'string';\n    var props = _objectSpread({\n      ref: ref\n    }, isDOMElement ? {} : {\n      handleAxis: handleAxis\n    });\n    return /*#__PURE__*/React.cloneElement(handle, props);\n  };\n  _proto.render = function render() {\n    var _this3 = this;\n    // Pass along only props not meant for the `<Resizable>`.`\n    // eslint-disable-next-line no-unused-vars\n    var _this$props2 = this.props,\n      children = _this$props2.children,\n      className = _this$props2.className,\n      draggableOpts = _this$props2.draggableOpts,\n      width = _this$props2.width,\n      height = _this$props2.height,\n      handle = _this$props2.handle,\n      handleSize = _this$props2.handleSize,\n      lockAspectRatio = _this$props2.lockAspectRatio,\n      axis = _this$props2.axis,\n      minConstraints = _this$props2.minConstraints,\n      maxConstraints = _this$props2.maxConstraints,\n      onResize = _this$props2.onResize,\n      onResizeStop = _this$props2.onResizeStop,\n      onResizeStart = _this$props2.onResizeStart,\n      resizeHandles = _this$props2.resizeHandles,\n      transformScale = _this$props2.transformScale,\n      p = _objectWithoutPropertiesLoose(_this$props2, _excluded);\n\n    // What we're doing here is getting the child of this element, and cloning it with this element's props.\n    // We are then defining its children as:\n    // 1. Its original children (resizable's child's children), and\n    // 2. One or more draggable handles.\n    return (0, _utils.cloneElement)(children, _objectSpread(_objectSpread({}, p), {}, {\n      className: (className ? className + \" \" : '') + \"react-resizable\",\n      children: [].concat(children.props.children, resizeHandles.map(function (handleAxis) {\n        var _this3$handleRefs$han;\n        // Create a ref to the handle so that `<DraggableCore>` doesn't have to use ReactDOM.findDOMNode().\n        var ref = (_this3$handleRefs$han = _this3.handleRefs[handleAxis]) != null ? _this3$handleRefs$han : _this3.handleRefs[handleAxis] = /*#__PURE__*/React.createRef();\n        return /*#__PURE__*/React.createElement(_reactDraggable.DraggableCore, _extends({}, draggableOpts, {\n          nodeRef: ref,\n          key: \"resizableHandle-\" + handleAxis,\n          onStop: _this3.resizeHandler('onResizeStop', handleAxis),\n          onStart: _this3.resizeHandler('onResizeStart', handleAxis),\n          onDrag: _this3.resizeHandler('onResize', handleAxis)\n        }), _this3.renderResizeHandle(handleAxis, ref));\n      }))\n    }));\n  };\n  return Resizable;\n}(React.Component);\nexports.default = Resizable;\nResizable.propTypes = _propTypes.resizableProps;\nResizable.defaultProps = {\n  axis: 'both',\n  handleSize: [20, 20],\n  lockAspectRatio: false,\n  minConstraints: [20, 20],\n  maxConstraints: [Infinity, Infinity],\n  resizeHandles: ['se'],\n  transformScale: 1\n};", "\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _Resizable = _interopRequireDefault(require(\"./Resizable\"));\nvar _propTypes2 = require(\"./propTypes\");\nvar _excluded = [\"handle\", \"handleSize\", \"onResize\", \"onResizeStart\", \"onResizeStop\", \"draggableOpts\", \"minConstraints\", \"maxConstraints\", \"lockAspectRatio\", \"axis\", \"width\", \"height\", \"resizeHandles\", \"style\", \"transformScale\"];\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar ResizableBox = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ResizableBox, _React$Component);\n  function ResizableBox() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.state = {\n      width: _this.props.width,\n      height: _this.props.height,\n      propsWidth: _this.props.width,\n      propsHeight: _this.props.height\n    };\n    _this.onResize = function (e, data) {\n      var size = data.size;\n      if (_this.props.onResize) {\n        e.persist == null ? void 0 : e.persist();\n        _this.setState(size, function () {\n          return _this.props.onResize && _this.props.onResize(e, data);\n        });\n      } else {\n        _this.setState(size);\n      }\n    };\n    return _this;\n  }\n  ResizableBox.getDerivedStateFromProps = function getDerivedStateFromProps(props, state) {\n    // If parent changes height/width, set that in our state.\n    if (state.propsWidth !== props.width || state.propsHeight !== props.height) {\n      return {\n        width: props.width,\n        height: props.height,\n        propsWidth: props.width,\n        propsHeight: props.height\n      };\n    }\n    return null;\n  };\n  var _proto = ResizableBox.prototype;\n  _proto.render = function render() {\n    // Basic wrapper around a Resizable instance.\n    // If you use Resizable directly, you are responsible for updating the child component\n    // with a new width and height.\n    var _this$props = this.props,\n      handle = _this$props.handle,\n      handleSize = _this$props.handleSize,\n      onResize = _this$props.onResize,\n      onResizeStart = _this$props.onResizeStart,\n      onResizeStop = _this$props.onResizeStop,\n      draggableOpts = _this$props.draggableOpts,\n      minConstraints = _this$props.minConstraints,\n      maxConstraints = _this$props.maxConstraints,\n      lockAspectRatio = _this$props.lockAspectRatio,\n      axis = _this$props.axis,\n      width = _this$props.width,\n      height = _this$props.height,\n      resizeHandles = _this$props.resizeHandles,\n      style = _this$props.style,\n      transformScale = _this$props.transformScale,\n      props = _objectWithoutPropertiesLoose(_this$props, _excluded);\n    return /*#__PURE__*/React.createElement(_Resizable.default, {\n      axis: axis,\n      draggableOpts: draggableOpts,\n      handle: handle,\n      handleSize: handleSize,\n      height: this.state.height,\n      lockAspectRatio: lockAspectRatio,\n      maxConstraints: maxConstraints,\n      minConstraints: minConstraints,\n      onResizeStart: onResizeStart,\n      onResize: this.onResize,\n      onResizeStop: onResizeStop,\n      resizeHandles: resizeHandles,\n      transformScale: transformScale,\n      width: this.state.width\n    }, /*#__PURE__*/React.createElement(\"div\", _extends({}, props, {\n      style: _objectSpread(_objectSpread({}, style), {}, {\n        width: this.state.width + 'px',\n        height: this.state.height + 'px'\n      })\n    })));\n  };\n  return ResizableBox;\n}(React.Component);\nexports.default = ResizableBox;\n// PropTypes are identical to <Resizable>, except that children are not strictly required to be present.\nResizableBox.propTypes = _objectSpread(_objectSpread({}, _propTypes2.resizableProps), {}, {\n  children: _propTypes.default.element\n});", "'use strict';\nmodule.exports = function() {\n  throw new Error(\"Don't instantiate Resizable directly! Use require('react-resizable').Resizable\");\n};\n\nmodule.exports.Resizable = require('./build/Resizable').default;\nmodule.exports.ResizableBox = require('./build/ResizableBox').default;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resizeHandleType = exports.resizeHandleAxesType = exports.default = void 0;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n/*:: import type {\n  Ref,\n  ChildrenArray as ReactChildrenArray,\n  Element as ReactElement\n} from \"react\";*/\n/*:: import type {\n  DragOverEvent,\n  EventCallback,\n  CompactType,\n  Layout,\n  LayoutItem,\n  ResizeHandleAxis\n} from \"./utils\";*/\n/*:: export type ReactRef<T: HTMLElement> = {|\n  +current: T | null\n|};*/\n// util\n/*:: export type ResizeHandle =\n  | ReactElement<any>\n  | ((\n      resizeHandleAxis: ResizeHandleAxis,\n      ref: ReactRef<HTMLElement>\n    ) => ReactElement<any>);*/\n// Defines which resize handles should be rendered (default: 'se')\n// Allows for any combination of:\n// 's' - South handle (bottom-center)\n// 'w' - West handle (left-center)\n// 'e' - East handle (right-center)\n// 'n' - North handle (top-center)\n// 'sw' - Southwest handle (bottom-left)\n// 'nw' - Northwest handle (top-left)\n// 'se' - Southeast handle (bottom-right)\n// 'ne' - Northeast handle (top-right)\nconst resizeHandleAxesType /*: ReactPropsChainableTypeChecker*/ = exports.resizeHandleAxesType = _propTypes.default.arrayOf(_propTypes.default.oneOf([\"s\", \"w\", \"e\", \"n\", \"sw\", \"nw\", \"se\", \"ne\"]));\n// Custom component for resize handles\nconst resizeHandleType /*: ReactPropsChainableTypeChecker*/ = exports.resizeHandleType = _propTypes.default.oneOfType([_propTypes.default.node, _propTypes.default.func]);\n/*:: export type Props = {|\n  className: string,\n  style: Object,\n  width: number,\n  autoSize: boolean,\n  cols: number,\n  draggableCancel: string,\n  draggableHandle: string,\n  verticalCompact: boolean,\n  compactType: CompactType,\n  layout: Layout,\n  margin: [number, number],\n  containerPadding: ?[number, number],\n  rowHeight: number,\n  maxRows: number,\n  isBounded: boolean,\n  isDraggable: boolean,\n  isResizable: boolean,\n  isDroppable: boolean,\n  preventCollision: boolean,\n  useCSSTransforms: boolean,\n  transformScale: number,\n  droppingItem: $Shape<LayoutItem>,\n  resizeHandles: ResizeHandleAxis[],\n  resizeHandle?: ResizeHandle,\n  allowOverlap: boolean,\n\n  // Callbacks\n  onLayoutChange: Layout => void,\n  onDrag: EventCallback,\n  onDragStart: EventCallback,\n  onDragStop: EventCallback,\n  onResize: EventCallback,\n  onResizeStart: EventCallback,\n  onResizeStop: EventCallback,\n  onDropDragOver: (e: DragOverEvent) => ?({| w?: number, h?: number |} | false),\n  onDrop: (layout: Layout, item: ?LayoutItem, e: Event) => void,\n  children: ReactChildrenArray<ReactElement<any>>,\n  innerRef?: Ref<\"div\">\n|};*/\n/*:: export type DefaultProps = $Diff<\n  Props,\n  {\n    children: ReactChildrenArray<ReactElement<any>>,\n    width: number\n  }\n>;*/\nvar _default = exports.default = {\n  //\n  // Basic props\n  //\n  className: _propTypes.default.string,\n  style: _propTypes.default.object,\n  // This can be set explicitly. If it is not set, it will automatically\n  // be set to the container width. Note that resizes will *not* cause this to adjust.\n  // If you need that behavior, use WidthProvider.\n  width: _propTypes.default.number,\n  // If true, the container height swells and contracts to fit contents\n  autoSize: _propTypes.default.bool,\n  // # of cols.\n  cols: _propTypes.default.number,\n  // A selector that will not be draggable.\n  draggableCancel: _propTypes.default.string,\n  // A selector for the draggable handler\n  draggableHandle: _propTypes.default.string,\n  // Deprecated\n  verticalCompact: function (props /*: Props*/) {\n    if (props.verticalCompact === false && process.env.NODE_ENV !== \"production\") {\n      console.warn(\n      // eslint-disable-line no-console\n      \"`verticalCompact` on <ReactGridLayout> is deprecated and will be removed soon. \" + 'Use `compactType`: \"horizontal\" | \"vertical\" | null.');\n    }\n  },\n  // Choose vertical or hotizontal compaction\n  compactType: (_propTypes.default.oneOf([\"vertical\", \"horizontal\"]) /*: ReactPropsChainableTypeChecker*/),\n  // layout is an array of object with the format:\n  // {x: Number, y: Number, w: Number, h: Number, i: String}\n  layout: function (props /*: Props*/) {\n    var layout = props.layout;\n    // I hope you're setting the data-grid property on the grid items\n    if (layout === undefined) return;\n    require(\"./utils\").validateLayout(layout, \"layout\");\n  },\n  //\n  // Grid Dimensions\n  //\n\n  // Margin between items [x, y] in px\n  margin: (_propTypes.default.arrayOf(_propTypes.default.number) /*: ReactPropsChainableTypeChecker*/),\n  // Padding inside the container [x, y] in px\n  containerPadding: (_propTypes.default.arrayOf(_propTypes.default.number) /*: ReactPropsChainableTypeChecker*/),\n  // Rows have a static height, but you can change this based on breakpoints if you like\n  rowHeight: _propTypes.default.number,\n  // Default Infinity, but you can specify a max here if you like.\n  // Note that this isn't fully fleshed out and won't error if you specify a layout that\n  // extends beyond the row capacity. It will, however, not allow users to drag/resize\n  // an item past the barrier. They can push items beyond the barrier, though.\n  // Intentionally not documented for this reason.\n  maxRows: _propTypes.default.number,\n  //\n  // Flags\n  //\n  isBounded: _propTypes.default.bool,\n  isDraggable: _propTypes.default.bool,\n  isResizable: _propTypes.default.bool,\n  // If true, grid can be placed one over the other.\n  allowOverlap: _propTypes.default.bool,\n  // If true, grid items won't change position when being dragged over.\n  preventCollision: _propTypes.default.bool,\n  // Use CSS transforms instead of top/left\n  useCSSTransforms: _propTypes.default.bool,\n  // parent layout transform scale\n  transformScale: _propTypes.default.number,\n  // If true, an external element can trigger onDrop callback with a specific grid position as a parameter\n  isDroppable: _propTypes.default.bool,\n  // Resize handle options\n  resizeHandles: resizeHandleAxesType,\n  resizeHandle: resizeHandleType,\n  //\n  // Callbacks\n  //\n\n  // Callback so you can save the layout. Calls after each drag & resize stops.\n  onLayoutChange: _propTypes.default.func,\n  // Calls when drag starts. Callback is of the signature (layout, oldItem, newItem, placeholder, e, ?node).\n  // All callbacks below have the same signature. 'start' and 'stop' callbacks omit the 'placeholder'.\n  onDragStart: _propTypes.default.func,\n  // Calls on each drag movement.\n  onDrag: _propTypes.default.func,\n  // Calls when drag is complete.\n  onDragStop: _propTypes.default.func,\n  //Calls when resize starts.\n  onResizeStart: _propTypes.default.func,\n  // Calls when resize movement happens.\n  onResize: _propTypes.default.func,\n  // Calls when resize is complete.\n  onResizeStop: _propTypes.default.func,\n  // Calls when some element is dropped.\n  onDrop: _propTypes.default.func,\n  //\n  // Other validations\n  //\n\n  droppingItem: (_propTypes.default.shape({\n    i: _propTypes.default.string.isRequired,\n    w: _propTypes.default.number.isRequired,\n    h: _propTypes.default.number.isRequired\n  }) /*: ReactPropsChainableTypeChecker*/),\n  // Children must not have duplicate keys.\n  children: function (props /*: Props*/, propName /*: string*/) {\n    const children = props[propName];\n\n    // Check children keys for duplicates. Throw if found.\n    const keys = {};\n    _react.default.Children.forEach(children, function (child) {\n      if (child?.key == null) return;\n      if (keys[child.key]) {\n        throw new Error('Duplicate child key \"' + child.key + '\" found! This will cause problems in ReactGridLayout.');\n      }\n      keys[child.key] = true;\n    });\n  },\n  // Optional ref for getting a reference for the wrapping div.\n  innerRef: _propTypes.default.any\n};", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _reactDom = require(\"react-dom\");\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _reactDraggable = require(\"react-draggable\");\nvar _reactResizable = require(\"react-resizable\");\nvar _utils = require(\"./utils\");\nvar _calculateUtils = require(\"./calculateUtils\");\nvar _ReactGridLayoutPropTypes = require(\"./ReactGridLayoutPropTypes\");\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/*:: import type { Element as ReactElement, Node as ReactNode } from \"react\";*/\n/*:: import type {\n  ReactDraggableCallbackData,\n  GridDragEvent,\n  GridResizeEvent,\n  DroppingPosition,\n  Position,\n  ResizeHandleAxis\n} from \"./utils\";*/\n/*:: import type { PositionParams } from \"./calculateUtils\";*/\n/*:: import type { ResizeHandle, ReactRef } from \"./ReactGridLayoutPropTypes\";*/\n/*:: type PartialPosition = { top: number, left: number };*/\n/*:: type GridItemCallback<Data: GridDragEvent | GridResizeEvent> = (\n  i: string,\n  w: number,\n  h: number,\n  Data\n) => void;*/\n/*:: type ResizeCallbackData = {\n  node: HTMLElement,\n  size: Position,\n  handle: ResizeHandleAxis\n};*/\n/*:: type GridItemResizeCallback = (\n  e: Event,\n  data: ResizeCallbackData,\n  position: Position\n) => void;*/\n/*:: type State = {\n  resizing: ?{ top: number, left: number, width: number, height: number },\n  dragging: ?{ top: number, left: number },\n  className: string\n};*/\n/*:: type Props = {\n  children: ReactElement<any>,\n  cols: number,\n  containerWidth: number,\n  margin: [number, number],\n  containerPadding: [number, number],\n  rowHeight: number,\n  maxRows: number,\n  isDraggable: boolean,\n  isResizable: boolean,\n  isBounded: boolean,\n  static?: boolean,\n  useCSSTransforms?: boolean,\n  usePercentages?: boolean,\n  transformScale: number,\n  droppingPosition?: DroppingPosition,\n\n  className: string,\n  style?: Object,\n  // Draggability\n  cancel: string,\n  handle: string,\n\n  x: number,\n  y: number,\n  w: number,\n  h: number,\n\n  minW: number,\n  maxW: number,\n  minH: number,\n  maxH: number,\n  i: string,\n\n  resizeHandles?: ResizeHandleAxis[],\n  resizeHandle?: ResizeHandle,\n\n  onDrag?: GridItemCallback<GridDragEvent>,\n  onDragStart?: GridItemCallback<GridDragEvent>,\n  onDragStop?: GridItemCallback<GridDragEvent>,\n  onResize?: GridItemCallback<GridResizeEvent>,\n  onResizeStart?: GridItemCallback<GridResizeEvent>,\n  onResizeStop?: GridItemCallback<GridResizeEvent>\n};*/\n/*:: type DefaultProps = {\n  className: string,\n  cancel: string,\n  handle: string,\n  minH: number,\n  minW: number,\n  maxH: number,\n  maxW: number,\n  transformScale: number\n};*/\n/**\n * An individual item within a ReactGridLayout.\n */\nclass GridItem extends _react.default.Component /*:: <Props, State>*/{\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      resizing: null,\n      dragging: null,\n      className: \"\"\n    });\n    _defineProperty(this, \"elementRef\", /*#__PURE__*/_react.default.createRef());\n    /**\n     * onDragStart event handler\n     * @param  {Event}  e             event data\n     * @param  {Object} callbackData  an object with node, delta and position information\n     */\n    _defineProperty(this, \"onDragStart\", (e, _ref) => {\n      let {\n        node\n      } = _ref;\n      const {\n        onDragStart,\n        transformScale\n      } = this.props;\n      if (!onDragStart) return;\n      const newPosition /*: PartialPosition*/ = {\n        top: 0,\n        left: 0\n      };\n\n      // TODO: this wont work on nested parents\n      const {\n        offsetParent\n      } = node;\n      if (!offsetParent) return;\n      const parentRect = offsetParent.getBoundingClientRect();\n      const clientRect = node.getBoundingClientRect();\n      const cLeft = clientRect.left / transformScale;\n      const pLeft = parentRect.left / transformScale;\n      const cTop = clientRect.top / transformScale;\n      const pTop = parentRect.top / transformScale;\n      newPosition.left = cLeft - pLeft + offsetParent.scrollLeft;\n      newPosition.top = cTop - pTop + offsetParent.scrollTop;\n      this.setState({\n        dragging: newPosition\n      });\n\n      // Call callback with this data\n      const {\n        x,\n        y\n      } = (0, _calculateUtils.calcXY)(this.getPositionParams(), newPosition.top, newPosition.left, this.props.w, this.props.h);\n      return onDragStart.call(this, this.props.i, x, y, {\n        e,\n        node,\n        newPosition\n      });\n    });\n    /**\n     * onDrag event handler\n     * @param  {Event}  e             event data\n     * @param  {Object} callbackData  an object with node, delta and position information\n     * @param  {boolean} dontFlush    if true, will not call flushSync\n     */\n    _defineProperty(this, \"onDrag\", (e, _ref2, dontFlush) => {\n      let {\n        node,\n        deltaX,\n        deltaY\n      } = _ref2;\n      const {\n        onDrag\n      } = this.props;\n      if (!onDrag) return;\n      if (!this.state.dragging) {\n        throw new Error(\"onDrag called before onDragStart.\");\n      }\n      let top = this.state.dragging.top + deltaY;\n      let left = this.state.dragging.left + deltaX;\n      const {\n        isBounded,\n        i,\n        w,\n        h,\n        containerWidth\n      } = this.props;\n      const positionParams = this.getPositionParams();\n\n      // Boundary calculations; keeps items within the grid\n      if (isBounded) {\n        const {\n          offsetParent\n        } = node;\n        if (offsetParent) {\n          const {\n            margin,\n            rowHeight\n          } = this.props;\n          const bottomBoundary = offsetParent.clientHeight - (0, _calculateUtils.calcGridItemWHPx)(h, rowHeight, margin[1]);\n          top = (0, _calculateUtils.clamp)(top, 0, bottomBoundary);\n          const colWidth = (0, _calculateUtils.calcGridColWidth)(positionParams);\n          const rightBoundary = containerWidth - (0, _calculateUtils.calcGridItemWHPx)(w, colWidth, margin[0]);\n          left = (0, _calculateUtils.clamp)(left, 0, rightBoundary);\n        }\n      }\n      const newPosition /*: PartialPosition*/ = {\n        top,\n        left\n      };\n\n      // dontFlush is set if we're calling from inside\n      if (dontFlush) {\n        this.setState({\n          dragging: newPosition\n        });\n      } else {\n        (0, _reactDom.flushSync)(() => {\n          this.setState({\n            dragging: newPosition\n          });\n        });\n      }\n\n      // Call callback with this data\n      const {\n        x,\n        y\n      } = (0, _calculateUtils.calcXY)(positionParams, top, left, w, h);\n      return onDrag.call(this, i, x, y, {\n        e,\n        node,\n        newPosition\n      });\n    });\n    /**\n     * onDragStop event handler\n     * @param  {Event}  e             event data\n     * @param  {Object} callbackData  an object with node, delta and position information\n     */\n    _defineProperty(this, \"onDragStop\", (e, _ref3) => {\n      let {\n        node\n      } = _ref3;\n      const {\n        onDragStop\n      } = this.props;\n      if (!onDragStop) return;\n      if (!this.state.dragging) {\n        throw new Error(\"onDragEnd called before onDragStart.\");\n      }\n      const {\n        w,\n        h,\n        i\n      } = this.props;\n      const {\n        left,\n        top\n      } = this.state.dragging;\n      const newPosition /*: PartialPosition*/ = {\n        top,\n        left\n      };\n      this.setState({\n        dragging: null\n      });\n      const {\n        x,\n        y\n      } = (0, _calculateUtils.calcXY)(this.getPositionParams(), top, left, w, h);\n      return onDragStop.call(this, i, x, y, {\n        e,\n        node,\n        newPosition\n      });\n    });\n    /**\n     * onResizeStop event handler\n     * @param  {Event}  e             event data\n     * @param  {Object} callbackData  an object with node and size information\n     */\n    _defineProperty(this, \"onResizeStop\", (e, callbackData, position) => this.onResizeHandler(e, callbackData, position, \"onResizeStop\"));\n    // onResizeStart event handler\n    _defineProperty(this, \"onResizeStart\", (e, callbackData, position) => this.onResizeHandler(e, callbackData, position, \"onResizeStart\"));\n    // onResize event handler\n    _defineProperty(this, \"onResize\", (e, callbackData, position) => this.onResizeHandler(e, callbackData, position, \"onResize\"));\n  }\n  shouldComponentUpdate(nextProps /*: Props*/, nextState /*: State*/) /*: boolean*/{\n    // We can't deeply compare children. If the developer memoizes them, we can\n    // use this optimization.\n    if (this.props.children !== nextProps.children) return true;\n    if (this.props.droppingPosition !== nextProps.droppingPosition) return true;\n    // TODO memoize these calculations so they don't take so long?\n    const oldPosition = (0, _calculateUtils.calcGridItemPosition)(this.getPositionParams(this.props), this.props.x, this.props.y, this.props.w, this.props.h, this.state);\n    const newPosition = (0, _calculateUtils.calcGridItemPosition)(this.getPositionParams(nextProps), nextProps.x, nextProps.y, nextProps.w, nextProps.h, nextState);\n    return !(0, _utils.fastPositionEqual)(oldPosition, newPosition) || this.props.useCSSTransforms !== nextProps.useCSSTransforms;\n  }\n  componentDidMount() {\n    this.moveDroppingItem({});\n  }\n  componentDidUpdate(prevProps /*: Props*/) {\n    this.moveDroppingItem(prevProps);\n  }\n\n  // When a droppingPosition is present, this means we should fire a move event, as if we had moved\n  // this element by `x, y` pixels.\n  moveDroppingItem(prevProps /*: Props*/) {\n    const {\n      droppingPosition\n    } = this.props;\n    if (!droppingPosition) return;\n    const node = this.elementRef.current;\n    // Can't find DOM node (are we unmounted?)\n    if (!node) return;\n    const prevDroppingPosition = prevProps.droppingPosition || {\n      left: 0,\n      top: 0\n    };\n    const {\n      dragging\n    } = this.state;\n    const shouldDrag = dragging && droppingPosition.left !== prevDroppingPosition.left || droppingPosition.top !== prevDroppingPosition.top;\n    if (!dragging) {\n      this.onDragStart(droppingPosition.e, {\n        node,\n        deltaX: droppingPosition.left,\n        deltaY: droppingPosition.top\n      });\n    } else if (shouldDrag) {\n      const deltaX = droppingPosition.left - dragging.left;\n      const deltaY = droppingPosition.top - dragging.top;\n      this.onDrag(droppingPosition.e, {\n        node,\n        deltaX,\n        deltaY\n      }, true // dontFLush: avoid flushSync to temper warnings\n      );\n    }\n  }\n  getPositionParams() /*: PositionParams*/{\n    let props /*: Props*/ = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.props;\n    return {\n      cols: props.cols,\n      containerPadding: props.containerPadding,\n      containerWidth: props.containerWidth,\n      margin: props.margin,\n      maxRows: props.maxRows,\n      rowHeight: props.rowHeight\n    };\n  }\n\n  /**\n   * This is where we set the grid item's absolute placement. It gets a little tricky because we want to do it\n   * well when server rendering, and the only way to do that properly is to use percentage width/left because\n   * we don't know exactly what the browser viewport is.\n   * Unfortunately, CSS Transforms, which are great for performance, break in this instance because a percentage\n   * left is relative to the item itself, not its container! So we cannot use them on the server rendering pass.\n   *\n   * @param  {Object} pos Position object with width, height, left, top.\n   * @return {Object}     Style object.\n   */\n  createStyle(pos /*: Position*/) /*: { [key: string]: ?string }*/{\n    const {\n      usePercentages,\n      containerWidth,\n      useCSSTransforms\n    } = this.props;\n    let style;\n    // CSS Transforms support (default)\n    if (useCSSTransforms) {\n      style = (0, _utils.setTransform)(pos);\n    } else {\n      // top,left (slow)\n      style = (0, _utils.setTopLeft)(pos);\n\n      // This is used for server rendering.\n      if (usePercentages) {\n        style.left = (0, _utils.perc)(pos.left / containerWidth);\n        style.width = (0, _utils.perc)(pos.width / containerWidth);\n      }\n    }\n    return style;\n  }\n\n  /**\n   * Mix a Draggable instance into a child.\n   * @param  {Element} child    Child element.\n   * @return {Element}          Child wrapped in Draggable.\n   */\n  mixinDraggable(child /*: ReactElement<any>*/, isDraggable /*: boolean*/) /*: ReactElement<any>*/{\n    return /*#__PURE__*/_react.default.createElement(_reactDraggable.DraggableCore, {\n      disabled: !isDraggable,\n      onStart: this.onDragStart,\n      onDrag: this.onDrag,\n      onStop: this.onDragStop,\n      handle: this.props.handle,\n      cancel: \".react-resizable-handle\" + (this.props.cancel ? \",\" + this.props.cancel : \"\"),\n      scale: this.props.transformScale,\n      nodeRef: this.elementRef\n    }, child);\n  }\n\n  /**\n   * Utility function to setup callback handler definitions for\n   * similarily structured resize events.\n   */\n  curryResizeHandler(position /*: Position*/, handler /*: Function*/) /*: Function*/{\n    return (e /*: Event*/, data /*: ResizeCallbackData*/) => /*: Function*/handler(e, data, position);\n  }\n\n  /**\n   * Mix a Resizable instance into a child.\n   * @param  {Element} child    Child element.\n   * @param  {Object} position  Position object (pixel values)\n   * @return {Element}          Child wrapped in Resizable.\n   */\n  mixinResizable(child /*: ReactElement<any>*/, position /*: Position*/, isResizable /*: boolean*/) /*: ReactElement<any>*/{\n    const {\n      cols,\n      minW,\n      minH,\n      maxW,\n      maxH,\n      transformScale,\n      resizeHandles,\n      resizeHandle\n    } = this.props;\n    const positionParams = this.getPositionParams();\n\n    // This is the max possible width - doesn't go to infinity because of the width of the window\n    const maxWidth = (0, _calculateUtils.calcGridItemPosition)(positionParams, 0, 0, cols, 0).width;\n\n    // Calculate min/max constraints using our min & maxes\n    const mins = (0, _calculateUtils.calcGridItemPosition)(positionParams, 0, 0, minW, minH);\n    const maxes = (0, _calculateUtils.calcGridItemPosition)(positionParams, 0, 0, maxW, maxH);\n    const minConstraints = [mins.width, mins.height];\n    const maxConstraints = [Math.min(maxes.width, maxWidth), Math.min(maxes.height, Infinity)];\n    return /*#__PURE__*/_react.default.createElement(_reactResizable.Resizable\n    // These are opts for the resize handle itself\n    , {\n      draggableOpts: {\n        disabled: !isResizable\n      },\n      className: isResizable ? undefined : \"react-resizable-hide\",\n      width: position.width,\n      height: position.height,\n      minConstraints: minConstraints,\n      maxConstraints: maxConstraints,\n      onResizeStop: this.curryResizeHandler(position, this.onResizeStop),\n      onResizeStart: this.curryResizeHandler(position, this.onResizeStart),\n      onResize: this.curryResizeHandler(position, this.onResize),\n      transformScale: transformScale,\n      resizeHandles: resizeHandles,\n      handle: resizeHandle\n    }, child);\n  }\n  /**\n   * Wrapper around resize events to provide more useful data.\n   */\n  onResizeHandler(e /*: Event*/, _ref4 /*:: */,\n  // 'size' is updated position\n  position /*: Position*/,\n  // existing position\n  handlerName /*: string*/) /*: void*/{\n    let {\n      node,\n      size,\n      handle\n    } /*: ResizeCallbackData*/ = _ref4 /*: ResizeCallbackData*/;\n    const handler = this.props[handlerName];\n    if (!handler) return;\n    const {\n      x,\n      y,\n      i,\n      maxH,\n      minH,\n      containerWidth\n    } = this.props;\n    const {\n      minW,\n      maxW\n    } = this.props;\n\n    // Clamping of dimensions based on resize direction\n    let updatedSize = size;\n    if (node) {\n      updatedSize = (0, _utils.resizeItemInDirection)(handle, position, size, containerWidth);\n      (0, _reactDom.flushSync)(() => {\n        this.setState({\n          resizing: handlerName === \"onResizeStop\" ? null : updatedSize\n        });\n      });\n    }\n\n    // Get new XY based on pixel size\n    let {\n      w,\n      h\n    } = (0, _calculateUtils.calcWH)(this.getPositionParams(), updatedSize.width, updatedSize.height, x, y, handle);\n\n    // Min/max capping.\n    // minW should be at least 1 (TODO propTypes validation?)\n    w = (0, _calculateUtils.clamp)(w, Math.max(minW, 1), maxW);\n    h = (0, _calculateUtils.clamp)(h, minH, maxH);\n    handler.call(this, i, w, h, {\n      e,\n      node,\n      size: updatedSize,\n      handle\n    });\n  }\n  render() /*: ReactNode*/{\n    const {\n      x,\n      y,\n      w,\n      h,\n      isDraggable,\n      isResizable,\n      droppingPosition,\n      useCSSTransforms\n    } = this.props;\n    const pos = (0, _calculateUtils.calcGridItemPosition)(this.getPositionParams(), x, y, w, h, this.state);\n    const child = _react.default.Children.only(this.props.children);\n\n    // Create the child element. We clone the existing element but modify its className and style.\n    let newChild = /*#__PURE__*/_react.default.cloneElement(child, {\n      ref: this.elementRef,\n      className: (0, _clsx.default)(\"react-grid-item\", child.props.className, this.props.className, {\n        static: this.props.static,\n        resizing: Boolean(this.state.resizing),\n        \"react-draggable\": isDraggable,\n        \"react-draggable-dragging\": Boolean(this.state.dragging),\n        dropping: Boolean(droppingPosition),\n        cssTransforms: useCSSTransforms\n      }),\n      // We can set the width and height on the child, but unfortunately we can't set the position.\n      style: {\n        ...this.props.style,\n        ...child.props.style,\n        ...this.createStyle(pos)\n      }\n    });\n\n    // Resizable support. This is usually on but the user can toggle it off.\n    newChild = this.mixinResizable(newChild, pos, isResizable);\n\n    // Draggable support. This is always on, except for with placeholders.\n    newChild = this.mixinDraggable(newChild, isDraggable);\n    return newChild;\n  }\n}\nexports.default = GridItem;\n_defineProperty(GridItem, \"propTypes\", {\n  // Children must be only a single element\n  children: _propTypes.default.element,\n  // General grid attributes\n  cols: _propTypes.default.number.isRequired,\n  containerWidth: _propTypes.default.number.isRequired,\n  rowHeight: _propTypes.default.number.isRequired,\n  margin: _propTypes.default.array.isRequired,\n  maxRows: _propTypes.default.number.isRequired,\n  containerPadding: _propTypes.default.array.isRequired,\n  // These are all in grid units\n  x: _propTypes.default.number.isRequired,\n  y: _propTypes.default.number.isRequired,\n  w: _propTypes.default.number.isRequired,\n  h: _propTypes.default.number.isRequired,\n  // All optional\n  minW: function (props /*: Props*/, propName /*: string*/) {\n    const value = props[propName];\n    if (typeof value !== \"number\") return new Error(\"minWidth not Number\");\n    if (value > props.w || value > props.maxW) return new Error(\"minWidth larger than item width/maxWidth\");\n  },\n  maxW: function (props /*: Props*/, propName /*: string*/) {\n    const value = props[propName];\n    if (typeof value !== \"number\") return new Error(\"maxWidth not Number\");\n    if (value < props.w || value < props.minW) return new Error(\"maxWidth smaller than item width/minWidth\");\n  },\n  minH: function (props /*: Props*/, propName /*: string*/) {\n    const value = props[propName];\n    if (typeof value !== \"number\") return new Error(\"minHeight not Number\");\n    if (value > props.h || value > props.maxH) return new Error(\"minHeight larger than item height/maxHeight\");\n  },\n  maxH: function (props /*: Props*/, propName /*: string*/) {\n    const value = props[propName];\n    if (typeof value !== \"number\") return new Error(\"maxHeight not Number\");\n    if (value < props.h || value < props.minH) return new Error(\"maxHeight smaller than item height/minHeight\");\n  },\n  // ID is nice to have for callbacks\n  i: _propTypes.default.string.isRequired,\n  // Resize handle options\n  resizeHandles: _ReactGridLayoutPropTypes.resizeHandleAxesType,\n  resizeHandle: _ReactGridLayoutPropTypes.resizeHandleType,\n  // Functions\n  onDragStop: _propTypes.default.func,\n  onDragStart: _propTypes.default.func,\n  onDrag: _propTypes.default.func,\n  onResizeStop: _propTypes.default.func,\n  onResizeStart: _propTypes.default.func,\n  onResize: _propTypes.default.func,\n  // Flags\n  isDraggable: _propTypes.default.bool.isRequired,\n  isResizable: _propTypes.default.bool.isRequired,\n  isBounded: _propTypes.default.bool.isRequired,\n  static: _propTypes.default.bool,\n  // Use CSS transforms instead of top/left\n  useCSSTransforms: _propTypes.default.bool.isRequired,\n  transformScale: _propTypes.default.number,\n  // Others\n  className: _propTypes.default.string,\n  // Selector for draggable handle\n  handle: _propTypes.default.string,\n  // Selector for draggable cancel (see react-draggable)\n  cancel: _propTypes.default.string,\n  // Current position of a dropping element\n  droppingPosition: _propTypes.default.shape({\n    e: _propTypes.default.object.isRequired,\n    left: _propTypes.default.number.isRequired,\n    top: _propTypes.default.number.isRequired\n  })\n});\n_defineProperty(GridItem, \"defaultProps\", {\n  className: \"\",\n  cancel: \"\",\n  handle: \"\",\n  minH: 1,\n  minW: 1,\n  maxH: Infinity,\n  maxW: Infinity,\n  transformScale: 1\n});", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _fastEquals = require(\"fast-equals\");\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nvar _utils = require(\"./utils\");\nvar _calculateUtils = require(\"./calculateUtils\");\nvar _GridItem = _interopRequireDefault(require(\"./GridItem\"));\nvar _ReactGridLayoutPropTypes = _interopRequireDefault(require(\"./ReactGridLayoutPropTypes\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/*:: import type {\n  ChildrenArray as ReactChildrenArray,\n  Element as ReactElement\n} from \"react\";*/\n/*:: import type {\n  CompactType,\n  GridResizeEvent,\n  GridDragEvent,\n  DragOverEvent,\n  Layout,\n  DroppingPosition,\n  LayoutItem\n} from \"./utils\";*/\n// Types\n/*:: import type { PositionParams } from \"./calculateUtils\";*/\n/*:: type State = {\n  activeDrag: ?LayoutItem,\n  layout: Layout,\n  mounted: boolean,\n  oldDragItem: ?LayoutItem,\n  oldLayout: ?Layout,\n  oldResizeItem: ?LayoutItem,\n  resizing: boolean,\n  droppingDOMNode: ?ReactElement<any>,\n  droppingPosition?: DroppingPosition,\n  // Mirrored props\n  children: ReactChildrenArray<ReactElement<any>>,\n  compactType?: CompactType,\n  propsLayout?: Layout\n};*/\n/*:: import type { Props, DefaultProps } from \"./ReactGridLayoutPropTypes\";*/\n// End Types\nconst layoutClassName = \"react-grid-layout\";\nlet isFirefox = false;\n// Try...catch will protect from navigator not existing (e.g. node) or a bad implementation of navigator\ntry {\n  isFirefox = /firefox/i.test(navigator.userAgent);\n} catch (e) {\n  /* Ignore */\n}\n\n/**\n * A reactive, fluid grid layout with draggable, resizable components.\n */\n\nclass ReactGridLayout extends React.Component /*:: <Props, State>*/{\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      activeDrag: null,\n      layout: (0, _utils.synchronizeLayoutWithChildren)(this.props.layout, this.props.children, this.props.cols,\n      // Legacy support for verticalCompact: false\n      (0, _utils.compactType)(this.props), this.props.allowOverlap),\n      mounted: false,\n      oldDragItem: null,\n      oldLayout: null,\n      oldResizeItem: null,\n      resizing: false,\n      droppingDOMNode: null,\n      children: []\n    });\n    _defineProperty(this, \"dragEnterCounter\", 0);\n    /**\n     * When dragging starts\n     * @param {String} i Id of the child\n     * @param {Number} x X position of the move\n     * @param {Number} y Y position of the move\n     * @param {Event} e The mousedown event\n     * @param {Element} node The current dragging DOM element\n     */\n    _defineProperty(this, \"onDragStart\", (i /*: string*/, x /*: number*/, y /*: number*/, _ref /*:: */) => {\n      let {\n        e,\n        node\n      } /*: GridDragEvent*/ = _ref /*: GridDragEvent*/;\n      const {\n        layout\n      } = this.state;\n      const l = (0, _utils.getLayoutItem)(layout, i);\n      if (!l) return;\n\n      // Create placeholder (display only)\n      const placeholder = {\n        w: l.w,\n        h: l.h,\n        x: l.x,\n        y: l.y,\n        placeholder: true,\n        i: i\n      };\n      this.setState({\n        oldDragItem: (0, _utils.cloneLayoutItem)(l),\n        oldLayout: layout,\n        activeDrag: placeholder\n      });\n      return this.props.onDragStart(layout, l, l, null, e, node);\n    });\n    /**\n     * Each drag movement create a new dragelement and move the element to the dragged location\n     * @param {String} i Id of the child\n     * @param {Number} x X position of the move\n     * @param {Number} y Y position of the move\n     * @param {Event} e The mousedown event\n     * @param {Element} node The current dragging DOM element\n     */\n    _defineProperty(this, \"onDrag\", (i, x, y, _ref2) => {\n      let {\n        e,\n        node\n      } = _ref2;\n      const {\n        oldDragItem\n      } = this.state;\n      let {\n        layout\n      } = this.state;\n      const {\n        cols,\n        allowOverlap,\n        preventCollision\n      } = this.props;\n      const l = (0, _utils.getLayoutItem)(layout, i);\n      if (!l) return;\n\n      // Create placeholder (display only)\n      const placeholder = {\n        w: l.w,\n        h: l.h,\n        x: l.x,\n        y: l.y,\n        placeholder: true,\n        i: i\n      };\n\n      // Move the element to the dragged location.\n      const isUserAction = true;\n      layout = (0, _utils.moveElement)(layout, l, x, y, isUserAction, preventCollision, (0, _utils.compactType)(this.props), cols, allowOverlap);\n      this.props.onDrag(layout, oldDragItem, l, placeholder, e, node);\n      this.setState({\n        layout: allowOverlap ? layout : (0, _utils.compact)(layout, (0, _utils.compactType)(this.props), cols),\n        activeDrag: placeholder\n      });\n    });\n    /**\n     * When dragging stops, figure out which position the element is closest to and update its x and y.\n     * @param  {String} i Index of the child.\n     * @param {Number} x X position of the move\n     * @param {Number} y Y position of the move\n     * @param {Event} e The mousedown event\n     * @param {Element} node The current dragging DOM element\n     */\n    _defineProperty(this, \"onDragStop\", (i, x, y, _ref3) => {\n      let {\n        e,\n        node\n      } = _ref3;\n      if (!this.state.activeDrag) return;\n      const {\n        oldDragItem\n      } = this.state;\n      let {\n        layout\n      } = this.state;\n      const {\n        cols,\n        preventCollision,\n        allowOverlap\n      } = this.props;\n      const l = (0, _utils.getLayoutItem)(layout, i);\n      if (!l) return;\n\n      // Move the element here\n      const isUserAction = true;\n      layout = (0, _utils.moveElement)(layout, l, x, y, isUserAction, preventCollision, (0, _utils.compactType)(this.props), cols, allowOverlap);\n\n      // Set state\n      const newLayout = allowOverlap ? layout : (0, _utils.compact)(layout, (0, _utils.compactType)(this.props), cols);\n      this.props.onDragStop(newLayout, oldDragItem, l, null, e, node);\n      const {\n        oldLayout\n      } = this.state;\n      this.setState({\n        activeDrag: null,\n        layout: newLayout,\n        oldDragItem: null,\n        oldLayout: null\n      });\n      this.onLayoutMaybeChanged(newLayout, oldLayout);\n    });\n    _defineProperty(this, \"onResizeStart\", (i, w, h, _ref4) => {\n      let {\n        e,\n        node\n      } = _ref4;\n      const {\n        layout\n      } = this.state;\n      const l = (0, _utils.getLayoutItem)(layout, i);\n      if (!l) return;\n      this.setState({\n        oldResizeItem: (0, _utils.cloneLayoutItem)(l),\n        oldLayout: this.state.layout,\n        resizing: true\n      });\n      this.props.onResizeStart(layout, l, l, null, e, node);\n    });\n    _defineProperty(this, \"onResize\", (i, w, h, _ref5) => {\n      let {\n        e,\n        node,\n        size,\n        handle\n      } = _ref5;\n      const {\n        oldResizeItem\n      } = this.state;\n      const {\n        layout\n      } = this.state;\n      const {\n        cols,\n        preventCollision,\n        allowOverlap\n      } = this.props;\n      let shouldMoveItem = false;\n      let finalLayout;\n      let x;\n      let y;\n      const [newLayout, l] = (0, _utils.withLayoutItem)(layout, i, l => {\n        let hasCollisions;\n        x = l.x;\n        y = l.y;\n        if ([\"sw\", \"w\", \"nw\", \"n\", \"ne\"].indexOf(handle) !== -1) {\n          if ([\"sw\", \"nw\", \"w\"].indexOf(handle) !== -1) {\n            x = l.x + (l.w - w);\n            w = l.x !== x && x < 0 ? l.w : w;\n            x = x < 0 ? 0 : x;\n          }\n          if ([\"ne\", \"n\", \"nw\"].indexOf(handle) !== -1) {\n            y = l.y + (l.h - h);\n            h = l.y !== y && y < 0 ? l.h : h;\n            y = y < 0 ? 0 : y;\n          }\n          shouldMoveItem = true;\n        }\n\n        // Something like quad tree should be used\n        // to find collisions faster\n        if (preventCollision && !allowOverlap) {\n          const collisions = (0, _utils.getAllCollisions)(layout, {\n            ...l,\n            w,\n            h,\n            x,\n            y\n          }).filter(layoutItem => layoutItem.i !== l.i);\n          hasCollisions = collisions.length > 0;\n\n          // If we're colliding, we need adjust the placeholder.\n          if (hasCollisions) {\n            // Reset layoutItem dimensions if there were collisions\n            y = l.y;\n            h = l.h;\n            x = l.x;\n            w = l.w;\n            shouldMoveItem = false;\n          }\n        }\n        l.w = w;\n        l.h = h;\n        return l;\n      });\n\n      // Shouldn't ever happen, but typechecking makes it necessary\n      if (!l) return;\n      finalLayout = newLayout;\n      if (shouldMoveItem) {\n        // Move the element to the new position.\n        const isUserAction = true;\n        finalLayout = (0, _utils.moveElement)(newLayout, l, x, y, isUserAction, this.props.preventCollision, (0, _utils.compactType)(this.props), cols, allowOverlap);\n      }\n\n      // Create placeholder element (display only)\n      const placeholder = {\n        w: l.w,\n        h: l.h,\n        x: l.x,\n        y: l.y,\n        static: true,\n        i: i\n      };\n      this.props.onResize(finalLayout, oldResizeItem, l, placeholder, e, node);\n\n      // Re-compact the newLayout and set the drag placeholder.\n      this.setState({\n        layout: allowOverlap ? finalLayout : (0, _utils.compact)(finalLayout, (0, _utils.compactType)(this.props), cols),\n        activeDrag: placeholder\n      });\n    });\n    _defineProperty(this, \"onResizeStop\", (i, w, h, _ref6) => {\n      let {\n        e,\n        node\n      } = _ref6;\n      const {\n        layout,\n        oldResizeItem\n      } = this.state;\n      const {\n        cols,\n        allowOverlap\n      } = this.props;\n      const l = (0, _utils.getLayoutItem)(layout, i);\n\n      // Set state\n      const newLayout = allowOverlap ? layout : (0, _utils.compact)(layout, (0, _utils.compactType)(this.props), cols);\n      this.props.onResizeStop(newLayout, oldResizeItem, l, null, e, node);\n      const {\n        oldLayout\n      } = this.state;\n      this.setState({\n        activeDrag: null,\n        layout: newLayout,\n        oldResizeItem: null,\n        oldLayout: null,\n        resizing: false\n      });\n      this.onLayoutMaybeChanged(newLayout, oldLayout);\n    });\n    // Called while dragging an element. Part of browser native drag/drop API.\n    // Native event target might be the layout itself, or an element within the layout.\n    _defineProperty(this, \"onDragOver\", e => {\n      e.preventDefault(); // Prevent any browser native action\n      e.stopPropagation();\n\n      // we should ignore events from layout's children in Firefox\n      // to avoid unpredictable jumping of a dropping placeholder\n      // FIXME remove this hack\n      if (isFirefox &&\n      // $FlowIgnore can't figure this out\n      !e.nativeEvent.target?.classList.contains(layoutClassName)) {\n        return false;\n      }\n      const {\n        droppingItem,\n        onDropDragOver,\n        margin,\n        cols,\n        rowHeight,\n        maxRows,\n        width,\n        containerPadding,\n        transformScale\n      } = this.props;\n      // Allow user to customize the dropping item or short-circuit the drop based on the results\n      // of the `onDragOver(e: Event)` callback.\n      const onDragOverResult = onDropDragOver?.(e);\n      if (onDragOverResult === false) {\n        if (this.state.droppingDOMNode) {\n          this.removeDroppingPlaceholder();\n        }\n        return false;\n      }\n      const finalDroppingItem = {\n        ...droppingItem,\n        ...onDragOverResult\n      };\n      const {\n        layout\n      } = this.state;\n\n      // $FlowIgnore missing def\n      const gridRect = e.currentTarget.getBoundingClientRect(); // The grid's position in the viewport\n\n      // Calculate the mouse position relative to the grid\n      const layerX = e.clientX - gridRect.left;\n      const layerY = e.clientY - gridRect.top;\n      const droppingPosition = {\n        left: layerX / transformScale,\n        top: layerY / transformScale,\n        e\n      };\n      if (!this.state.droppingDOMNode) {\n        const positionParams /*: PositionParams*/ = {\n          cols,\n          margin,\n          maxRows,\n          rowHeight,\n          containerWidth: width,\n          containerPadding: containerPadding || margin\n        };\n        const calculatedPosition = (0, _calculateUtils.calcXY)(positionParams, layerY, layerX, finalDroppingItem.w, finalDroppingItem.h);\n        this.setState({\n          droppingDOMNode: /*#__PURE__*/React.createElement(\"div\", {\n            key: finalDroppingItem.i\n          }),\n          droppingPosition,\n          layout: [...layout, {\n            ...finalDroppingItem,\n            x: calculatedPosition.x,\n            y: calculatedPosition.y,\n            static: false,\n            isDraggable: true\n          }]\n        });\n      } else if (this.state.droppingPosition) {\n        const {\n          left,\n          top\n        } = this.state.droppingPosition;\n        const shouldUpdatePosition = left != layerX || top != layerY;\n        if (shouldUpdatePosition) {\n          this.setState({\n            droppingPosition\n          });\n        }\n      }\n    });\n    _defineProperty(this, \"removeDroppingPlaceholder\", () => {\n      const {\n        droppingItem,\n        cols\n      } = this.props;\n      const {\n        layout\n      } = this.state;\n      const newLayout = (0, _utils.compact)(layout.filter(l => l.i !== droppingItem.i), (0, _utils.compactType)(this.props), cols, this.props.allowOverlap);\n      this.setState({\n        layout: newLayout,\n        droppingDOMNode: null,\n        activeDrag: null,\n        droppingPosition: undefined\n      });\n    });\n    _defineProperty(this, \"onDragLeave\", e => {\n      e.preventDefault(); // Prevent any browser native action\n      e.stopPropagation();\n      this.dragEnterCounter--;\n\n      // onDragLeave can be triggered on each layout's child.\n      // But we know that count of dragEnter and dragLeave events\n      // will be balanced after leaving the layout's container\n      // so we can increase and decrease count of dragEnter and\n      // when it'll be equal to 0 we'll remove the placeholder\n      if (this.dragEnterCounter === 0) {\n        this.removeDroppingPlaceholder();\n      }\n    });\n    _defineProperty(this, \"onDragEnter\", e => {\n      e.preventDefault(); // Prevent any browser native action\n      e.stopPropagation();\n      this.dragEnterCounter++;\n    });\n    _defineProperty(this, \"onDrop\", (e /*: Event*/) => {\n      e.preventDefault(); // Prevent any browser native action\n      e.stopPropagation();\n      const {\n        droppingItem\n      } = this.props;\n      const {\n        layout\n      } = this.state;\n      const item = layout.find(l => l.i === droppingItem.i);\n\n      // reset dragEnter counter on drop\n      this.dragEnterCounter = 0;\n      this.removeDroppingPlaceholder();\n      this.props.onDrop(layout, item, e);\n    });\n  }\n  componentDidMount() {\n    this.setState({\n      mounted: true\n    });\n    // Possibly call back with layout on mount. This should be done after correcting the layout width\n    // to ensure we don't rerender with the wrong width.\n    this.onLayoutMaybeChanged(this.state.layout, this.props.layout);\n  }\n  static getDerivedStateFromProps(nextProps /*: Props*/, prevState /*: State*/) /*: $Shape<State> | null*/{\n    let newLayoutBase;\n    if (prevState.activeDrag) {\n      return null;\n    }\n\n    // Legacy support for compactType\n    // Allow parent to set layout directly.\n    if (!(0, _fastEquals.deepEqual)(nextProps.layout, prevState.propsLayout) || nextProps.compactType !== prevState.compactType) {\n      newLayoutBase = nextProps.layout;\n    } else if (!(0, _utils.childrenEqual)(nextProps.children, prevState.children)) {\n      // If children change, also regenerate the layout. Use our state\n      // as the base in case because it may be more up to date than\n      // what is in props.\n      newLayoutBase = prevState.layout;\n    }\n\n    // We need to regenerate the layout.\n    if (newLayoutBase) {\n      const newLayout = (0, _utils.synchronizeLayoutWithChildren)(newLayoutBase, nextProps.children, nextProps.cols, (0, _utils.compactType)(nextProps), nextProps.allowOverlap);\n      return {\n        layout: newLayout,\n        // We need to save these props to state for using\n        // getDerivedStateFromProps instead of componentDidMount (in which we would get extra rerender)\n        compactType: nextProps.compactType,\n        children: nextProps.children,\n        propsLayout: nextProps.layout\n      };\n    }\n    return null;\n  }\n  shouldComponentUpdate(nextProps /*: Props*/, nextState /*: State*/) /*: boolean*/{\n    return (\n      // NOTE: this is almost always unequal. Therefore the only way to get better performance\n      // from SCU is if the user intentionally memoizes children. If they do, and they can\n      // handle changes properly, performance will increase.\n      this.props.children !== nextProps.children || !(0, _utils.fastRGLPropsEqual)(this.props, nextProps, _fastEquals.deepEqual) || this.state.activeDrag !== nextState.activeDrag || this.state.mounted !== nextState.mounted || this.state.droppingPosition !== nextState.droppingPosition\n    );\n  }\n  componentDidUpdate(prevProps /*: Props*/, prevState /*: State*/) {\n    if (!this.state.activeDrag) {\n      const newLayout = this.state.layout;\n      const oldLayout = prevState.layout;\n      this.onLayoutMaybeChanged(newLayout, oldLayout);\n    }\n  }\n\n  /**\n   * Calculates a pixel value for the container.\n   * @return {String} Container height in pixels.\n   */\n  containerHeight() /*: ?string*/{\n    if (!this.props.autoSize) return;\n    const nbRow = (0, _utils.bottom)(this.state.layout);\n    const containerPaddingY = this.props.containerPadding ? this.props.containerPadding[1] : this.props.margin[1];\n    return nbRow * this.props.rowHeight + (nbRow - 1) * this.props.margin[1] + containerPaddingY * 2 + \"px\";\n  }\n  onLayoutMaybeChanged(newLayout /*: Layout*/, oldLayout /*: ?Layout*/) {\n    if (!oldLayout) oldLayout = this.state.layout;\n    if (!(0, _fastEquals.deepEqual)(oldLayout, newLayout)) {\n      this.props.onLayoutChange(newLayout);\n    }\n  }\n  /**\n   * Create a placeholder object.\n   * @return {Element} Placeholder div.\n   */\n  placeholder() /*: ?ReactElement<any>*/{\n    const {\n      activeDrag\n    } = this.state;\n    if (!activeDrag) return null;\n    const {\n      width,\n      cols,\n      margin,\n      containerPadding,\n      rowHeight,\n      maxRows,\n      useCSSTransforms,\n      transformScale\n    } = this.props;\n\n    // {...this.state.activeDrag} is pretty slow, actually\n    return /*#__PURE__*/React.createElement(_GridItem.default, {\n      w: activeDrag.w,\n      h: activeDrag.h,\n      x: activeDrag.x,\n      y: activeDrag.y,\n      i: activeDrag.i,\n      className: `react-grid-placeholder ${this.state.resizing ? \"placeholder-resizing\" : \"\"}`,\n      containerWidth: width,\n      cols: cols,\n      margin: margin,\n      containerPadding: containerPadding || margin,\n      maxRows: maxRows,\n      rowHeight: rowHeight,\n      isDraggable: false,\n      isResizable: false,\n      isBounded: false,\n      useCSSTransforms: useCSSTransforms,\n      transformScale: transformScale\n    }, /*#__PURE__*/React.createElement(\"div\", null));\n  }\n\n  /**\n   * Given a grid item, set its style attributes & surround in a <Draggable>.\n   * @param  {Element} child React element.\n   * @return {Element}       Element wrapped in draggable and properly placed.\n   */\n  processGridItem(child /*: ReactElement<any>*/, isDroppingItem /*: boolean*/) /*: ?ReactElement<any>*/{\n    if (!child || !child.key) return;\n    const l = (0, _utils.getLayoutItem)(this.state.layout, String(child.key));\n    if (!l) return null;\n    const {\n      width,\n      cols,\n      margin,\n      containerPadding,\n      rowHeight,\n      maxRows,\n      isDraggable,\n      isResizable,\n      isBounded,\n      useCSSTransforms,\n      transformScale,\n      draggableCancel,\n      draggableHandle,\n      resizeHandles,\n      resizeHandle\n    } = this.props;\n    const {\n      mounted,\n      droppingPosition\n    } = this.state;\n\n    // Determine user manipulations possible.\n    // If an item is static, it can't be manipulated by default.\n    // Any properties defined directly on the grid item will take precedence.\n    const draggable = typeof l.isDraggable === \"boolean\" ? l.isDraggable : !l.static && isDraggable;\n    const resizable = typeof l.isResizable === \"boolean\" ? l.isResizable : !l.static && isResizable;\n    const resizeHandlesOptions = l.resizeHandles || resizeHandles;\n\n    // isBounded set on child if set on parent, and child is not explicitly false\n    const bounded = draggable && isBounded && l.isBounded !== false;\n    return /*#__PURE__*/React.createElement(_GridItem.default, {\n      containerWidth: width,\n      cols: cols,\n      margin: margin,\n      containerPadding: containerPadding || margin,\n      maxRows: maxRows,\n      rowHeight: rowHeight,\n      cancel: draggableCancel,\n      handle: draggableHandle,\n      onDragStop: this.onDragStop,\n      onDragStart: this.onDragStart,\n      onDrag: this.onDrag,\n      onResizeStart: this.onResizeStart,\n      onResize: this.onResize,\n      onResizeStop: this.onResizeStop,\n      isDraggable: draggable,\n      isResizable: resizable,\n      isBounded: bounded,\n      useCSSTransforms: useCSSTransforms && mounted,\n      usePercentages: !mounted,\n      transformScale: transformScale,\n      w: l.w,\n      h: l.h,\n      x: l.x,\n      y: l.y,\n      i: l.i,\n      minH: l.minH,\n      minW: l.minW,\n      maxH: l.maxH,\n      maxW: l.maxW,\n      static: l.static,\n      droppingPosition: isDroppingItem ? droppingPosition : undefined,\n      resizeHandles: resizeHandlesOptions,\n      resizeHandle: resizeHandle\n    }, child);\n  }\n  render() /*: React.Element<\"div\">*/{\n    const {\n      className,\n      style,\n      isDroppable,\n      innerRef\n    } = this.props;\n    const mergedClassName = (0, _clsx.default)(layoutClassName, className);\n    const mergedStyle = {\n      height: this.containerHeight(),\n      ...style\n    };\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: innerRef,\n      className: mergedClassName,\n      style: mergedStyle,\n      onDrop: isDroppable ? this.onDrop : _utils.noop,\n      onDragLeave: isDroppable ? this.onDragLeave : _utils.noop,\n      onDragEnter: isDroppable ? this.onDragEnter : _utils.noop,\n      onDragOver: isDroppable ? this.onDragOver : _utils.noop\n    }, React.Children.map(this.props.children, child => this.processGridItem(child)), isDroppable && this.state.droppingDOMNode && this.processGridItem(this.state.droppingDOMNode, true), this.placeholder());\n  }\n}\nexports.default = ReactGridLayout;\n// TODO publish internal ReactClass displayName transform\n_defineProperty(ReactGridLayout, \"displayName\", \"ReactGridLayout\");\n// Refactored to another module to make way for preval\n_defineProperty(ReactGridLayout, \"propTypes\", _ReactGridLayoutPropTypes.default);\n_defineProperty(ReactGridLayout, \"defaultProps\", {\n  autoSize: true,\n  cols: 12,\n  className: \"\",\n  style: {},\n  draggableHandle: \"\",\n  draggableCancel: \"\",\n  containerPadding: null,\n  rowHeight: 150,\n  maxRows: Infinity,\n  // infinite vertical growth\n  layout: [],\n  margin: [10, 10],\n  isBounded: false,\n  isDraggable: true,\n  isResizable: true,\n  allowOverlap: false,\n  isDroppable: false,\n  useCSSTransforms: true,\n  transformScale: 1,\n  verticalCompact: true,\n  compactType: \"vertical\",\n  preventCollision: false,\n  droppingItem: {\n    i: \"__dropping-elem__\",\n    h: 1,\n    w: 1\n  },\n  resizeHandles: [\"se\"],\n  onLayoutChange: _utils.noop,\n  onDragStart: _utils.noop,\n  onDrag: _utils.noop,\n  onDragStop: _utils.noop,\n  onResizeStart: _utils.noop,\n  onResize: _utils.noop,\n  onResizeStop: _utils.noop,\n  onDrop: _utils.noop,\n  onDropDragOver: _utils.noop\n});", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.findOrGenerateResponsiveLayout = findOrGenerateResponsiveLayout;\nexports.getBreakpointFromWidth = getBreakpointFromWidth;\nexports.getColsFromBreakpoint = getColsFromBreakpoint;\nexports.sortBreakpoints = sortBreakpoints;\nvar _utils = require(\"./utils\");\n/*:: import type { CompactType, Layout } from \"./utils\";*/\n/*:: export type Breakpoint = string;*/\n/*:: export type DefaultBreakpoints = \"lg\" | \"md\" | \"sm\" | \"xs\" | \"xxs\";*/\n/*:: export type ResponsiveLayout<T: Breakpoint> = {\n  +[breakpoint: T]: Layout\n};*/\n// + indicates read-only\n/*:: export type Breakpoints<T: Breakpoint> = {\n  +[breakpoint: T]: number\n};*/\n/*:: export type OnLayoutChangeCallback = (\n  Layout,\n  { [key: Breakpoint]: Layout }\n) => void;*/\n/**\n * Given a width, find the highest breakpoint that matches is valid for it (width > breakpoint).\n *\n * @param  {Object} breakpoints Breakpoints object (e.g. {lg: 1200, md: 960, ...})\n * @param  {Number} width Screen width.\n * @return {String}       Highest breakpoint that is less than width.\n */\nfunction getBreakpointFromWidth(breakpoints /*: Breakpoints<Breakpoint>*/, width /*: number*/) /*: Breakpoint*/{\n  const sorted = sortBreakpoints(breakpoints);\n  let matching = sorted[0];\n  for (let i = 1, len = sorted.length; i < len; i++) {\n    const breakpointName = sorted[i];\n    if (width > breakpoints[breakpointName]) matching = breakpointName;\n  }\n  return matching;\n}\n\n/**\n * Given a breakpoint, get the # of cols set for it.\n * @param  {String} breakpoint Breakpoint name.\n * @param  {Object} cols       Map of breakpoints to cols.\n * @return {Number}            Number of cols.\n */\nfunction getColsFromBreakpoint(breakpoint /*: Breakpoint*/, cols /*: Breakpoints<Breakpoint>*/) /*: number*/{\n  if (!cols[breakpoint]) {\n    throw new Error(\"ResponsiveReactGridLayout: `cols` entry for breakpoint \" + breakpoint + \" is missing!\");\n  }\n  return cols[breakpoint];\n}\n\n/**\n * Given existing layouts and a new breakpoint, find or generate a new layout.\n *\n * This finds the layout above the new one and generates from it, if it exists.\n *\n * @param  {Object} layouts     Existing layouts.\n * @param  {Array} breakpoints All breakpoints.\n * @param  {String} breakpoint New breakpoint.\n * @param  {String} breakpoint Last breakpoint (for fallback).\n * @param  {Number} cols       Column count at new breakpoint.\n * @param  {Boolean} verticalCompact Whether or not to compact the layout\n *   vertically.\n * @return {Array}             New layout.\n */\nfunction findOrGenerateResponsiveLayout(layouts /*: ResponsiveLayout<Breakpoint>*/, breakpoints /*: Breakpoints<Breakpoint>*/, breakpoint /*: Breakpoint*/, lastBreakpoint /*: Breakpoint*/, cols /*: number*/, compactType /*: CompactType*/) /*: Layout*/{\n  // If it already exists, just return it.\n  if (layouts[breakpoint]) return (0, _utils.cloneLayout)(layouts[breakpoint]);\n  // Find or generate the next layout\n  let layout = layouts[lastBreakpoint];\n  const breakpointsSorted = sortBreakpoints(breakpoints);\n  const breakpointsAbove = breakpointsSorted.slice(breakpointsSorted.indexOf(breakpoint));\n  for (let i = 0, len = breakpointsAbove.length; i < len; i++) {\n    const b = breakpointsAbove[i];\n    if (layouts[b]) {\n      layout = layouts[b];\n      break;\n    }\n  }\n  layout = (0, _utils.cloneLayout)(layout || []); // clone layout so we don't modify existing items\n  return (0, _utils.compact)((0, _utils.correctBounds)(layout, {\n    cols: cols\n  }), compactType, cols);\n}\n\n/**\n * Given breakpoints, return an array of breakpoints sorted by width. This is usually\n * e.g. ['xxs', 'xs', 'sm', ...]\n *\n * @param  {Object} breakpoints Key/value pair of breakpoint names to widths.\n * @return {Array}              Sorted breakpoints.\n */\nfunction sortBreakpoints(breakpoints /*: Breakpoints<Breakpoint>*/) /*: Array<Breakpoint>*/{\n  const keys /*: Array<string>*/ = Object.keys(breakpoints);\n  return keys.sort(function (a, b) {\n    return breakpoints[a] - breakpoints[b];\n  });\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _fastEquals = require(\"fast-equals\");\nvar _utils = require(\"./utils\");\nvar _responsiveUtils = require(\"./responsiveUtils\");\nvar _ReactGridLayout = _interopRequireDefault(require(\"./ReactGridLayout\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); } /*:: import { type Layout, type Pick } from \"./utils\";*/ /*:: import { type ResponsiveLayout, type OnLayoutChangeCallback, type Breakpoints } from \"./responsiveUtils\";*/\n// $FlowFixMe[method-unbinding]\nconst type = obj => Object.prototype.toString.call(obj);\n\n/**\n * Get a value of margin or containerPadding.\n *\n * @param  {Array | Object} param Margin | containerPadding, e.g. [10, 10] | {lg: [10, 10], ...}.\n * @param  {String} breakpoint   Breakpoint: lg, md, sm, xs and etc.\n * @return {Array}\n */\nfunction getIndentationValue /*:: <T: ?[number, number]>*/(param /*: { [key: string]: T } | T*/, breakpoint /*: string*/) /*: T*/{\n  // $FlowIgnore TODO fix this typedef\n  if (param == null) return null;\n  // $FlowIgnore TODO fix this typedef\n  return Array.isArray(param) ? param : param[breakpoint];\n}\n/*:: type State = {\n  layout: Layout,\n  breakpoint: string,\n  cols: number,\n  layouts?: ResponsiveLayout<string>\n};*/\n/*:: type Props<Breakpoint: string = string> = {|\n  ...React.ElementConfig<typeof ReactGridLayout>,\n\n  // Responsive config\n  breakpoint?: ?Breakpoint,\n  breakpoints: Breakpoints<Breakpoint>,\n  cols: { [key: Breakpoint]: number },\n  layouts: ResponsiveLayout<Breakpoint>,\n  width: number,\n  margin: { [key: Breakpoint]: [number, number] } | [number, number],\n  /* prettier-ignore *-/\n  containerPadding: { [key: Breakpoint]: ?[number, number] } | ?[number, number],\n\n  // Callbacks\n  onBreakpointChange: (Breakpoint, cols: number) => void,\n  onLayoutChange: OnLayoutChangeCallback,\n  onWidthChange: (\n    containerWidth: number,\n    margin: [number, number],\n    cols: number,\n    containerPadding: ?[number, number]\n  ) => void\n|};*/\n/*:: type DefaultProps = Pick<\n  Props<>,\n  {|\n    allowOverlap: 0,\n    breakpoints: 0,\n    cols: 0,\n    containerPadding: 0,\n    layouts: 0,\n    margin: 0,\n    onBreakpointChange: 0,\n    onLayoutChange: 0,\n    onWidthChange: 0\n  |}\n>;*/\nclass ResponsiveReactGridLayout extends React.Component\n/*:: <\n  Props<>,\n  State\n>*/\n{\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", this.generateInitialState());\n    // wrap layouts so we do not need to pass layouts to child\n    _defineProperty(this, \"onLayoutChange\", (layout /*: Layout*/) => {\n      this.props.onLayoutChange(layout, {\n        ...this.props.layouts,\n        [this.state.breakpoint]: layout\n      });\n    });\n  }\n  generateInitialState() /*: State*/{\n    const {\n      width,\n      breakpoints,\n      layouts,\n      cols\n    } = this.props;\n    const breakpoint = (0, _responsiveUtils.getBreakpointFromWidth)(breakpoints, width);\n    const colNo = (0, _responsiveUtils.getColsFromBreakpoint)(breakpoint, cols);\n    // verticalCompact compatibility, now deprecated\n    const compactType = this.props.verticalCompact === false ? null : this.props.compactType;\n    // Get the initial layout. This can tricky; we try to generate one however possible if one doesn't exist\n    // for this layout.\n    const initialLayout = (0, _responsiveUtils.findOrGenerateResponsiveLayout)(layouts, breakpoints, breakpoint, breakpoint, colNo, compactType);\n    return {\n      layout: initialLayout,\n      breakpoint: breakpoint,\n      cols: colNo\n    };\n  }\n  static getDerivedStateFromProps(nextProps /*: Props<*>*/, prevState /*: State*/) /*: ?$Shape<State>*/{\n    if (!(0, _fastEquals.deepEqual)(nextProps.layouts, prevState.layouts)) {\n      // Allow parent to set layouts directly.\n      const {\n        breakpoint,\n        cols\n      } = prevState;\n\n      // Since we're setting an entirely new layout object, we must generate a new responsive layout\n      // if one does not exist.\n      const newLayout = (0, _responsiveUtils.findOrGenerateResponsiveLayout)(nextProps.layouts, nextProps.breakpoints, breakpoint, breakpoint, cols, nextProps.compactType);\n      return {\n        layout: newLayout,\n        layouts: nextProps.layouts\n      };\n    }\n    return null;\n  }\n  componentDidUpdate(prevProps /*: Props<*>*/) {\n    // Allow parent to set width or breakpoint directly.\n    if (this.props.width != prevProps.width || this.props.breakpoint !== prevProps.breakpoint || !(0, _fastEquals.deepEqual)(this.props.breakpoints, prevProps.breakpoints) || !(0, _fastEquals.deepEqual)(this.props.cols, prevProps.cols)) {\n      this.onWidthChange(prevProps);\n    }\n  }\n  /**\n   * When the width changes work through breakpoints and reset state with the new width & breakpoint.\n   * Width changes are necessary to figure out the widget widths.\n   */\n  onWidthChange(prevProps /*: Props<*>*/) {\n    const {\n      breakpoints,\n      cols,\n      layouts,\n      compactType\n    } = this.props;\n    const newBreakpoint = this.props.breakpoint || (0, _responsiveUtils.getBreakpointFromWidth)(this.props.breakpoints, this.props.width);\n    const lastBreakpoint = this.state.breakpoint;\n    const newCols /*: number*/ = (0, _responsiveUtils.getColsFromBreakpoint)(newBreakpoint, cols);\n    const newLayouts = {\n      ...layouts\n    };\n\n    // Breakpoint change\n    if (lastBreakpoint !== newBreakpoint || prevProps.breakpoints !== breakpoints || prevProps.cols !== cols) {\n      // Preserve the current layout if the current breakpoint is not present in the next layouts.\n      if (!(lastBreakpoint in newLayouts)) newLayouts[lastBreakpoint] = (0, _utils.cloneLayout)(this.state.layout);\n\n      // Find or generate a new layout.\n      let layout = (0, _responsiveUtils.findOrGenerateResponsiveLayout)(newLayouts, breakpoints, newBreakpoint, lastBreakpoint, newCols, compactType);\n\n      // This adds missing items.\n      layout = (0, _utils.synchronizeLayoutWithChildren)(layout, this.props.children, newCols, compactType, this.props.allowOverlap);\n\n      // Store the new layout.\n      newLayouts[newBreakpoint] = layout;\n\n      // callbacks\n      this.props.onBreakpointChange(newBreakpoint, newCols);\n      this.props.onLayoutChange(layout, newLayouts);\n      this.setState({\n        breakpoint: newBreakpoint,\n        layout: layout,\n        cols: newCols\n      });\n    }\n    const margin = getIndentationValue(this.props.margin, newBreakpoint);\n    const containerPadding = getIndentationValue(this.props.containerPadding, newBreakpoint);\n\n    //call onWidthChange on every change of width, not only on breakpoint changes\n    this.props.onWidthChange(this.props.width, margin, newCols, containerPadding);\n  }\n  render() /*: React.Element<typeof ReactGridLayout>*/{\n    /* eslint-disable no-unused-vars */\n    const {\n      breakpoint,\n      breakpoints,\n      cols,\n      layouts,\n      margin,\n      containerPadding,\n      onBreakpointChange,\n      onLayoutChange,\n      onWidthChange,\n      ...other\n    } = this.props;\n    /* eslint-enable no-unused-vars */\n\n    return /*#__PURE__*/React.createElement(_ReactGridLayout.default, _extends({}, other, {\n      // $FlowIgnore should allow nullable here due to DefaultProps\n      margin: getIndentationValue(margin, this.state.breakpoint),\n      containerPadding: getIndentationValue(containerPadding, this.state.breakpoint),\n      onLayoutChange: this.onLayoutChange,\n      layout: this.state.layout,\n      cols: this.state.cols\n    }));\n  }\n}\nexports.default = ResponsiveReactGridLayout;\n// This should only include propTypes needed in this code; RGL itself\n// will do validation of the rest props passed to it.\n_defineProperty(ResponsiveReactGridLayout, \"propTypes\", {\n  //\n  // Basic props\n  //\n\n  // Optional, but if you are managing width yourself you may want to set the breakpoint\n  // yourself as well.\n  breakpoint: _propTypes.default.string,\n  // {name: pxVal}, e.g. {lg: 1200, md: 996, sm: 768, xs: 480}\n  breakpoints: _propTypes.default.object,\n  allowOverlap: _propTypes.default.bool,\n  // # of cols. This is a breakpoint -> cols map\n  cols: _propTypes.default.object,\n  // # of margin. This is a breakpoint -> margin map\n  // e.g. { lg: [5, 5], md: [10, 10], sm: [15, 15] }\n  // Margin between items [x, y] in px\n  // e.g. [10, 10]\n  margin: _propTypes.default.oneOfType([_propTypes.default.array, _propTypes.default.object]),\n  // # of containerPadding. This is a breakpoint -> containerPadding map\n  // e.g. { lg: [5, 5], md: [10, 10], sm: [15, 15] }\n  // Padding inside the container [x, y] in px\n  // e.g. [10, 10]\n  containerPadding: _propTypes.default.oneOfType([_propTypes.default.array, _propTypes.default.object]),\n  // layouts is an object mapping breakpoints to layouts.\n  // e.g. {lg: Layout, md: Layout, ...}\n  layouts(props /*: Props<>*/, propName /*: string*/) {\n    if (type(props[propName]) !== \"[object Object]\") {\n      throw new Error(\"Layout property must be an object. Received: \" + type(props[propName]));\n    }\n    Object.keys(props[propName]).forEach(key => {\n      if (!(key in props.breakpoints)) {\n        throw new Error(\"Each key in layouts must align with a key in breakpoints.\");\n      }\n      (0, _utils.validateLayout)(props.layouts[key], \"layouts.\" + key);\n    });\n  },\n  // The width of this component.\n  // Required in this propTypes stanza because generateInitialState() will fail without it.\n  width: _propTypes.default.number.isRequired,\n  //\n  // Callbacks\n  //\n\n  // Calls back with breakpoint and new # cols\n  onBreakpointChange: _propTypes.default.func,\n  // Callback so you can save the layout.\n  // Calls back with (currentLayout, allLayouts). allLayouts are keyed by breakpoint.\n  onLayoutChange: _propTypes.default.func,\n  // Calls back with (containerWidth, margin, cols, containerPadding)\n  onWidthChange: _propTypes.default.func\n});\n_defineProperty(ResponsiveReactGridLayout, \"defaultProps\", {\n  breakpoints: {\n    lg: 1200,\n    md: 996,\n    sm: 768,\n    xs: 480,\n    xxs: 0\n  },\n  cols: {\n    lg: 12,\n    md: 10,\n    sm: 6,\n    xs: 4,\n    xxs: 2\n  },\n  containerPadding: {\n    lg: null,\n    md: null,\n    sm: null,\n    xs: null,\n    xxs: null\n  },\n  layouts: {},\n  margin: [10, 10],\n  allowOverlap: false,\n  onBreakpointChange: _utils.noop,\n  onLayoutChange: _utils.noop,\n  onWidthChange: _utils.noop\n});", "/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */\r\n/* eslint-disable require-jsdoc, valid-jsdoc */\r\nvar MapShim = (function () {\r\n    if (typeof Map !== 'undefined') {\r\n        return Map;\r\n    }\r\n    /**\r\n     * Returns index in provided array that matches the specified key.\r\n     *\r\n     * @param {Array<Array>} arr\r\n     * @param {*} key\r\n     * @returns {number}\r\n     */\r\n    function getIndex(arr, key) {\r\n        var result = -1;\r\n        arr.some(function (entry, index) {\r\n            if (entry[0] === key) {\r\n                result = index;\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n        return result;\r\n    }\r\n    return /** @class */ (function () {\r\n        function class_1() {\r\n            this.__entries__ = [];\r\n        }\r\n        Object.defineProperty(class_1.prototype, \"size\", {\r\n            /**\r\n             * @returns {boolean}\r\n             */\r\n            get: function () {\r\n                return this.__entries__.length;\r\n            },\r\n            enumerable: true,\r\n            configurable: true\r\n        });\r\n        /**\r\n         * @param {*} key\r\n         * @returns {*}\r\n         */\r\n        class_1.prototype.get = function (key) {\r\n            var index = getIndex(this.__entries__, key);\r\n            var entry = this.__entries__[index];\r\n            return entry && entry[1];\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @param {*} value\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.set = function (key, value) {\r\n            var index = getIndex(this.__entries__, key);\r\n            if (~index) {\r\n                this.__entries__[index][1] = value;\r\n            }\r\n            else {\r\n                this.__entries__.push([key, value]);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.delete = function (key) {\r\n            var entries = this.__entries__;\r\n            var index = getIndex(entries, key);\r\n            if (~index) {\r\n                entries.splice(index, 1);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.has = function (key) {\r\n            return !!~getIndex(this.__entries__, key);\r\n        };\r\n        /**\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.clear = function () {\r\n            this.__entries__.splice(0);\r\n        };\r\n        /**\r\n         * @param {Function} callback\r\n         * @param {*} [ctx=null]\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.forEach = function (callback, ctx) {\r\n            if (ctx === void 0) { ctx = null; }\r\n            for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {\r\n                var entry = _a[_i];\r\n                callback.call(ctx, entry[1], entry[0]);\r\n            }\r\n        };\r\n        return class_1;\r\n    }());\r\n})();\n\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */\r\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document;\n\n// Returns global object of a current environment.\r\nvar global$1 = (function () {\r\n    if (typeof global !== 'undefined' && global.Math === Math) {\r\n        return global;\r\n    }\r\n    if (typeof self !== 'undefined' && self.Math === Math) {\r\n        return self;\r\n    }\r\n    if (typeof window !== 'undefined' && window.Math === Math) {\r\n        return window;\r\n    }\r\n    // eslint-disable-next-line no-new-func\r\n    return Function('return this')();\r\n})();\n\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */\r\nvar requestAnimationFrame$1 = (function () {\r\n    if (typeof requestAnimationFrame === 'function') {\r\n        // It's required to use a bounded function because IE sometimes throws\r\n        // an \"Invalid calling object\" error if rAF is invoked without the global\r\n        // object on the left hand side.\r\n        return requestAnimationFrame.bind(global$1);\r\n    }\r\n    return function (callback) { return setTimeout(function () { return callback(Date.now()); }, 1000 / 60); };\r\n})();\n\n// Defines minimum timeout before adding a trailing call.\r\nvar trailingTimeout = 2;\r\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */\r\nfunction throttle (callback, delay) {\r\n    var leadingCall = false, trailingCall = false, lastCallTime = 0;\r\n    /**\r\n     * Invokes the original callback function and schedules new invocation if\r\n     * the \"proxy\" was called during current request.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function resolvePending() {\r\n        if (leadingCall) {\r\n            leadingCall = false;\r\n            callback();\r\n        }\r\n        if (trailingCall) {\r\n            proxy();\r\n        }\r\n    }\r\n    /**\r\n     * Callback invoked after the specified delay. It will further postpone\r\n     * invocation of the original function delegating it to the\r\n     * requestAnimationFrame.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function timeoutCallback() {\r\n        requestAnimationFrame$1(resolvePending);\r\n    }\r\n    /**\r\n     * Schedules invocation of the original function.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function proxy() {\r\n        var timeStamp = Date.now();\r\n        if (leadingCall) {\r\n            // Reject immediately following calls.\r\n            if (timeStamp - lastCallTime < trailingTimeout) {\r\n                return;\r\n            }\r\n            // Schedule new call to be in invoked when the pending one is resolved.\r\n            // This is important for \"transitions\" which never actually start\r\n            // immediately so there is a chance that we might miss one if change\r\n            // happens amids the pending invocation.\r\n            trailingCall = true;\r\n        }\r\n        else {\r\n            leadingCall = true;\r\n            trailingCall = false;\r\n            setTimeout(timeoutCallback, delay);\r\n        }\r\n        lastCallTime = timeStamp;\r\n    }\r\n    return proxy;\r\n}\n\n// Minimum delay before invoking the update of observers.\r\nvar REFRESH_DELAY = 20;\r\n// A list of substrings of CSS properties used to find transition events that\r\n// might affect dimensions of observed elements.\r\nvar transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight'];\r\n// Check if MutationObserver is available.\r\nvar mutationObserverSupported = typeof MutationObserver !== 'undefined';\r\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */\r\nvar ResizeObserverController = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserverController.\r\n     *\r\n     * @private\r\n     */\r\n    function ResizeObserverController() {\r\n        /**\r\n         * Indicates whether DOM listeners have been added.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.connected_ = false;\r\n        /**\r\n         * Tells that controller has subscribed for Mutation Events.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.mutationEventsAdded_ = false;\r\n        /**\r\n         * Keeps reference to the instance of MutationObserver.\r\n         *\r\n         * @private {MutationObserver}\r\n         */\r\n        this.mutationsObserver_ = null;\r\n        /**\r\n         * A list of connected observers.\r\n         *\r\n         * @private {Array<ResizeObserverSPI>}\r\n         */\r\n        this.observers_ = [];\r\n        this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\r\n        this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\r\n    }\r\n    /**\r\n     * Adds observer to observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be added.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.addObserver = function (observer) {\r\n        if (!~this.observers_.indexOf(observer)) {\r\n            this.observers_.push(observer);\r\n        }\r\n        // Add listeners if they haven't been added yet.\r\n        if (!this.connected_) {\r\n            this.connect_();\r\n        }\r\n    };\r\n    /**\r\n     * Removes observer from observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.removeObserver = function (observer) {\r\n        var observers = this.observers_;\r\n        var index = observers.indexOf(observer);\r\n        // Remove observer if it's present in registry.\r\n        if (~index) {\r\n            observers.splice(index, 1);\r\n        }\r\n        // Remove listeners if controller has no connected observers.\r\n        if (!observers.length && this.connected_) {\r\n            this.disconnect_();\r\n        }\r\n    };\r\n    /**\r\n     * Invokes the update of observers. It will continue running updates insofar\r\n     * it detects changes.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.refresh = function () {\r\n        var changesDetected = this.updateObservers_();\r\n        // Continue running updates if changes have been detected as there might\r\n        // be future ones caused by CSS transitions.\r\n        if (changesDetected) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Updates every observer from observers list and notifies them of queued\r\n     * entries.\r\n     *\r\n     * @private\r\n     * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n     *      dimensions of it's elements.\r\n     */\r\n    ResizeObserverController.prototype.updateObservers_ = function () {\r\n        // Collect observers that have active observations.\r\n        var activeObservers = this.observers_.filter(function (observer) {\r\n            return observer.gatherActive(), observer.hasActive();\r\n        });\r\n        // Deliver notifications in a separate cycle in order to avoid any\r\n        // collisions between observers, e.g. when multiple instances of\r\n        // ResizeObserver are tracking the same element and the callback of one\r\n        // of them changes content dimensions of the observed target. Sometimes\r\n        // this may result in notifications being blocked for the rest of observers.\r\n        activeObservers.forEach(function (observer) { return observer.broadcastActive(); });\r\n        return activeObservers.length > 0;\r\n    };\r\n    /**\r\n     * Initializes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.connect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already added.\r\n        if (!isBrowser || this.connected_) {\r\n            return;\r\n        }\r\n        // Subscription to the \"Transitionend\" event is used as a workaround for\r\n        // delayed transitions. This way it's possible to capture at least the\r\n        // final state of an element.\r\n        document.addEventListener('transitionend', this.onTransitionEnd_);\r\n        window.addEventListener('resize', this.refresh);\r\n        if (mutationObserverSupported) {\r\n            this.mutationsObserver_ = new MutationObserver(this.refresh);\r\n            this.mutationsObserver_.observe(document, {\r\n                attributes: true,\r\n                childList: true,\r\n                characterData: true,\r\n                subtree: true\r\n            });\r\n        }\r\n        else {\r\n            document.addEventListener('DOMSubtreeModified', this.refresh);\r\n            this.mutationEventsAdded_ = true;\r\n        }\r\n        this.connected_ = true;\r\n    };\r\n    /**\r\n     * Removes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.disconnect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already removed.\r\n        if (!isBrowser || !this.connected_) {\r\n            return;\r\n        }\r\n        document.removeEventListener('transitionend', this.onTransitionEnd_);\r\n        window.removeEventListener('resize', this.refresh);\r\n        if (this.mutationsObserver_) {\r\n            this.mutationsObserver_.disconnect();\r\n        }\r\n        if (this.mutationEventsAdded_) {\r\n            document.removeEventListener('DOMSubtreeModified', this.refresh);\r\n        }\r\n        this.mutationsObserver_ = null;\r\n        this.mutationEventsAdded_ = false;\r\n        this.connected_ = false;\r\n    };\r\n    /**\r\n     * \"Transitionend\" event handler.\r\n     *\r\n     * @private\r\n     * @param {TransitionEvent} event\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {\r\n        var _b = _a.propertyName, propertyName = _b === void 0 ? '' : _b;\r\n        // Detect whether transition may affect dimensions of an element.\r\n        var isReflowProperty = transitionKeys.some(function (key) {\r\n            return !!~propertyName.indexOf(key);\r\n        });\r\n        if (isReflowProperty) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Returns instance of the ResizeObserverController.\r\n     *\r\n     * @returns {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.getInstance = function () {\r\n        if (!this.instance_) {\r\n            this.instance_ = new ResizeObserverController();\r\n        }\r\n        return this.instance_;\r\n    };\r\n    /**\r\n     * Holds reference to the controller's instance.\r\n     *\r\n     * @private {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.instance_ = null;\r\n    return ResizeObserverController;\r\n}());\n\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */\r\nvar defineConfigurable = (function (target, props) {\r\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\r\n        var key = _a[_i];\r\n        Object.defineProperty(target, key, {\r\n            value: props[key],\r\n            enumerable: false,\r\n            writable: false,\r\n            configurable: true\r\n        });\r\n    }\r\n    return target;\r\n});\n\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */\r\nvar getWindowOf = (function (target) {\r\n    // Assume that the element is an instance of Node, which means that it\r\n    // has the \"ownerDocument\" property from which we can retrieve a\r\n    // corresponding global object.\r\n    var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;\r\n    // Return the local global object if it's not possible extract one from\r\n    // provided element.\r\n    return ownerGlobal || global$1;\r\n});\n\n// Placeholder of an empty content rectangle.\r\nvar emptyRect = createRectInit(0, 0, 0, 0);\r\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */\r\nfunction toFloat(value) {\r\n    return parseFloat(value) || 0;\r\n}\r\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */\r\nfunction getBordersSize(styles) {\r\n    var positions = [];\r\n    for (var _i = 1; _i < arguments.length; _i++) {\r\n        positions[_i - 1] = arguments[_i];\r\n    }\r\n    return positions.reduce(function (size, position) {\r\n        var value = styles['border-' + position + '-width'];\r\n        return size + toFloat(value);\r\n    }, 0);\r\n}\r\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */\r\nfunction getPaddings(styles) {\r\n    var positions = ['top', 'right', 'bottom', 'left'];\r\n    var paddings = {};\r\n    for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {\r\n        var position = positions_1[_i];\r\n        var value = styles['padding-' + position];\r\n        paddings[position] = toFloat(value);\r\n    }\r\n    return paddings;\r\n}\r\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getSVGContentRect(target) {\r\n    var bbox = target.getBBox();\r\n    return createRectInit(0, 0, bbox.width, bbox.height);\r\n}\r\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getHTMLElementContentRect(target) {\r\n    // Client width & height properties can't be\r\n    // used exclusively as they provide rounded values.\r\n    var clientWidth = target.clientWidth, clientHeight = target.clientHeight;\r\n    // By this condition we can catch all non-replaced inline, hidden and\r\n    // detached elements. Though elements with width & height properties less\r\n    // than 0.5 will be discarded as well.\r\n    //\r\n    // Without it we would need to implement separate methods for each of\r\n    // those cases and it's not possible to perform a precise and performance\r\n    // effective test for hidden elements. E.g. even jQuery's ':visible' filter\r\n    // gives wrong results for elements with width & height less than 0.5.\r\n    if (!clientWidth && !clientHeight) {\r\n        return emptyRect;\r\n    }\r\n    var styles = getWindowOf(target).getComputedStyle(target);\r\n    var paddings = getPaddings(styles);\r\n    var horizPad = paddings.left + paddings.right;\r\n    var vertPad = paddings.top + paddings.bottom;\r\n    // Computed styles of width & height are being used because they are the\r\n    // only dimensions available to JS that contain non-rounded values. It could\r\n    // be possible to utilize the getBoundingClientRect if only it's data wasn't\r\n    // affected by CSS transformations let alone paddings, borders and scroll bars.\r\n    var width = toFloat(styles.width), height = toFloat(styles.height);\r\n    // Width & height include paddings and borders when the 'border-box' box\r\n    // model is applied (except for IE).\r\n    if (styles.boxSizing === 'border-box') {\r\n        // Following conditions are required to handle Internet Explorer which\r\n        // doesn't include paddings and borders to computed CSS dimensions.\r\n        //\r\n        // We can say that if CSS dimensions + paddings are equal to the \"client\"\r\n        // properties then it's either IE, and thus we don't need to subtract\r\n        // anything, or an element merely doesn't have paddings/borders styles.\r\n        if (Math.round(width + horizPad) !== clientWidth) {\r\n            width -= getBordersSize(styles, 'left', 'right') + horizPad;\r\n        }\r\n        if (Math.round(height + vertPad) !== clientHeight) {\r\n            height -= getBordersSize(styles, 'top', 'bottom') + vertPad;\r\n        }\r\n    }\r\n    // Following steps can't be applied to the document's root element as its\r\n    // client[Width/Height] properties represent viewport area of the window.\r\n    // Besides, it's as well not necessary as the <html> itself neither has\r\n    // rendered scroll bars nor it can be clipped.\r\n    if (!isDocumentElement(target)) {\r\n        // In some browsers (only in Firefox, actually) CSS width & height\r\n        // include scroll bars size which can be removed at this step as scroll\r\n        // bars are the only difference between rounded dimensions + paddings\r\n        // and \"client\" properties, though that is not always true in Chrome.\r\n        var vertScrollbar = Math.round(width + horizPad) - clientWidth;\r\n        var horizScrollbar = Math.round(height + vertPad) - clientHeight;\r\n        // Chrome has a rather weird rounding of \"client\" properties.\r\n        // E.g. for an element with content width of 314.2px it sometimes gives\r\n        // the client width of 315px and for the width of 314.7px it may give\r\n        // 314px. And it doesn't happen all the time. So just ignore this delta\r\n        // as a non-relevant.\r\n        if (Math.abs(vertScrollbar) !== 1) {\r\n            width -= vertScrollbar;\r\n        }\r\n        if (Math.abs(horizScrollbar) !== 1) {\r\n            height -= horizScrollbar;\r\n        }\r\n    }\r\n    return createRectInit(paddings.left, paddings.top, width, height);\r\n}\r\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nvar isSVGGraphicsElement = (function () {\r\n    // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\r\n    // interface.\r\n    if (typeof SVGGraphicsElement !== 'undefined') {\r\n        return function (target) { return target instanceof getWindowOf(target).SVGGraphicsElement; };\r\n    }\r\n    // If it's so, then check that element is at least an instance of the\r\n    // SVGElement and that it has the \"getBBox\" method.\r\n    // eslint-disable-next-line no-extra-parens\r\n    return function (target) { return (target instanceof getWindowOf(target).SVGElement &&\r\n        typeof target.getBBox === 'function'); };\r\n})();\r\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nfunction isDocumentElement(target) {\r\n    return target === getWindowOf(target).document.documentElement;\r\n}\r\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getContentRect(target) {\r\n    if (!isBrowser) {\r\n        return emptyRect;\r\n    }\r\n    if (isSVGGraphicsElement(target)) {\r\n        return getSVGContentRect(target);\r\n    }\r\n    return getHTMLElementContentRect(target);\r\n}\r\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */\r\nfunction createReadOnlyRect(_a) {\r\n    var x = _a.x, y = _a.y, width = _a.width, height = _a.height;\r\n    // If DOMRectReadOnly is available use it as a prototype for the rectangle.\r\n    var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;\r\n    var rect = Object.create(Constr.prototype);\r\n    // Rectangle's properties are not writable and non-enumerable.\r\n    defineConfigurable(rect, {\r\n        x: x, y: y, width: width, height: height,\r\n        top: y,\r\n        right: x + width,\r\n        bottom: height + y,\r\n        left: x\r\n    });\r\n    return rect;\r\n}\r\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction createRectInit(x, y, width, height) {\r\n    return { x: x, y: y, width: width, height: height };\r\n}\n\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */\r\nvar ResizeObservation = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObservation.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     */\r\n    function ResizeObservation(target) {\r\n        /**\r\n         * Broadcasted width of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastWidth = 0;\r\n        /**\r\n         * Broadcasted height of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastHeight = 0;\r\n        /**\r\n         * Reference to the last observed content rectangle.\r\n         *\r\n         * @private {DOMRectInit}\r\n         */\r\n        this.contentRect_ = createRectInit(0, 0, 0, 0);\r\n        this.target = target;\r\n    }\r\n    /**\r\n     * Updates content rectangle and tells whether it's width or height properties\r\n     * have changed since the last broadcast.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObservation.prototype.isActive = function () {\r\n        var rect = getContentRect(this.target);\r\n        this.contentRect_ = rect;\r\n        return (rect.width !== this.broadcastWidth ||\r\n            rect.height !== this.broadcastHeight);\r\n    };\r\n    /**\r\n     * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n     * from the corresponding properties of the last observed content rectangle.\r\n     *\r\n     * @returns {DOMRectInit} Last observed content rectangle.\r\n     */\r\n    ResizeObservation.prototype.broadcastRect = function () {\r\n        var rect = this.contentRect_;\r\n        this.broadcastWidth = rect.width;\r\n        this.broadcastHeight = rect.height;\r\n        return rect;\r\n    };\r\n    return ResizeObservation;\r\n}());\n\nvar ResizeObserverEntry = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObserverEntry.\r\n     *\r\n     * @param {Element} target - Element that is being observed.\r\n     * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n     */\r\n    function ResizeObserverEntry(target, rectInit) {\r\n        var contentRect = createReadOnlyRect(rectInit);\r\n        // According to the specification following properties are not writable\r\n        // and are also not enumerable in the native implementation.\r\n        //\r\n        // Property accessors are not being used as they'd require to define a\r\n        // private WeakMap storage which may cause memory leaks in browsers that\r\n        // don't support this type of collections.\r\n        defineConfigurable(this, { target: target, contentRect: contentRect });\r\n    }\r\n    return ResizeObserverEntry;\r\n}());\n\nvar ResizeObserverSPI = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n     *      when one of the observed elements changes it's content dimensions.\r\n     * @param {ResizeObserverController} controller - Controller instance which\r\n     *      is responsible for the updates of observer.\r\n     * @param {ResizeObserver} callbackCtx - Reference to the public\r\n     *      ResizeObserver instance which will be passed to callback function.\r\n     */\r\n    function ResizeObserverSPI(callback, controller, callbackCtx) {\r\n        /**\r\n         * Collection of resize observations that have detected changes in dimensions\r\n         * of elements.\r\n         *\r\n         * @private {Array<ResizeObservation>}\r\n         */\r\n        this.activeObservations_ = [];\r\n        /**\r\n         * Registry of the ResizeObservation instances.\r\n         *\r\n         * @private {Map<Element, ResizeObservation>}\r\n         */\r\n        this.observations_ = new MapShim();\r\n        if (typeof callback !== 'function') {\r\n            throw new TypeError('The callback provided as parameter 1 is not a function.');\r\n        }\r\n        this.callback_ = callback;\r\n        this.controller_ = controller;\r\n        this.callbackCtx_ = callbackCtx;\r\n    }\r\n    /**\r\n     * Starts observing provided element.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.observe = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is already being observed.\r\n        if (observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.set(target, new ResizeObservation(target));\r\n        this.controller_.addObserver(this);\r\n        // Force the update of observations.\r\n        this.controller_.refresh();\r\n    };\r\n    /**\r\n     * Stops observing provided element.\r\n     *\r\n     * @param {Element} target - Element to stop observing.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.unobserve = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is not being observed.\r\n        if (!observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.delete(target);\r\n        if (!observations.size) {\r\n            this.controller_.removeObserver(this);\r\n        }\r\n    };\r\n    /**\r\n     * Stops observing all elements.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.disconnect = function () {\r\n        this.clearActive();\r\n        this.observations_.clear();\r\n        this.controller_.removeObserver(this);\r\n    };\r\n    /**\r\n     * Collects observation instances the associated element of which has changed\r\n     * it's content rectangle.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.gatherActive = function () {\r\n        var _this = this;\r\n        this.clearActive();\r\n        this.observations_.forEach(function (observation) {\r\n            if (observation.isActive()) {\r\n                _this.activeObservations_.push(observation);\r\n            }\r\n        });\r\n    };\r\n    /**\r\n     * Invokes initial callback function with a list of ResizeObserverEntry\r\n     * instances collected from active resize observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.broadcastActive = function () {\r\n        // Do nothing if observer doesn't have active observations.\r\n        if (!this.hasActive()) {\r\n            return;\r\n        }\r\n        var ctx = this.callbackCtx_;\r\n        // Create ResizeObserverEntry instance for every active observation.\r\n        var entries = this.activeObservations_.map(function (observation) {\r\n            return new ResizeObserverEntry(observation.target, observation.broadcastRect());\r\n        });\r\n        this.callback_.call(ctx, entries, ctx);\r\n        this.clearActive();\r\n    };\r\n    /**\r\n     * Clears the collection of active observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.clearActive = function () {\r\n        this.activeObservations_.splice(0);\r\n    };\r\n    /**\r\n     * Tells whether observer has active observations.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObserverSPI.prototype.hasActive = function () {\r\n        return this.activeObservations_.length > 0;\r\n    };\r\n    return ResizeObserverSPI;\r\n}());\n\n// Registry of internal observers. If WeakMap is not available use current shim\r\n// for the Map collection as it has all required methods and because WeakMap\r\n// can't be fully polyfilled anyway.\r\nvar observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();\r\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */\r\nvar ResizeObserver = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n     *      dimensions of the observed elements change.\r\n     */\r\n    function ResizeObserver(callback) {\r\n        if (!(this instanceof ResizeObserver)) {\r\n            throw new TypeError('Cannot call a class as a function.');\r\n        }\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        var controller = ResizeObserverController.getInstance();\r\n        var observer = new ResizeObserverSPI(callback, controller, this);\r\n        observers.set(this, observer);\r\n    }\r\n    return ResizeObserver;\r\n}());\r\n// Expose public methods of ResizeObserver.\r\n[\r\n    'observe',\r\n    'unobserve',\r\n    'disconnect'\r\n].forEach(function (method) {\r\n    ResizeObserver.prototype[method] = function () {\r\n        var _a;\r\n        return (_a = observers.get(this))[method].apply(_a, arguments);\r\n    };\r\n});\n\nvar index = (function () {\r\n    // Export existing implementation if available.\r\n    if (typeof global$1.ResizeObserver !== 'undefined') {\r\n        return global$1.ResizeObserver;\r\n    }\r\n    return ResizeObserver;\r\n})();\n\nexport default index;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = WidthProvideRGL;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _resizeObserverPolyfill = _interopRequireDefault(require(\"resize-observer-polyfill\"));\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/*:: import type { ReactRef } from \"../ReactGridLayoutPropTypes\";*/\n/*:: type WPDefaultProps = {|\n  measureBeforeMount: boolean\n|};*/\n/*:: type WPProps = {|\n  className?: string,\n  style?: Object,\n  ...WPDefaultProps\n|};*/\n// eslint-disable-next-line no-unused-vars\n/*:: type WPState = {|\n  width: number\n|};*/\n/*:: type ComposedProps<Config> = {|\n  ...Config,\n  measureBeforeMount?: boolean,\n  className?: string,\n  style?: Object,\n  width?: number\n|};*/\nconst layoutClassName = \"react-grid-layout\";\n\n/*\n * A simple HOC that provides facility for listening to container resizes.\n *\n * The Flow type is pretty janky here. I can't just spread `WPProps` into this returned object - I wish I could - but it triggers\n * a flow bug of some sort that causes it to stop typechecking.\n */\nfunction WidthProvideRGL /*:: <Config>*/(ComposedComponent /*: React.AbstractComponent<Config>*/) /*: React.AbstractComponent<ComposedProps<Config>>*/{\n  var _WidthProvider;\n  return _WidthProvider = class WidthProvider extends React.Component\n  /*:: <\n      ComposedProps<Config>,\n      WPState\n    >*/\n  {\n    constructor() {\n      super(...arguments);\n      _defineProperty(this, \"state\", {\n        width: 1280\n      });\n      _defineProperty(this, \"elementRef\", /*#__PURE__*/React.createRef());\n      _defineProperty(this, \"mounted\", false);\n      _defineProperty(this, \"resizeObserver\", void 0);\n    }\n    componentDidMount() {\n      this.mounted = true;\n      this.resizeObserver = new _resizeObserverPolyfill.default(entries => {\n        const node = this.elementRef.current;\n        if (node instanceof HTMLElement) {\n          const width = entries[0].contentRect.width;\n          this.setState({\n            width\n          });\n        }\n      });\n      const node = this.elementRef.current;\n      if (node instanceof HTMLElement) {\n        this.resizeObserver.observe(node);\n      }\n    }\n    componentWillUnmount() {\n      this.mounted = false;\n      const node = this.elementRef.current;\n      if (node instanceof HTMLElement) {\n        this.resizeObserver.unobserve(node);\n      }\n      this.resizeObserver.disconnect();\n    }\n    render() {\n      const {\n        measureBeforeMount,\n        ...rest\n      } = this.props;\n      if (measureBeforeMount && !this.mounted) {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: (0, _clsx.default)(this.props.className, layoutClassName),\n          style: this.props.style\n          // $FlowIgnore ref types\n          ,\n          ref: this.elementRef\n        });\n      }\n      return /*#__PURE__*/React.createElement(ComposedComponent, _extends({\n        innerRef: this.elementRef\n      }, rest, this.state));\n    }\n  }, _defineProperty(_WidthProvider, \"defaultProps\", {\n    measureBeforeMount: false\n  }), _defineProperty(_WidthProvider, \"propTypes\", {\n    // If true, will not render children until mounted. Useful for getting the exact width before\n    // rendering, to prevent any unsightly resizing.\n    measureBeforeMount: _propTypes.default.bool\n  }), _WidthProvider;\n}", "module.exports = require(\"./build/ReactGridLayout\").default;\nmodule.exports.utils = require(\"./build/utils\");\nmodule.exports.calculateUtils = require(\"./build/calculateUtils\");\nmodule.exports.Responsive =\n  require(\"./build/ResponsiveReactGridLayout\").default;\nmodule.exports.Responsive.utils = require(\"./build/responsiveUtils\");\nmodule.exports.WidthProvider =\n  require(\"./build/components/WidthProvider\").default;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAUM,eAAU,2BACd,YAAoC;AAEpC,eAAO,SAAS,QACd,GACA,GACA,cACA,cACA,UACA,UACA,MAAU;AAEV,iBAAO,WAAW,GAAG,GAAG,IAAI;QAC9B;MACF;AAOM,eAAU,iBAEd,eAA4B;AAC5B,eAAO,SAAS,WACd,GACA,GACA,SACA,OAAwB;AAExB,cAAI,CAAC,KAAK,CAAC,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC9D,mBAAO,cAAc,GAAG,GAAG,SAAS,KAAK;UAC1C;AAED,cAAM,UAAU,MAAM,IAAI,CAAC;AAC3B,cAAM,UAAU,MAAM,IAAI,CAAC;AAE3B,cAAI,WAAW,SAAS;AACtB,mBAAO,YAAY,KAAK,YAAY;UACrC;AAED,gBAAM,IAAI,GAAG,CAAC;AACd,gBAAM,IAAI,GAAG,CAAC;AAEd,cAAM,SAAS,cAAc,GAAG,GAAG,SAAS,KAAK;AAEjD,gBAAM,OAAO,CAAC;AACd,gBAAM,OAAO,CAAC;AAEd,iBAAO;QACT;MACF;AASgB,eAAA,MAA0C,GAAM,GAAI;AAClE,YAAM,SAA8B,CAAA;AAEpC,iBAAW,OAAO,GAAG;AACnB,iBAAO,GAAG,IAAI,EAAE,GAAG;QACpB;AAED,iBAAW,OAAO,GAAG;AACnB,iBAAO,GAAG,IAAI,EAAE,GAAG;QACpB;AAED,eAAO;MACT;AAQM,eAAU,cAAc,OAAU;AACtC,eAAO,MAAM,gBAAgB,UAAU,MAAM,eAAe;MAC9D;AAKM,eAAU,cAAc,OAAU;AACtC,eAAO,OAAO,MAAM,SAAS;MAC/B;AAKgB,eAAA,mBAAmB,GAAQ,GAAM;AAC/C,eAAO,MAAM,KAAM,MAAM,KAAK,MAAM;MACtC;ACnGA,UAAM,gBAAgB;AACtB,UAAM,cAAc;AACpB,UAAM,WAAW;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU;AAChB,UAAM,aAAa;AACnB,UAAM,aAAa;AACnB,UAAM,UAAU;AAChB,UAAM,aAAa;AAEX,UAAA,WAAa,OAAO,UAAS;AAE/B,eAAU,iBAAuB,IAQA;AAPrC,YAAAA,kBAAc,GAAA,gBACdC,iBAAa,GAAA,eACbC,gBAAY,GAAA,cACZC,mBAAe,GAAA,iBACfC,mBAAe,GAAA,iBACfC,gBAAY,GAAA,cACZ,sBAAmB,GAAA;AAEnB,YAAM,UAAU,oBAAoB,UAAsC;AAK1E,iBAAS,WAAW,GAAQ,GAAQ,MAAU;AAE5C,cAAI,MAAM,GAAG;AACX,mBAAO;UACR;AAMD,cAAI,CAAC,KAAK,CAAC,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC9D,mBAAO,MAAM,KAAK,MAAM;UACzB;AAcD,cAAI,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AACxC,mBAAOF,iBAAgB,GAAG,GAAG,SAAS,IAAI;UAC3C;AAKD,cAAM,SAAS,MAAM,QAAQ,CAAC;AAC9B,cAAM,SAAS,MAAM,QAAQ,CAAC;AAE9B,cAAI,UAAU,QAAQ;AACpB,mBAAO,WAAW,UAAUH,gBAAe,GAAG,GAAG,SAAS,IAAI;UAC/D;AAMD,cAAM,OAAO,SAAS,KAAK,CAAC;AAE5B,cAAI,SAAS,SAAS,KAAK,CAAC,GAAG;AAC7B,mBAAO;UACR;AAED,cAAI,SAAS,UAAU;AAGrB,mBAAOC,eAAc,GAAG,GAAG,SAAS,IAAI;UACzC;AAED,cAAI,SAAS,aAAa;AACxB,mBAAOG,iBAAgB,GAAG,GAAG,SAAS,IAAI;UAC3C;AAED,cAAI,SAAS,SAAS;AACpB,mBAAOF,cAAa,GAAG,GAAG,SAAS,IAAI;UACxC;AAED,cAAI,SAAS,SAAS;AACpB,mBAAOG,cAAa,GAAG,GAAG,SAAS,IAAI;UACxC;AAKD,cAAI,SAAS,cAAc,SAAS,eAAe;AAGjD,mBAAO,cAAc,CAAC,KAAK,cAAc,CAAC,IACtC,QACAF,iBAAgB,GAAG,GAAG,SAAS,IAAI;UACxC;AAKD,cAAI,SAAS,eAAe,SAAS,cAAc,SAAS,YAAY;AACtE,mBAAO,mBAAmB,EAAE,QAAO,GAAI,EAAE,QAAO,CAAE;UACnD;AAaD,iBAAO;;AAGT,eAAO;MACT;AC/HM,eAAU,eACd,GACA,GACA,SACA,MAAS;AAET,YAAIG,SAAQ,EAAE;AAEd,YAAI,EAAE,WAAWA,QAAO;AACtB,iBAAO;QACR;AAMD,eAAOA,WAAU,GAAG;AAClB,cAAI,CAAC,QAAQ,EAAEA,MAAK,GAAG,EAAEA,MAAK,GAAGA,QAAOA,QAAO,GAAG,GAAG,IAAI,GAAG;AAC1D,mBAAO;UACR;QACF;AAED,eAAO;MACT;AAKO,UAAM,yBAAyB,iBAAiB,cAAc;AC1BrD,eAAA,cAAc,GAAS,GAAO;AAC5C,eAAO,mBAAmB,EAAE,QAAO,GAAI,EAAE,QAAO,CAAE;MACpD;ACJM,eAAU,aACd,GACA,GACA,SACA,MAAS;AAET,YAAI,eAAe,EAAE,SAAS,EAAE;AAEhC,YAAI,CAAC,cAAc;AACjB,iBAAO;QACR;AAED,YAAI,CAAC,EAAE,MAAM;AACX,iBAAO;QACR;AAQD,YAAM,iBAAuC,CAAA;AAE7C,YAAI,SAAS;AAEb,UAAE,QAAQ,SAAC,QAAQ,MAAI;AACrB,cAAI,CAAC,cAAc;AACjB;UACD;AAED,cAAI,WAAW;AACf,cAAI,cAAc;AAElB,YAAE,QAAQ,SAAC,QAAQ,MAAI;AACrB,gBACE,CAAC,YACD,CAAC,eAAe,WAAW,MAC1B,WACC,QAAQ,MAAM,MAAM,QAAQ,aAAa,GAAG,GAAG,IAAI,KACnD,QAAQ,QAAQ,QAAQ,MAAM,MAAM,GAAG,GAAG,IAAI,IAChD;AACA,6BAAe,WAAW,IAAI;YAC/B;AAED;UACF,CAAC;AAED;AACA,yBAAe;QACjB,CAAC;AAED,eAAO;MACT;AAKO,UAAM,uBAAuB,iBAAiB,YAAY;ACxDjE,UAAM,QAAQ;AACN,UAAA,iBAAmB,OAAO,UAAS;AAKrC,eAAU,gBACd,GACA,GACA,SACA,MAAS;AAET,YAAM,QAAQ,OAAO,KAAK,CAAC;AAE3B,YAAIA,SAAQ,MAAM;AAElB,YAAI,OAAO,KAAK,CAAC,EAAE,WAAWA,QAAO;AACnC,iBAAO;QACR;AAED,YAAI;AAMJ,eAAOA,WAAU,GAAG;AAClB,gBAAM,MAAMA,MAAK;AAEjB,cAAI,QAAQ,OAAO;AACjB,gBAAM,gBAAgB,CAAC,CAAC,EAAE;AAC1B,gBAAM,gBAAgB,CAAC,CAAC,EAAE;AAE1B,iBAAK,iBAAiB,kBAAkB,kBAAkB,eAAe;AACvE,qBAAO;YACR;UACF;AAED,cACE,CAAC,eAAe,KAAK,GAAG,GAAG,KAC3B,CAAC,QAAQ,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,KAAK,KAAK,GAAG,GAAG,IAAI,GAC7C;AACA,mBAAO;UACR;QACF;AAED,eAAO;MACT;AAKO,UAAM,0BAA0B,iBAAiB,eAAe;ACrDvD,eAAA,gBAAgB,GAAW,GAAS;AAClD,eAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;MAChD;ACHM,eAAU,aACd,GACA,GACA,SACA,MAAS;AAET,YAAI,eAAe,EAAE,SAAS,EAAE;AAEhC,YAAI,CAAC,cAAc;AACjB,iBAAO;QACR;AAED,YAAI,CAAC,EAAE,MAAM;AACX,iBAAO;QACR;AAQD,YAAM,iBAAuC,CAAA;AAE7C,UAAE,QAAQ,SAAC,QAAQ,MAAI;AACrB,cAAI,CAAC,cAAc;AACjB;UACD;AAED,cAAI,WAAW;AACf,cAAI,aAAa;AAEjB,YAAE,QAAQ,SAAC,QAAQ,MAAI;AACrB,gBACE,CAAC,YACD,CAAC,eAAe,UAAU,MACzB,WAAW,QAAQ,QAAQ,QAAQ,MAAM,MAAM,GAAG,GAAG,IAAI,IAC1D;AACA,6BAAe,UAAU,IAAI;YAC9B;AAED;UACF,CAAC;AAED,yBAAe;QACjB,CAAC;AAED,eAAO;MACT;AAKO,UAAM,uBAAuB,iBAAiB,YAAY;AC1CjE,UAAM,iBAA4D,OAAO,OACvE;QACE;QACA;QACA;QACA;QACA;QACA;QACA,qBAAqB;MACtB,CAAA;AAEH,UAAM,0BACJ,OAAO,OAAO;QACZ,gBAAgB;QAChB;QACA,cAAc;QACd,iBAAiB;QACjB;QACA,cAAc;QACd,qBAAqB;MACtB,CAAA;AAEH,UAAM,cAAc,iBAAiB,cAAc;AAKnC,eAAA,UAAgB,GAAM,GAAI;AACxC,eAAO,YAAY,GAAG,GAAG,MAAS;MACpC;AAEA,UAAM,iBAAiB,iBACrB,MAAM,gBAAgB,EAAE,qBAAqB,WAAA;AAAM,eAAA;MAAkB,EAAA,CAAE,CAAC;AAM1D,eAAA,aAAmB,GAAM,GAAI;AAC3C,eAAO,eAAe,GAAG,GAAG,MAAS;MACvC;AAEA,UAAM,sBAAsB,iBAAiB,uBAAuB;AAKpD,eAAA,kBAAwB,GAAM,GAAI;AAChD,eAAO,oBAAoB,GAAG,GAAG,oBAAI,QAAO,CAAE;MAChD;AAEA,UAAM,yBAAyB,iBAC7B,MAAM,yBAAyB;QAC7B,qBAAqB,WAAA;AAAM,iBAAA;QAAkB;MAC9C,CAAA,CAAC;AAMY,eAAA,qBAA2B,GAAM,GAAI;AACnD,eAAO,uBAAuB,GAAG,GAAG,oBAAI,QAAO,CAAE;MACnD;AAUM,eAAU,kBACd,sBAAgD;AAEhD,eAAO,iBACL,MAAM,gBAAgB,qBAAqB,cAAqB,CAAC,CAAC;MAEtE;AAYM,eAAU,0BAEd,sBAAgD;AAChD,YAAM,aAAa,iBACjB,MACE,yBACA,qBAAqB,uBAA8B,CAAC,CACrD;AAGH,eAAQ,SAAC,GAAQ,GAAQ,MAAyB;AAAzB,cAAA,SAAA,QAAA;AAAA,mBAAgB,oBAAA,QAAO;UAAE;AAChD,iBAAA,WAAW,GAAG,GAAG,IAAI;QAArB;MACJ;;;;;;;;;;;;;;ACxHA;AAAA;AAAA,aAAS,EAAEC,IAAE;AAAC,UAAI,GAAE,GAAE,IAAE;AAAG,UAAG,YAAU,OAAOA,MAAG,YAAU,OAAOA,GAAE,MAAGA;AAAA,eAAU,YAAU,OAAOA,GAAE,KAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,YAAI,IAAEA,GAAE;AAAO,aAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAAA,GAAE,CAAC,MAAI,IAAE,EAAEA,GAAE,CAAC,CAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAA,MAAE,MAAM,MAAI,KAAKA,GAAE,CAAAA,GAAE,CAAC,MAAI,MAAI,KAAG,MAAK,KAAG;AAAG,aAAO;AAAA,IAAC;AAAC,aAAS,IAAG;AAAC,eAAQA,IAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,QAAO,IAAE,GAAE,IAAI,EAACA,KAAE,UAAU,CAAC,OAAK,IAAE,EAAEA,EAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAG,aAAO;AAAA,IAAC;AAAC,WAAO,UAAQ,GAAE,OAAO,QAAQ,OAAK;AAAA;AAAA;;;ACA3Y;AAAA;AACA,WAAO,UAAU,SAAS,kBAAkB,GAAG,GAAG,aAAa;AAC7D,UAAI,MAAM,EAAG,QAAO;AACpB,aAAO,EAAE,cAAc,EAAE,aAAa,YAAY,EAAE,OAAO,EAAE,KAAK,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,mBAAmB,YAAY,EAAE,iBAAiB,EAAE,eAAe,KAAK,YAAY,EAAE,aAAa,EAAE,WAAW,KAAK,YAAY,EAAE,QAAQ,EAAE,MAAM,KAAK,YAAY,EAAE,QAAQ,EAAE,MAAM,KAAK,YAAY,EAAE,kBAAkB,EAAE,gBAAgB,KAAK,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,aAAa,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,eAAe,YAAY,EAAE,eAAe,EAAE,aAAa,KAAK,YAAY,EAAE,cAAc,EAAE,YAAY,KAAK,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,eAAe,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,EAAE,cAAc,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,aAAa,EAAE,YAAY,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,YAAY,EAAE,cAAc,EAAE,YAAY,KAAK,YAAY,EAAE,UAAU,EAAE,QAAQ;AAAA,IAC1uC;AAAA;AAAA;;;ACJA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS;AACjB,YAAQ,gBAAgB;AACxB,YAAQ,cAAc;AACtB,YAAQ,kBAAkB;AAC1B,YAAQ,WAAW;AACnB,YAAQ,UAAU;AAClB,YAAQ,cAAc;AACtB,YAAQ,cAAc;AACtB,YAAQ,gBAAgB;AACxB,YAAQ,oBAAoB;AAC5B,YAAQ,oBAAoB;AAC5B,YAAQ,mBAAmB;AAC3B,YAAQ,oBAAoB;AAC5B,YAAQ,gBAAgB;AACxB,YAAQ,aAAa;AACrB,YAAQ,eAAe;AACvB,YAAQ,cAAc;AACtB,YAAQ,+BAA+B;AACvC,YAAQ,OAAO;AACf,YAAQ,OAAO;AACf,YAAQ,wBAAwB;AAChC,YAAQ,aAAa;AACrB,YAAQ,eAAe;AACvB,YAAQ,kBAAkB;AAC1B,YAAQ,0BAA0B;AAClC,YAAQ,0BAA0B;AAClC,YAAQ,gCAAgC;AACxC,YAAQ,iBAAiB;AACzB,YAAQ,iBAAiB;AACzB,QAAI,cAAc;AAClB,QAAI,SAAS,uBAAuB,eAAgB;AACpD,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AAoFpF,QAAM,eAAe;AACrB,QAAM,QAAQ;AAQd,aAAS,OAAO,QAAiC;AAC/C,UAAI,MAAM,GACR;AACF,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,kBAAU,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;AAClC,YAAI,UAAU,IAAK,OAAM;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AACA,aAAS,YAAY,QAAiC;AACpD,YAAM,YAAY,MAAM,OAAO,MAAM;AACrC,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,kBAAU,CAAC,IAAI,gBAAgB,OAAO,CAAC,CAAC;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AAIA,aAAS,aAAa,QAAqB,YAAyC;AAClF,YAAM,YAAY,MAAM,OAAO,MAAM;AACrC,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,YAAI,WAAW,MAAM,OAAO,CAAC,EAAE,GAAG;AAChC,oBAAU,CAAC,IAAI;AAAA,QACjB,OAAO;AACL,oBAAU,CAAC,IAAI,OAAO,CAAC;AAAA,QACzB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAIA,aAAS,eAAe,QAAqB,SAAsB,IAA8D;AAC/H,UAAI,OAAO,cAAc,QAAQ,OAAO;AACxC,UAAI,CAAC,KAAM,QAAO,CAAC,QAAQ,IAAI;AAC/B,aAAO,GAAG,gBAAgB,IAAI,CAAC;AAE/B,eAAS,aAAa,QAAQ,IAAI;AAClC,aAAO,CAAC,QAAQ,IAAI;AAAA,IACtB;AAGA,aAAS,gBAAgB,YAA6C;AACpE,aAAO;AAAA,QACL,GAAG,WAAW;AAAA,QACd,GAAG,WAAW;AAAA,QACd,GAAG,WAAW;AAAA,QACd,GAAG,WAAW;AAAA,QACd,GAAG,WAAW;AAAA,QACd,MAAM,WAAW;AAAA,QACjB,MAAM,WAAW;AAAA,QACjB,MAAM,WAAW;AAAA,QACjB,MAAM,WAAW;AAAA,QACjB,OAAO,QAAQ,WAAW,KAAK;AAAA,QAC/B,QAAQ,QAAQ,WAAW,MAAM;AAAA;AAAA,QAEjC,aAAa,WAAW;AAAA,QACxB,aAAa,WAAW;AAAA,QACxB,eAAe,WAAW;AAAA,QAC1B,WAAW,WAAW;AAAA,MACxB;AAAA,IACF;AAMA,aAAS,cAAc,GAAuB,GAAoC;AAChF,cAAQ,GAAG,YAAY,WAAW,OAAO,QAAQ,SAAS,IAAI,GAAG,OAAK,GAAG,GAAG,GAAG,OAAO,QAAQ,SAAS,IAAI,GAAG,OAAK,GAAG,GAAG,CAAC,MAAM,GAAG,YAAY,WAAW,OAAO,QAAQ,SAAS,IAAI,GAAG,OAAK,GAAG,MAAM,WAAW,CAAC,GAAG,OAAO,QAAQ,SAAS,IAAI,GAAG,OAAK,GAAG,MAAM,WAAW,CAAC,CAAC;AAAA,IAClR;AAWA,QAAM,oBAA4C,QAAQ,oBAAoB;AAG9E,aAAS,kBAAkB,GAAkB,GAA+B;AAC1E,aAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE;AAAA,IACvF;AAKA,aAAS,SAAS,IAAqB,IAAkC;AACvE,UAAI,GAAG,MAAM,GAAG,EAAG,QAAO;AAC1B,UAAI,GAAG,IAAI,GAAG,KAAK,GAAG,EAAG,QAAO;AAChC,UAAI,GAAG,KAAK,GAAG,IAAI,GAAG,EAAG,QAAO;AAChC,UAAI,GAAG,IAAI,GAAG,KAAK,GAAG,EAAG,QAAO;AAChC,UAAI,GAAG,KAAK,GAAG,IAAI,GAAG,EAAG,QAAO;AAChC,aAAO;AAAA,IACT;AAcA,aAAS,QAAQ,QAAqBC,cAA+B,MAAmB,cAAyC;AAE/H,YAAM,cAAc,WAAW,MAAM;AAErC,YAAM,SAAS,gBAAgB,QAAQA,YAAW;AAElD,YAAM,MAAM,MAAM,OAAO,MAAM;AAC/B,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,YAAI,IAAI,gBAAgB,OAAO,CAAC,CAAC;AAGjC,YAAI,CAAC,EAAE,QAAQ;AACb,cAAI,YAAY,aAAa,GAAGA,cAAa,MAAM,QAAQ,YAAY;AAIvE,sBAAY,KAAK,CAAC;AAAA,QACpB;AAGA,YAAI,OAAO,QAAQ,OAAO,CAAC,CAAC,CAAC,IAAI;AAGjC,UAAE,QAAQ;AAAA,MACZ;AACA,aAAO;AAAA,IACT;AACA,QAAM,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAIA,aAAS,2BAA2B,QAAqB,MAAuB,aAA0B,MAAsB;AAC9H,YAAM,WAAW,YAAY,IAAI;AACjC,WAAK,IAAI,KAAK;AACd,YAAM,YAAY,OAAO,IAAI,gBAAc;AACzC,eAAO,WAAW;AAAA,MACpB,CAAC,EAAE,QAAQ,KAAK,CAAC;AAGjB,eAAS,IAAI,YAAY,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClD,cAAM,YAAY,OAAO,CAAC;AAE1B,YAAI,UAAU,OAAQ;AAItB,YAAI,UAAU,IAAI,KAAK,IAAI,KAAK,EAAG;AACnC,YAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,qCAA2B,QAAQ,WAAW,cAAc,KAAK,QAAQ,GAAG,IAAI;AAAA,QAClF;AAAA,MACF;AACA,WAAK,IAAI,IAAI;AAAA,IACf;AAQA,aAAS,YAAY,aAA0B,GAAoBA,cAA+B,MAAmB,YAAyB,cAA6C;AACzL,YAAM,WAAWA,iBAAgB;AACjC,YAAM,WAAWA,iBAAgB;AACjC,UAAI,UAAU;AAIZ,UAAE,IAAI,KAAK,IAAI,OAAO,WAAW,GAAG,EAAE,CAAC;AAEvC,eAAO,EAAE,IAAI,KAAK,CAAC,kBAAkB,aAAa,CAAC,GAAG;AACpD,YAAE;AAAA,QACJ;AAAA,MACF,WAAW,UAAU;AAEnB,eAAO,EAAE,IAAI,KAAK,CAAC,kBAAkB,aAAa,CAAC,GAAG;AACpD,YAAE;AAAA,QACJ;AAAA,MACF;AAGA,UAAIC;AAEJ,cAAQA,YAAW,kBAAkB,aAAa,CAAC,MAAM,EAAED,iBAAgB,QAAQ,eAAe;AAChG,YAAI,UAAU;AACZ,qCAA2B,YAAY,GAAGC,UAAS,IAAIA,UAAS,GAAG,GAAG;AAAA,QACxE,OAAO;AACL,qCAA2B,YAAY,GAAGA,UAAS,IAAIA,UAAS,GAAG,GAAG;AAAA,QACxE;AAEA,YAAI,YAAY,EAAE,IAAI,EAAE,IAAI,MAAM;AAChC,YAAE,IAAI,OAAO,EAAE;AACf,YAAE;AAEF,iBAAO,EAAE,IAAI,KAAK,CAAC,kBAAkB,aAAa,CAAC,GAAG;AACpD,cAAE;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAGA,QAAE,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC;AACrB,QAAE,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC;AACrB,aAAO;AAAA,IACT;AAUA,aAAS,cAAc,QAAqB,QAA2C;AACrF,YAAM,eAAe,WAAW,MAAM;AACtC,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,cAAM,IAAI,OAAO,CAAC;AAElB,YAAI,EAAE,IAAI,EAAE,IAAI,OAAO,KAAM,GAAE,IAAI,OAAO,OAAO,EAAE;AAEnD,YAAI,EAAE,IAAI,GAAG;AACX,YAAE,IAAI;AACN,YAAE,IAAI,OAAO;AAAA,QACf;AACA,YAAI,CAAC,EAAE,OAAQ,cAAa,KAAK,CAAC;AAAA,aAAO;AAGvC,iBAAO,kBAAkB,cAAc,CAAC,GAAG;AACzC,cAAE;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AASA,aAAS,cAAc,QAAqB,IAAkC;AAC5E,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,YAAI,OAAO,CAAC,EAAE,MAAM,GAAI,QAAO,OAAO,CAAC;AAAA,MACzC;AAAA,IACF;AAUA,aAAS,kBAAkB,QAAqB,YAA8C;AAC5F,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,YAAI,SAAS,OAAO,CAAC,GAAG,UAAU,EAAG,QAAO,OAAO,CAAC;AAAA,MACtD;AAAA,IACF;AACA,aAAS,iBAAiB,QAAqB,YAAoD;AACjG,aAAO,OAAO,OAAO,OAAK,SAAS,GAAG,UAAU,CAAC;AAAA,IACnD;AAOA,aAAS,WAAW,QAA4C;AAC9D,aAAO,OAAO,OAAO,OAAK,EAAE,MAAM;AAAA,IACpC;AAYA,aAAS,YAAY,QAAqB,GAAoB,GAAiB,GAAiB,cAA6B,kBAAiCD,cAA+B,MAAmB,cAAyC;AAGvP,UAAI,EAAE,UAAU,EAAE,gBAAgB,KAAM,QAAO;AAG/C,UAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAG,QAAO;AACnC,UAAI,kBAAkB,EAAE,CAAC,QAAQ,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG;AAC/E,YAAM,OAAO,EAAE;AACf,YAAM,OAAO,EAAE;AAGf,UAAI,OAAO,MAAM,SAAU,GAAE,IAAI;AACjC,UAAI,OAAO,MAAM,SAAU,GAAE,IAAI;AACjC,QAAE,QAAQ;AAMV,UAAI,SAAS,gBAAgB,QAAQA,YAAW;AAChD,YAAM,WAAWA,iBAAgB,cAAc,OAAO,MAAM,WAAW,QAAQ,IAAIA,iBAAgB,gBAAgB,OAAO,MAAM,WAAW,QAAQ,IAAI;AAEvJ,UAAI,SAAU,UAAS,OAAO,QAAQ;AACtC,YAAM,aAAa,iBAAiB,QAAQ,CAAC;AAC7C,YAAM,gBAAgB,WAAW,SAAS;AAI1C,UAAI,iBAAiB,cAAc;AAGjC,eAAO,YAAY,MAAM;AAAA,MAC3B,WAAW,iBAAiB,kBAAkB;AAI5C,YAAI,0BAA0B,EAAE,CAAC,cAAc;AAC/C,UAAE,IAAI;AACN,UAAE,IAAI;AACN,UAAE,QAAQ;AACV,eAAO;AAAA,MACT;AAGA,eAAS,IAAI,GAAG,MAAM,WAAW,QAAQ,IAAI,KAAK,KAAK;AACrD,cAAM,YAAY,WAAW,CAAC;AAC9B,YAAI,+BAA+B,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,SAAS,UAAU,CAAC,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,GAAG;AAGjH,YAAI,UAAU,MAAO;AAGrB,YAAI,UAAU,QAAQ;AACpB,mBAAS,6BAA6B,QAAQ,WAAW,GAAG,cAAcA,cAAa,IAAI;AAAA,QAC7F,OAAO;AACL,mBAAS,6BAA6B,QAAQ,GAAG,WAAW,cAAcA,cAAa,IAAI;AAAA,QAC7F;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAUA,aAAS,6BAA6B,QAAqB,cAA+B,YAA6B,cAA6BA,cAA+B,MAA+B;AAChN,YAAM,WAAWA,iBAAgB;AAEjC,YAAM,WAAWA,iBAAgB;AACjC,YAAM,mBAAmB,aAAa;AAKtC,UAAI,cAAc;AAEhB,uBAAe;AAGf,cAAM,WAA4B;AAAA,UAChC,GAAG,WAAW,KAAK,IAAI,aAAa,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW;AAAA,UACtE,GAAG,WAAW,KAAK,IAAI,aAAa,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW;AAAA,UACtE,GAAG,WAAW;AAAA,UACd,GAAG,WAAW;AAAA,UACd,GAAG;AAAA,QACL;AACA,cAAM,iBAAiB,kBAAkB,QAAQ,QAAQ;AACzD,cAAM,iBAAiB,kBAAkB,eAAe,IAAI,eAAe,IAAI,aAAa;AAC5F,cAAM,gBAAgB,kBAAkB,aAAa,IAAI,aAAa,IAAI,eAAe;AAGzF,YAAI,CAAC,gBAAgB;AACnB,cAAI,8BAA8B,WAAW,CAAC,WAAW,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI;AACrF,iBAAO,YAAY,QAAQ,YAAY,WAAW,SAAS,IAAI,QAAW,WAAW,SAAS,IAAI,QAAW,cAAc,kBAAkBA,cAAa,IAAI;AAAA,QAChK,WAAW,kBAAkB,UAAU;AACrC,iBAAO,YAAY,QAAQ,YAAY,QAAW,aAAa,IAAI,GAAG,cAAc,kBAAkBA,cAAa,IAAI;AAAA,QACzH,WAAW,kBAAkBA,gBAAe,MAAM;AAChD,uBAAa,IAAI,WAAW;AAC5B,qBAAW,IAAI,WAAW,IAAI,WAAW;AACzC,iBAAO;AAAA,QACT,WAAW,iBAAiB,UAAU;AACpC,iBAAO,YAAY,QAAQ,cAAc,WAAW,GAAG,QAAW,cAAc,kBAAkBA,cAAa,IAAI;AAAA,QACrH;AAAA,MACF;AACA,YAAM,OAAO,WAAW,WAAW,IAAI,IAAI;AAC3C,YAAM,OAAO,WAAW,WAAW,IAAI,IAAI;AAC3C,UAAI,QAAQ,QAAQ,QAAQ,MAAM;AAChC,eAAO;AAAA,MACT;AACA,aAAO,YAAY,QAAQ,YAAY,WAAW,WAAW,IAAI,IAAI,QAAW,WAAW,WAAW,IAAI,IAAI,QAAW,cAAc,kBAAkBA,cAAa,IAAI;AAAA,IAC5K;AAQA,aAAS,KAAK,KAA8B;AAC1C,aAAO,MAAM,MAAM;AAAA,IACrB;AAKA,QAAM,iBAAiB,CAAC,MAAmB,cAA2B,UAAuB,mBAAgC;AAC3H,aAAO,OAAO,WAAW,iBAAiB,eAAe;AAAA,IAC3D;AACA,QAAM,kBAAkB,CAAC,KAAkB,eAA4B,cAA2B;AAChG,aAAO,MAAM,IAAI,gBAAgB;AAAA,IACnC;AACA,QAAM,gBAAgB,CAAC,SAAsB,KAAK,IAAI,GAAG,IAAI;AAC7D,QAAM,eAAe,CAAC,QAAqB,KAAK,IAAI,GAAG,GAAG;AAC1D,QAAM,cAAc,CAAC,aAAa,MAAM,oBAAoB;AAC1D,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,MAAM,YAAY,OAAO,SAAS,YAAY;AACpD,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,QAAQ,gBAAgB,KAAK,YAAY,QAAQ,MAAM;AAAA,QACvD,KAAK,aAAa,GAAG;AAAA,MACvB;AAAA,IACF;AACA,QAAM,aAAa,CAAC,aAAa,OAAO,mBAAmB;AACzD,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,OAAO,cAAc;AAAA,QAChF,MAAM,cAAc,IAAI;AAAA,MAC1B;AAAA,IACF;AACA,QAAM,aAAa,CAAC,aAAa,OAAO,mBAAmB;AACzD,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,OAAO,YAAY,QAAQ,QAAQ,YAAY;AACrD,aAAO;AAAA,QACL;AAAA,QACA,OAAO,OAAO,IAAI,YAAY,QAAQ,eAAe,YAAY,MAAM,YAAY,OAAO,OAAO,cAAc;AAAA,QAC/G,KAAK,aAAa,GAAG;AAAA,QACrB,MAAM,cAAc,IAAI;AAAA,MAC1B;AAAA,IACF;AACA,QAAM,cAAc,CAAC,aAAa,OAAO,mBAAmB;AAC1D,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,QAAQ,gBAAgB,KAAK,YAAY,QAAQ,MAAM;AAAA,QACvD,KAAK,aAAa,GAAG;AAAA,MACvB;AAAA,IACF;AACA,QAAM,kBAAkB,WAAY;AAClC,aAAO,YAAY,UAAU,UAAU,IAAI,SAAY,UAAU,CAAC,GAAG,WAAW,GAAG,SAAS,GAAG,UAAU,UAAU,IAAI,SAAY,UAAU,CAAC,CAAC;AAAA,IACjJ;AACA,QAAM,kBAAkB,WAAY;AAClC,aAAO,YAAY,UAAU,UAAU,IAAI,SAAY,UAAU,CAAC,GAAG,WAAW,GAAG,SAAS,GAAG,UAAU,UAAU,IAAI,SAAY,UAAU,CAAC,CAAC;AAAA,IACjJ;AACA,QAAM,kBAAkB,WAAY;AAClC,aAAO,YAAY,UAAU,UAAU,IAAI,SAAY,UAAU,CAAC,GAAG,WAAW,GAAG,SAAS,GAAG,UAAU,UAAU,IAAI,SAAY,UAAU,CAAC,CAAC;AAAA,IACjJ;AACA,QAAM,kBAAkB,WAAY;AAClC,aAAO,YAAY,UAAU,UAAU,IAAI,SAAY,UAAU,CAAC,GAAG,WAAW,GAAG,SAAS,GAAG,UAAU,UAAU,IAAI,SAAY,UAAU,CAAC,CAAC;AAAA,IACjJ;AACA,QAAM,0BAA0B;AAAA,MAC9B,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,IAAI;AAAA,IACN;AAKA,aAAS,sBAAsB,WAAkC,aAA4B,SAAwB,gBAA2C;AAC9J,YAAM,iBAAiB,wBAAwB,SAAS;AAExD,UAAI,CAAC,eAAgB,QAAO;AAC5B,aAAO,eAAe,aAAa;AAAA,QACjC,GAAG;AAAA,QACH,GAAG;AAAA,MACL,GAAG,cAAc;AAAA,IACnB;AACA,aAAS,aAAa,OAA2B;AAC/C,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAmB;AAEnB,YAAM,YAAY,aAAa,IAAI,MAAM,GAAG;AAC5C,aAAO;AAAA,QACL,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,OAAO,GAAG,KAAK;AAAA,QACf,QAAQ,GAAG,MAAM;AAAA,QACjB,UAAU;AAAA,MACZ;AAAA,IACF;AACA,aAAS,WAAW,OAA2B;AAC7C,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAmB;AACnB,aAAO;AAAA,QACL,KAAK,GAAG,GAAG;AAAA,QACX,MAAM,GAAG,IAAI;AAAA,QACb,OAAO,GAAG,KAAK;AAAA,QACf,QAAQ,GAAG,MAAM;AAAA,QACjB,UAAU;AAAA,MACZ;AAAA,IACF;AAQA,aAAS,gBAAgB,QAAqBA,cAA2C;AACvF,UAAIA,iBAAgB,aAAc,QAAO,wBAAwB,MAAM;AACvE,UAAIA,iBAAgB,WAAY,QAAO,wBAAwB,MAAM;AAAA,UAAO,QAAO;AAAA,IACrF;AAOA,aAAS,wBAAwB,QAAiC;AAEhE,aAAO,OAAO,MAAM,CAAC,EAAE,KAAK,SAAU,GAAG,GAAG;AAC1C,YAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;AACzC,iBAAO;AAAA,QACT,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;AAErC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAOA,aAAS,wBAAwB,QAAiC;AAChE,aAAO,OAAO,MAAM,CAAC,EAAE,KAAK,SAAU,GAAG,GAAG;AAC1C,YAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;AACzC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAaA,aAAS,8BAA8B,eAA4B,UAA8B,MAAmBA,cAA+B,cAAyC;AAC1L,sBAAgB,iBAAiB,CAAC;AAGlC,YAAM,SAA4B,CAAC;AACnC,aAAO,QAAQ,SAAS,QAAQ,UAAU,CAAC,UAAkC;AAE3E,YAAI,OAAO,OAAO,KAAM;AACxB,cAAM,SAAS,cAAc,eAAe,OAAO,MAAM,GAAG,CAAC;AAC7D,cAAM,IAAI,MAAM,MAAM,WAAW;AAGjC,YAAI,UAAU,KAAK,MAAM;AACvB,iBAAO,KAAK,gBAAgB,MAAM,CAAC;AAAA,QACrC,OAAO;AAEL,cAAI,GAAG;AACL,gBAAI,CAAC,cAAc;AACjB,6BAAe,CAAC,CAAC,GAAG,0BAA0B;AAAA,YAChD;AAEA,mBAAO,KAAK,gBAAgB;AAAA,cAC1B,GAAG;AAAA,cACH,GAAG,MAAM;AAAA,YACX,CAAC,CAAC;AAAA,UACJ,OAAO;AAGL,mBAAO,KAAK,gBAAgB;AAAA,cAC1B,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG,OAAO,MAAM;AAAA,cAChB,GAAG,OAAO,MAAM,GAAG;AAAA,YACrB,CAAC,CAAC;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAGD,YAAM,kBAAkB,cAAc,QAAQ;AAAA,QAC5C;AAAA,MACF,CAAC;AACD,aAAO,eAAe,kBAAkB,QAAQ,iBAAiBA,cAAa,IAAI;AAAA,IACpF;AASA,aAAS,eAAe,QAA+B;AACrD,UAAI,cAA2B,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnG,YAAM,WAAW,CAAC,KAAK,KAAK,KAAK,GAAG;AACpC,UAAI,CAAC,MAAM,QAAQ,MAAM,EAAG,OAAM,IAAI,MAAM,cAAc,oBAAoB;AAC9E,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,cAAM,OAAO,OAAO,CAAC;AACrB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAM,MAAM,SAAS,CAAC;AACtB,gBAAM,QAAQ,KAAK,GAAG;AACtB,cAAI,OAAO,UAAU,YAAY,OAAO,MAAM,KAAK,GAAG;AACpD,kBAAM,IAAI,MAAM,oBAAoB,WAAW,IAAI,CAAC,KAAK,GAAG,gCAAgC,KAAK,KAAK,OAAO,KAAK,GAAG;AAAA,UACvH;AAAA,QACF;AACA,YAAI,OAAO,KAAK,MAAM,eAAe,OAAO,KAAK,MAAM,UAAU;AAC/D,gBAAM,IAAI,MAAM,oBAAoB,WAAW,IAAI,CAAC,mCAAmC,KAAK,CAAC,KAAK,OAAO,KAAK,CAAC,GAAG;AAAA,QACpH;AAAA,MACF;AAAA,IACF;AAGA,aAAS,YAAY,OAAsF;AACzG,YAAM;AAAA,QACJ;AAAA,QACA,aAAAA;AAAA,MACF,IAAI,SAAS,CAAC;AACd,aAAO,oBAAoB,QAAQ,OAAOA;AAAA,IAC5C;AACA,aAAS,MAAM;AACb,UAAI,CAAC,MAAO;AAEZ,cAAQ,IAAI,GAAG,SAAS;AAAA,IAC1B;AACA,QAAM,OAAO,MAAM;AAAA,IAAC;AACpB,YAAQ,OAAO;AAAA;AAAA;;;ACj0Bf;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,mBAAmB;AAC3B,YAAQ,uBAAuB;AAC/B,YAAQ,mBAAmB;AAC3B,YAAQ,SAAS;AACjB,YAAQ,SAAS;AACjB,YAAQ,QAAQ;AAWhB,aAAS,iBAAiB,gBAAiD;AACzE,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,cAAQ,iBAAiB,OAAO,CAAC,KAAK,OAAO,KAAK,iBAAiB,CAAC,IAAI,KAAK;AAAA,IAC/E;AAMA,aAAS,iBAAiB,WAAwB,cAA2B,UAAmC;AAE9G,UAAI,CAAC,OAAO,SAAS,SAAS,EAAG,QAAO;AACxC,aAAO,KAAK,MAAM,eAAe,YAAY,KAAK,IAAI,GAAG,YAAY,CAAC,IAAI,QAAQ;AAAA,IACpF;AAYA,aAAS,qBAAqB,gBAAqC,GAAgB,GAAgB,GAAgB,GAAgB,OAAmC;AACpK,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,WAAW,iBAAiB,cAAc;AAChD,YAAM,MAAM,CAAC;AAGb,UAAI,SAAS,MAAM,UAAU;AAC3B,YAAI,QAAQ,KAAK,MAAM,MAAM,SAAS,KAAK;AAC3C,YAAI,SAAS,KAAK,MAAM,MAAM,SAAS,MAAM;AAAA,MAC/C,OAEK;AACH,YAAI,QAAQ,iBAAiB,GAAG,UAAU,OAAO,CAAC,CAAC;AACnD,YAAI,SAAS,iBAAiB,GAAG,WAAW,OAAO,CAAC,CAAC;AAAA,MACvD;AAGA,UAAI,SAAS,MAAM,UAAU;AAC3B,YAAI,MAAM,KAAK,MAAM,MAAM,SAAS,GAAG;AACvC,YAAI,OAAO,KAAK,MAAM,MAAM,SAAS,IAAI;AAAA,MAC3C,WAAW,SAAS,MAAM,YAAY,OAAO,MAAM,SAAS,QAAQ,YAAY,OAAO,MAAM,SAAS,SAAS,UAAU;AACvH,YAAI,MAAM,KAAK,MAAM,MAAM,SAAS,GAAG;AACvC,YAAI,OAAO,KAAK,MAAM,MAAM,SAAS,IAAI;AAAA,MAC3C,OAEK;AACH,YAAI,MAAM,KAAK,OAAO,YAAY,OAAO,CAAC,KAAK,IAAI,iBAAiB,CAAC,CAAC;AACtE,YAAI,OAAO,KAAK,OAAO,WAAW,OAAO,CAAC,KAAK,IAAI,iBAAiB,CAAC,CAAC;AAAA,MACxE;AACA,aAAO;AAAA,IACT;AAWA,aAAS,OAAO,gBAAqC,KAAkB,MAAmB,GAAgB,GAA8C;AACtJ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,WAAW,iBAAiB,cAAc;AAKhD,UAAI,IAAI,KAAK,OAAO,OAAO,iBAAiB,CAAC,MAAM,WAAW,OAAO,CAAC,EAAE;AACxE,UAAI,IAAI,KAAK,OAAO,MAAM,iBAAiB,CAAC,MAAM,YAAY,OAAO,CAAC,EAAE;AAGxE,UAAI,MAAM,GAAG,GAAG,OAAO,CAAC;AACxB,UAAI,MAAM,GAAG,GAAG,UAAU,CAAC;AAC3B,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAYA,aAAS,OAAO,gBAAqC,OAAoB,QAAqB,GAAgB,GAAgB,QAAmD;AAC/K,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,WAAW,iBAAiB,cAAc;AAKhD,UAAI,IAAI,KAAK,OAAO,QAAQ,OAAO,CAAC,MAAM,WAAW,OAAO,CAAC,EAAE;AAC/D,UAAI,IAAI,KAAK,OAAO,SAAS,OAAO,CAAC,MAAM,YAAY,OAAO,CAAC,EAAE;AAGjE,UAAI,KAAK,MAAM,GAAG,GAAG,OAAO,CAAC;AAC7B,UAAI,KAAK,MAAM,GAAG,GAAG,UAAU,CAAC;AAChC,UAAI,CAAC,MAAM,KAAK,IAAI,EAAE,QAAQ,MAAM,MAAM,IAAI;AAC5C,aAAK,MAAM,GAAG,GAAG,IAAI;AAAA,MACvB;AACA,UAAI,CAAC,MAAM,KAAK,IAAI,EAAE,QAAQ,MAAM,MAAM,IAAI;AAC5C,aAAK,MAAM,GAAG,GAAG,OAAO;AAAA,MAC1B;AACA,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AAGA,aAAS,MAAM,KAAkB,YAAyB,YAAqC;AAC7F,aAAO,KAAK,IAAI,KAAK,IAAI,KAAK,UAAU,GAAG,UAAU;AAAA,IACvD;AAAA;AAAA;;;ACpKA;AAAA;AAAA;AAaA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAIA,YAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AACvD,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AACnE,YAAI,oBAAoB,YAAY,OAAO,IAAI,cAAc,IAAI;AACjE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AAGnE,YAAI,wBAAwB,YAAY,OAAO,IAAI,kBAAkB,IAAI;AACzE,YAAI,6BAA6B,YAAY,OAAO,IAAI,uBAAuB,IAAI;AACnF,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,2BAA2B,YAAY,OAAO,IAAI,qBAAqB,IAAI;AAC/E,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAC/D,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,uBAAuB,YAAY,OAAO,IAAI,iBAAiB,IAAI;AACvE,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAE/D,iBAAS,mBAAmB,MAAM;AAChC,iBAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA,UACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;AAAA,QACplB;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAIE,WAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAE1C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,+KAAyL;AAAA,YAC3M;AAAA,UACF;AAEA,iBAAO,iBAAiB,MAAM,KAAK,OAAO,MAAM,MAAM;AAAA,QACxD;AACA,iBAAS,iBAAiB,QAAQ;AAChC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,YAAY;AACpB,gBAAQ,iBAAiB;AACzB,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAUA;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoB;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAY;AACpB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqB;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpLA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAQA,QAAI,wBAAwB,OAAO;AACnC,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,mBAAmB,OAAO,UAAU;AAExC,aAAS,SAAS,KAAK;AACtB,UAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,cAAM,IAAI,UAAU,uDAAuD;AAAA,MAC5E;AAEA,aAAO,OAAO,GAAG;AAAA,IAClB;AAEA,aAAS,kBAAkB;AAC1B,UAAI;AACH,YAAI,CAAC,OAAO,QAAQ;AACnB,iBAAO;AAAA,QACR;AAKA,YAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,cAAM,CAAC,IAAI;AACX,YAAI,OAAO,oBAAoB,KAAK,EAAE,CAAC,MAAM,KAAK;AACjD,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,gBAAM,MAAM,OAAO,aAAa,CAAC,CAAC,IAAI;AAAA,QACvC;AACA,YAAI,SAAS,OAAO,oBAAoB,KAAK,EAAE,IAAI,SAAU,GAAG;AAC/D,iBAAO,MAAM,CAAC;AAAA,QACf,CAAC;AACD,YAAI,OAAO,KAAK,EAAE,MAAM,cAAc;AACrC,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,+BAAuB,MAAM,EAAE,EAAE,QAAQ,SAAU,QAAQ;AAC1D,gBAAM,MAAM,IAAI;AAAA,QACjB,CAAC;AACD,YAAI,OAAO,KAAK,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAC/C,wBAAwB;AACzB,iBAAO;AAAA,QACR;AAEA,eAAO;AAAA,MACR,SAAS,KAAK;AAEb,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU,gBAAgB,IAAI,OAAO,SAAS,SAAU,QAAQ,QAAQ;AAC9E,UAAI;AACJ,UAAI,KAAK,SAAS,MAAM;AACxB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,eAAO,OAAO,UAAU,CAAC,CAAC;AAE1B,iBAAS,OAAO,MAAM;AACrB,cAAI,eAAe,KAAK,MAAM,GAAG,GAAG;AACnC,eAAG,GAAG,IAAI,KAAK,GAAG;AAAA,UACnB;AAAA,QACD;AAEA,YAAI,uBAAuB;AAC1B,oBAAU,sBAAsB,IAAI;AACpC,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,gBAAI,iBAAiB,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AAC5C,iBAAG,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzFA;AAAA;AAAA;AASA,QAAI,uBAAuB;AAE3B,WAAO,UAAU;AAAA;AAAA;;;ACXjB;AAAA;AAAA,WAAO,UAAU,SAAS,KAAK,KAAK,OAAO,UAAU,cAAc;AAAA;AAAA;;;ACAnE;AAAA;AAAA;AASA,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACrC,6BAAuB;AACvB,2BAAqB,CAAC;AACtB,YAAM;AAEV,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAO;AAAA,MACrB;AAAA,IACF;AAhBM;AACA;AACA;AA2BN,aAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,UAAU;AAC5E,UAAI,MAAuC;AACzC,iBAAS,gBAAgB,WAAW;AAClC,cAAI,IAAI,WAAW,YAAY,GAAG;AAChC,gBAAI;AAIJ,gBAAI;AAGF,kBAAI,OAAO,UAAU,YAAY,MAAM,YAAY;AACjD,oBAAI,MAAM;AAAA,mBACP,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FACC,OAAO,UAAU,YAAY,IAAI;AAAA,gBAEpH;AACA,oBAAI,OAAO;AACX,sBAAM;AAAA,cACR;AACA,sBAAQ,UAAU,YAAY,EAAE,QAAQ,cAAc,eAAe,UAAU,MAAM,oBAAoB;AAAA,YAC3G,SAAS,IAAI;AACX,sBAAQ;AAAA,YACV;AACA,gBAAI,SAAS,EAAE,iBAAiB,QAAQ;AACtC;AAAA,iBACG,iBAAiB,iBAAiB,6BACnC,WAAW,OAAO,eAAe,6FAC6B,OAAO,QAAQ;AAAA,cAI/E;AAAA,YACF;AACA,gBAAI,iBAAiB,SAAS,EAAE,MAAM,WAAW,qBAAqB;AAGpE,iCAAmB,MAAM,OAAO,IAAI;AAEpC,kBAAI,QAAQ,WAAW,SAAS,IAAI;AAEpC;AAAA,gBACE,YAAY,WAAW,YAAY,MAAM,WAAW,SAAS,OAAO,QAAQ;AAAA,cAC9E;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,mBAAe,oBAAoB,WAAW;AAC5C,UAAI,MAAuC;AACzC,6BAAqB,CAAC;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtGjB;AAAA;AAAA;AASA,QAAI,UAAU;AACd,QAAI,SAAS;AAEb,QAAI,uBAAuB;AAC3B,QAAI,MAAM;AACV,QAAI,iBAAiB;AAErB,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACzC,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAAA,IACF;AAEA,aAAS,+BAA+B;AACtC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,SAAS,gBAAgB,qBAAqB;AAE7D,UAAI,kBAAkB,OAAO,WAAW,cAAc,OAAO;AAC7D,UAAI,uBAAuB;AAgB3B,eAAS,cAAc,eAAe;AACpC,YAAI,aAAa,kBAAkB,mBAAmB,cAAc,eAAe,KAAK,cAAc,oBAAoB;AAC1H,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT;AAAA,MACF;AAiDA,UAAI,YAAY;AAIhB,UAAI,iBAAiB;AAAA,QACnB,OAAO,2BAA2B,OAAO;AAAA,QACzC,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,MAAM,2BAA2B,SAAS;AAAA,QAC1C,MAAM,2BAA2B,UAAU;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAE3C,KAAK,qBAAqB;AAAA,QAC1B,SAAS;AAAA,QACT,SAAS,yBAAyB;AAAA,QAClC,aAAa,6BAA6B;AAAA,QAC1C,YAAY;AAAA,QACZ,MAAM,kBAAkB;AAAA,QACxB,UAAU;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAOA,eAAS,GAAG,GAAG,GAAG;AAEhB,YAAI,MAAM,GAAG;AAGX,iBAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,QAClC,OAAO;AAEL,iBAAO,MAAM,KAAK,MAAM;AAAA,QAC1B;AAAA,MACF;AAUA,eAAS,cAAc,SAAS,MAAM;AACpC,aAAK,UAAU;AACf,aAAK,OAAO,QAAQ,OAAO,SAAS,WAAW,OAAM,CAAC;AACtD,aAAK,QAAQ;AAAA,MACf;AAEA,oBAAc,YAAY,MAAM;AAEhC,eAAS,2BAA2B,UAAU;AAC5C,YAAI,MAAuC;AACzC,cAAI,0BAA0B,CAAC;AAC/B,cAAI,6BAA6B;AAAA,QACnC;AACA,iBAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAC7F,0BAAgB,iBAAiB;AACjC,yBAAe,gBAAgB;AAE/B,cAAI,WAAW,sBAAsB;AACnC,gBAAI,qBAAqB;AAEvB,kBAAI,MAAM,IAAI;AAAA,gBACZ;AAAA,cAGF;AACA,kBAAI,OAAO;AACX,oBAAM;AAAA,YACR,WAAoD,OAAO,YAAY,aAAa;AAElF,kBAAI,WAAW,gBAAgB,MAAM;AACrC,kBACE,CAAC,wBAAwB,QAAQ;AAAA,cAEjC,6BAA6B,GAC7B;AACA;AAAA,kBACE,6EACuB,eAAe,gBAAgB,gBAAgB;AAAA,gBAIxE;AACA,wCAAwB,QAAQ,IAAI;AACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,gBAAI,YAAY;AACd,kBAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,uBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,8BAA8B,SAAS,gBAAgB,8BAA8B;AAAA,cAC1J;AACA,qBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,iCAAiC,MAAM,gBAAgB,mCAAmC;AAAA,YAC/J;AACA,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,OAAO,UAAU,eAAe,UAAU,YAAY;AAAA,UACxE;AAAA,QACF;AAEA,YAAI,mBAAmB,UAAU,KAAK,MAAM,KAAK;AACjD,yBAAiB,aAAa,UAAU,KAAK,MAAM,IAAI;AAEvD,eAAO;AAAA,MACT;AAEA,eAAS,2BAA2B,cAAc;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAChF,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,cAAc;AAI7B,gBAAI,cAAc,eAAe,SAAS;AAE1C,mBAAO,IAAI;AAAA,cACT,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,cAAc,oBAAoB,gBAAgB,mBAAmB,MAAM,eAAe;AAAA,cAC9J,EAAC,aAA0B;AAAA,YAC7B;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB;AAC9B,eAAO,2BAA2B,4BAA4B;AAAA,MAChE;AAEA,eAAS,yBAAyB,aAAa;AAC7C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,iDAAiD;AAAA,UAC/I;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK,oBAAoB;AACjH,gBAAI,iBAAiB,OAAO;AAC1B,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,2BAA2B;AAClC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,eAAe,SAAS,GAAG;AAC9B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,qCAAqC;AAAA,UACnL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,+BAA+B;AACtC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,QAAQ,mBAAmB,SAAS,GAAG;AAC1C,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,0CAA0C;AAAA,UACxL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,eAAe;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,EAAE,MAAM,QAAQ,aAAa,gBAAgB;AAC/C,gBAAI,oBAAoB,cAAc,QAAQ;AAC9C,gBAAI,kBAAkB,aAAa,MAAM,QAAQ,CAAC;AAClD,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,kBAAkB,oBAAoB,gBAAgB,mBAAmB,kBAAkB,oBAAoB,KAAK;AAAA,UACnN;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,gBAAgB;AAC7C,YAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAClC,cAAI,MAAuC;AACzC,gBAAI,UAAU,SAAS,GAAG;AACxB;AAAA,gBACE,iEAAiE,UAAU,SAAS;AAAA,cAEtF;AAAA,YACF,OAAO;AACL,2BAAa,wDAAwD;AAAA,YACvE;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,mBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,gBAAI,GAAG,WAAW,eAAe,CAAC,CAAC,GAAG;AACpC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI,eAAe,KAAK,UAAU,gBAAgB,SAAS,SAAS,KAAK,OAAO;AAC9E,gBAAI,OAAO,eAAe,KAAK;AAC/B,gBAAI,SAAS,UAAU;AACrB,qBAAO,OAAO,KAAK;AAAA,YACrB;AACA,mBAAO;AAAA,UACT,CAAC;AACD,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,SAAS,IAAI,QAAQ,kBAAkB,gBAAgB,wBAAwB,eAAe,IAAI;AAAA,QACnM;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,aAAa;AAC9C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,kDAAkD;AAAA,UAChJ;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,yBAAyB;AAAA,UACvK;AACA,mBAAS,OAAO,WAAW;AACzB,gBAAI,IAAI,WAAW,GAAG,GAAG;AACvB,kBAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC/G,kBAAI,iBAAiB,OAAO;AAC1B,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB,qBAAqB;AACnD,YAAI,CAAC,MAAM,QAAQ,mBAAmB,GAAG;AACvC,iBAAwC,aAAa,wEAAwE,IAAI;AACjI,iBAAO;AAAA,QACT;AAEA,iBAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,cAAI,UAAU,oBAAoB,CAAC;AACnC,cAAI,OAAO,YAAY,YAAY;AACjC;AAAA,cACE,gGACc,yBAAyB,OAAO,IAAI,eAAe,IAAI;AAAA,YACvE;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,gBAAgB,CAAC;AACrB,mBAASC,KAAI,GAAGA,KAAI,oBAAoB,QAAQA,MAAK;AACnD,gBAAIC,WAAU,oBAAoBD,EAAC;AACnC,gBAAI,gBAAgBC,SAAQ,OAAO,UAAU,eAAe,UAAU,cAAc,oBAAoB;AACxG,gBAAI,iBAAiB,MAAM;AACzB,qBAAO;AAAA,YACT;AACA,gBAAI,cAAc,QAAQ,IAAI,cAAc,MAAM,cAAc,GAAG;AACjE,4BAAc,KAAK,cAAc,KAAK,YAAY;AAAA,YACpD;AAAA,UACF;AACA,cAAI,uBAAwB,cAAc,SAAS,IAAK,6BAA6B,cAAc,KAAK,IAAI,IAAI,MAAK;AACrH,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI;AAAA,QACpJ;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,oBAAoB;AAC3B,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,GAAG;AAC5B,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,2BAA2B;AAAA,UAC9I;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,eAAe,UAAU,cAAc,KAAK,MAAM;AAC/E,eAAO,IAAI;AAAA,WACR,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,+FACX,OAAO;AAAA,QAC1F;AAAA,MACF;AAEA,eAAS,uBAAuB,YAAY;AAC1C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,OAAO,YAAY;AAC1B,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,OAAO,YAAY,YAAY;AACjC,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,6BAA6B,YAAY;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AAEA,cAAI,UAAU,OAAO,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU;AACpD,mBAAS,OAAO,SAAS;AACvB,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,IAAI,YAAY,GAAG,KAAK,OAAO,YAAY,YAAY;AACzD,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,CAAC,SAAS;AACZ,qBAAO,IAAI;AAAA,gBACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,qBACjF,KAAK,UAAU,MAAM,QAAQ,GAAG,MAAM,IAAI,IAC7D,mBAAmB,KAAK,UAAU,OAAO,KAAK,UAAU,GAAG,MAAM,IAAI;AAAA,cACvE;AAAA,YACF;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,OAAO,WAAW;AACzB,gBAAQ,OAAO,WAAW;AAAA,UACxB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO,CAAC;AAAA,UACV,KAAK;AACH,gBAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,qBAAO,UAAU,MAAM,MAAM;AAAA,YAC/B;AACA,gBAAI,cAAc,QAAQ,eAAe,SAAS,GAAG;AACnD,qBAAO;AAAA,YACT;AAEA,gBAAI,aAAa,cAAc,SAAS;AACxC,gBAAI,YAAY;AACd,kBAAI,WAAW,WAAW,KAAK,SAAS;AACxC,kBAAI;AACJ,kBAAI,eAAe,UAAU,SAAS;AACpC,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF,OAAO;AAEL,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,QAAQ,KAAK;AACjB,sBAAI,OAAO;AACT,wBAAI,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG;AACrB,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AAEA,mBAAO;AAAA,UACT;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAEA,eAAS,SAAS,UAAU,WAAW;AAErC,YAAI,aAAa,UAAU;AACzB,iBAAO;AAAA,QACT;AAGA,YAAI,CAAC,WAAW;AACd,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,eAAe,MAAM,UAAU;AAC3C,iBAAO;AAAA,QACT;AAGA,YAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;AAC/D,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,eAAS,YAAY,WAAW;AAC9B,YAAI,WAAW,OAAO;AACtB,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,iBAAO;AAAA,QACT;AACA,YAAI,qBAAqB,QAAQ;AAI/B,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,UAAU,SAAS,GAAG;AACjC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAIA,eAAS,eAAe,WAAW;AACjC,YAAI,OAAO,cAAc,eAAe,cAAc,MAAM;AAC1D,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,UAAU;AACzB,cAAI,qBAAqB,MAAM;AAC7B,mBAAO;AAAA,UACT,WAAW,qBAAqB,QAAQ;AACtC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAIA,eAAS,yBAAyB,OAAO;AACvC,YAAI,OAAO,eAAe,KAAK;AAC/B,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,QAAQ;AAAA,UACjB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAGA,eAAS,aAAa,WAAW;AAC/B,YAAI,CAAC,UAAU,eAAe,CAAC,UAAU,YAAY,MAAM;AACzD,iBAAO;AAAA,QACT;AACA,eAAO,UAAU,YAAY;AAAA,MAC/B;AAEA,qBAAe,iBAAiB;AAChC,qBAAe,oBAAoB,eAAe;AAClD,qBAAe,YAAY;AAE3B,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjmBA;AAAA;AAOA,QAAI,MAAuC;AACrC,gBAAU;AAIV,4BAAsB;AAC1B,aAAO,UAAU,kCAAqC,QAAQ,WAAW,mBAAmB;AAAA,IAC9F,OAAO;AAGL,aAAO,UAAU,KAAsC;AAAA,IACzD;AAVM;AAIA;AAAA;AAAA;;;ACZN;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,YAAY;AACpB,YAAQ,cAAc;AACtB,YAAQ,MAAM;AACd,YAAQ,aAAa;AACrB,YAAQ,QAAQ;AAEhB,aAAS,YAAY,OAAoC,UAAkC;AACzF,eAAS,IAAI,GAAG,SAAS,MAAM,QAAQ,IAAI,QAAQ,KAAK;AACtD,YAAI,SAAS,MAAM,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,EAAG,QAAO,MAAM,CAAC;AAAA,MACpE;AAAA,IACF;AACA,aAAS,WAAW,MAAqC;AAEvD,aAAO,OAAO,SAAS,cAAc,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM;AAAA,IAChF;AACA,aAAS,MAAM,KAAoC;AACjD,aAAO,OAAO,QAAQ,YAAY,CAAC,MAAM,GAAG;AAAA,IAC9C;AACA,aAAS,IAAI,GAA4B;AACvC,aAAO,SAAS,GAAG,EAAE;AAAA,IACvB;AACA,aAAS,UAAU,OAAoB,UAAuB,eAAwC;AACpG,UAAI,MAAM,QAAQ,GAAG;AACnB,eAAO,IAAI,MAAM,gBAAgB,QAAQ,cAAc,aAAa,0CAA0C;AAAA,MAChH;AAAA,IACF;AAAA;AAAA;;;AC9BA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,qBAAqB;AAC7B,YAAQ,uBAAuB;AAC/B,YAAQ,UAAU;AAClB,YAAQ,YAAY;AACpB,QAAM,WAAW,CAAC,OAAO,UAAU,KAAK,IAAI;AAC5C,aAAS,YAAwB;AAC/B,UAAI,OAAoB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAG5F,UAAI,OAAO,WAAW,YAAa,QAAO;AAI1C,YAAM,QAAQ,OAAO,UAAU,iBAAiB;AAChD,UAAI,CAAC,MAAO,QAAO;AACnB,UAAI,QAAQ,MAAO,QAAO;AAC1B,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,mBAAmB,MAAM,SAAS,CAAC,CAAC,KAAK,MAAO,QAAO,SAAS,CAAC;AAAA,MACvE;AACA,aAAO;AAAA,IACT;AACA,aAAS,mBAAmB,MAAmB,QAAiC;AAC9E,aAAO,SAAS,GAAG,MAAM,GAAG,iBAAiB,IAAI,CAAC,KAAK;AAAA,IACzD;AACA,aAAS,qBAAqB,MAAmB,QAAiC;AAChF,aAAO,SAAS,IAAI,OAAO,YAAY,CAAC,IAAI,IAAI,KAAK;AAAA,IACvD;AACA,aAAS,iBAAiB,KAA8B;AACtD,UAAI,MAAM;AACV,UAAI,mBAAmB;AACvB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,kBAAkB;AACpB,iBAAO,IAAI,CAAC,EAAE,YAAY;AAC1B,6BAAmB;AAAA,QACrB,WAAW,IAAI,CAAC,MAAM,KAAK;AACzB,6BAAmB;AAAA,QACrB,OAAO;AACL,iBAAO,IAAI,CAAC;AAAA,QACd;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAKA,QAAI,WAAW,QAAQ,UAAW,UAAU;AAAA;AAAA;;;ACnD5C;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,eAAe;AACvB,YAAQ,WAAW;AACnB,YAAQ,sBAAsB;AAC9B,YAAQ,qBAAqB;AAC7B,YAAQ,qBAAqB;AAC7B,YAAQ,WAAW;AACnB,YAAQ,qBAAqB;AAC7B,YAAQ,iBAAiB;AACzB,YAAQ,cAAc;AACtB,YAAQ,aAAa;AACrB,YAAQ,kBAAkB;AAC1B,YAAQ,8BAA8B;AACtC,YAAQ,qBAAqB;AAC7B,YAAQ,cAAc;AACtB,YAAQ,aAAa;AACrB,YAAQ,kBAAkB;AAC1B,YAAQ,cAAc;AACtB,YAAQ,iCAAiC;AACzC,QAAI,SAAS;AACb,QAAI,aAAa,wBAAwB,mBAAsB;AAC/D,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AAErmB,QAAI,sBAAsB;AAC1B,aAAS,gBAAgB,IAAe,UAAoC;AAC1E,UAAI,CAAC,qBAAqB;AACxB,+BAAuB,GAAG,OAAO,aAAa,CAAC,WAAW,yBAAyB,sBAAsB,qBAAqB,kBAAkB,GAAG,SAAU,QAAQ;AAEnK,kBAAQ,GAAG,OAAO,YAAY,GAAG,MAAM,CAAC;AAAA,QAC1C,CAAC;AAAA,MACH;AAIA,UAAI,EAAE,GAAG,OAAO,YAAY,GAAG,mBAAmB,CAAC,EAAG,QAAO;AAG7D,aAAO,GAAG,mBAAmB,EAAE,QAAQ;AAAA,IACzC;AAGA,aAAS,4BAA4B,IAAe,UAAuB,UAAkC;AAC3G,UAAI,OAAO;AACX,SAAG;AACD,YAAI,gBAAgB,MAAM,QAAQ,EAAG,QAAO;AAC5C,YAAI,SAAS,SAAU,QAAO;AAE9B,eAAO,KAAK;AAAA,MACd,SAAS;AACT,aAAO;AAAA,IACT;AACA,aAAS,SAAS,IAAgB,OAAoB,SAAwB,cAAqC;AACjH,UAAI,CAAC,GAAI;AACT,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,GAAG;AAAA,MACL;AAEA,UAAI,GAAG,kBAAkB;AACvB,WAAG,iBAAiB,OAAO,SAAS,OAAO;AAAA,MAC7C,WAAW,GAAG,aAAa;AACzB,WAAG,YAAY,OAAO,OAAO,OAAO;AAAA,MACtC,OAAO;AAEL,WAAG,OAAO,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AACA,aAAS,YAAY,IAAgB,OAAoB,SAAwB,cAAqC;AACpH,UAAI,CAAC,GAAI;AACT,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,GAAG;AAAA,MACL;AAEA,UAAI,GAAG,qBAAqB;AAC1B,WAAG,oBAAoB,OAAO,SAAS,OAAO;AAAA,MAChD,WAAW,GAAG,aAAa;AACzB,WAAG,YAAY,OAAO,OAAO,OAAO;AAAA,MACtC,OAAO;AAEL,WAAG,OAAO,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AACA,aAAS,YAAY,MAAoC;AAGvD,UAAI,SAAS,KAAK;AAClB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,iBAAW,GAAG,OAAO,KAAK,cAAc,cAAc;AACtD,iBAAW,GAAG,OAAO,KAAK,cAAc,iBAAiB;AACzD,aAAO;AAAA,IACT;AACA,aAAS,WAAW,MAAoC;AAGtD,UAAI,QAAQ,KAAK;AACjB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,gBAAU,GAAG,OAAO,KAAK,cAAc,eAAe;AACtD,gBAAU,GAAG,OAAO,KAAK,cAAc,gBAAgB;AACvD,aAAO;AAAA,IACT;AACA,aAAS,YAAY,MAAoC;AACvD,UAAI,SAAS,KAAK;AAClB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,iBAAW,GAAG,OAAO,KAAK,cAAc,UAAU;AAClD,iBAAW,GAAG,OAAO,KAAK,cAAc,aAAa;AACrD,aAAO;AAAA,IACT;AACA,aAAS,WAAW,MAAoC;AACtD,UAAI,QAAQ,KAAK;AACjB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,gBAAU,GAAG,OAAO,KAAK,cAAc,WAAW;AAClD,gBAAU,GAAG,OAAO,KAAK,cAAc,YAAY;AACnD,aAAO;AAAA,IACT;AAKA,aAAS,mBAAmB,KAA2B,cAAgC,OAAyC;AAC9H,YAAM,SAAS,iBAAiB,aAAa,cAAc;AAC3D,YAAM,mBAAmB,SAAS;AAAA,QAChC,MAAM;AAAA,QACN,KAAK;AAAA,MACP,IAAI,aAAa,sBAAsB;AACvC,YAAM,KAAK,IAAI,UAAU,aAAa,aAAa,iBAAiB,QAAQ;AAC5E,YAAM,KAAK,IAAI,UAAU,aAAa,YAAY,iBAAiB,OAAO;AAC1E,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,aAAS,mBAAmB,YAAkC,gBAAgE;AAC5H,YAAM,cAAc,eAAe,YAAY,gBAAgB,IAAI;AACnE,aAAO;AAAA,QACL,EAAE,GAAG,WAAW,oBAAoB,aAAa,WAAW,OAAO,CAAC,GAAG;AAAA,MACzE;AAAA,IACF;AACA,aAAS,mBAAmB,YAAkC,gBAAgE;AAC5H,YAAM,cAAc,eAAe,YAAY,gBAAgB,EAAE;AACjE,aAAO;AAAA,IACT;AACA,aAAS,eAAe,MAAc,gBAAoD,YAAqC;AAC7H,UAAI;AAAA,QACF;AAAA,QACA;AAAA,MACF,IAA0B;AAC1B,UAAI,cAAc,aAAa,CAAC,GAAG,UAAU,IAAI,CAAC,GAAG,UAAU;AAC/D,UAAI,gBAAgB;AAClB,cAAM,WAAW,GAAG,OAAO,eAAe,MAAM,WAAW,eAAe,IAAI,eAAe,IAAI,UAAU;AAC3G,cAAM,WAAW,GAAG,OAAO,eAAe,MAAM,WAAW,eAAe,IAAI,eAAe,IAAI,UAAU;AAC3G,sBAAc,aAAa,QAAQ,KAAK,QAAQ,MAAM;AAAA,MACxD;AACA,aAAO;AAAA,IACT;AACA,aAAS,SAAS,GAAyB,YAAkE;AAC3G,aAAO,EAAE,kBAAkB,GAAG,OAAO,aAAa,EAAE,eAAe,OAAK,eAAe,EAAE,UAAU,KAAK,EAAE,mBAAmB,GAAG,OAAO,aAAa,EAAE,gBAAgB,OAAK,eAAe,EAAE,UAAU;AAAA,IACxM;AACA,aAAS,mBAAmB,GAAsC;AAChE,UAAI,EAAE,iBAAiB,EAAE,cAAc,CAAC,EAAG,QAAO,EAAE,cAAc,CAAC,EAAE;AACrE,UAAI,EAAE,kBAAkB,EAAE,eAAe,CAAC,EAAG,QAAO,EAAE,eAAe,CAAC,EAAE;AAAA,IAC1E;AAOA,aAAS,oBAAoB,KAAqB;AAChD,UAAI,CAAC,IAAK;AACV,UAAI,UAAU,IAAI,eAAe,0BAA0B;AAC3D,UAAI,CAAC,SAAS;AACZ,kBAAU,IAAI,cAAc,OAAO;AACnC,gBAAQ,OAAO;AACf,gBAAQ,KAAK;AACb,gBAAQ,YAAY;AACpB,gBAAQ,aAAa;AACrB,YAAI,qBAAqB,MAAM,EAAE,CAAC,EAAE,YAAY,OAAO;AAAA,MACzD;AACA,UAAI,IAAI,KAAM,cAAa,IAAI,MAAM,uCAAuC;AAAA,IAC9E;AACA,aAAS,+BAA+B,KAAqB;AAE3D,UAAI,OAAO,uBAAuB;AAChC,eAAO,sBAAsB,MAAM;AACjC,iCAAuB,GAAG;AAAA,QAC5B,CAAC;AAAA,MACH,OAAO;AACL,+BAAuB,GAAG;AAAA,MAC5B;AAAA,IACF;AACA,aAAS,uBAAuB,KAAqB;AACnD,UAAI,CAAC,IAAK;AACV,UAAI;AACF,YAAI,IAAI,KAAM,iBAAgB,IAAI,MAAM,uCAAuC;AAE/E,YAAI,IAAI,WAAW;AAEjB,cAAI,UAAU,MAAM;AAAA,QACtB,OAAO;AAGL,gBAAM,aAAa,IAAI,eAAe,QAAQ,aAAa;AAC3D,cAAI,aAAa,UAAU,SAAS,SAAS;AAC3C,sBAAU,gBAAgB;AAAA,UAC5B;AAAA,QACF;AAAA,MACF,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AACA,aAAS,aAAa,IAAsB,WAAwB;AAClE,UAAI,GAAG,WAAW;AAChB,WAAG,UAAU,IAAI,SAAS;AAAA,MAC5B,OAAO;AACL,YAAI,CAAC,GAAG,UAAU,MAAM,IAAI,OAAO,YAAY,SAAS,SAAS,CAAC,GAAG;AACnE,aAAG,aAAa,IAAI,SAAS;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AACA,aAAS,gBAAgB,IAAsB,WAAwB;AACrE,UAAI,GAAG,WAAW;AAChB,WAAG,UAAU,OAAO,SAAS;AAAA,MAC/B,OAAO;AACL,WAAG,YAAY,GAAG,UAAU,QAAQ,IAAI,OAAO,YAAY,SAAS,WAAW,GAAG,GAAG,EAAE;AAAA,MACzF;AAAA,IACF;AAAA;AAAA;;;ACtOA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,WAAW;AACnB,YAAQ,WAAW;AACnB,YAAQ,iBAAiB;AACzB,YAAQ,sBAAsB;AAC9B,YAAQ,mBAAmB;AAC3B,YAAQ,qBAAqB;AAC7B,YAAQ,aAAa;AACrB,QAAI,SAAS;AACb,QAAI,UAAU;AAId,aAAS,iBAAiB,WAA2B,GAAgB,GAAsC;AAEzG,UAAI,CAAC,UAAU,MAAM,OAAQ,QAAO,CAAC,GAAG,CAAC;AAGzC,UAAI;AAAA,QACF;AAAA,MACF,IAAI,UAAU;AACd,eAAS,OAAO,WAAW,WAAW,SAAS,YAAY,MAAM;AACjE,YAAM,OAAO,YAAY,SAAS;AAClC,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,cAAc,cAAc;AAClC,YAAI;AACJ,YAAI,WAAW,UAAU;AACvB,sBAAY,KAAK;AAAA,QACnB,OAAO;AAKL,gBAAM,WAAa,KAAK,YAAY;AACpC,sBAAY,SAAS,cAAc,MAAM;AAAA,QAC3C;AACA,YAAI,EAAE,qBAAqB,YAAY,cAAc;AACnD,gBAAM,IAAI,MAAM,sBAAsB,SAAS,8BAA8B;AAAA,QAC/E;AACA,cAAM,cAAgC;AACtC,cAAM,YAAY,YAAY,iBAAiB,IAAI;AACnD,cAAM,iBAAiB,YAAY,iBAAiB,WAAW;AAE/D,iBAAS;AAAA,UACP,MAAM,CAAC,KAAK,cAAc,GAAG,OAAO,KAAK,eAAe,WAAW,KAAK,GAAG,OAAO,KAAK,UAAU,UAAU;AAAA,UAC3G,KAAK,CAAC,KAAK,aAAa,GAAG,OAAO,KAAK,eAAe,UAAU,KAAK,GAAG,OAAO,KAAK,UAAU,SAAS;AAAA,UACvG,QAAQ,GAAG,QAAQ,YAAY,WAAW,KAAK,GAAG,QAAQ,YAAY,IAAI,IAAI,KAAK,cAAc,GAAG,OAAO,KAAK,eAAe,YAAY,KAAK,GAAG,OAAO,KAAK,UAAU,WAAW;AAAA,UACpL,SAAS,GAAG,QAAQ,aAAa,WAAW,KAAK,GAAG,QAAQ,aAAa,IAAI,IAAI,KAAK,aAAa,GAAG,OAAO,KAAK,eAAe,aAAa,KAAK,GAAG,OAAO,KAAK,UAAU,YAAY;AAAA,QAC1L;AAAA,MACF;AAGA,WAAK,GAAG,OAAO,OAAO,OAAO,KAAK,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,KAAK;AACjE,WAAK,GAAG,OAAO,OAAO,OAAO,MAAM,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,MAAM;AAGnE,WAAK,GAAG,OAAO,OAAO,OAAO,IAAI,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,IAAI;AAC/D,WAAK,GAAG,OAAO,OAAO,OAAO,GAAG,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,GAAG;AAC7D,aAAO,CAAC,GAAG,CAAC;AAAA,IACd;AACA,aAAS,WAAW,MAA6B,UAAuB,UAA6C;AACnH,YAAM,IAAI,KAAK,MAAM,WAAW,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AACjD,YAAM,IAAI,KAAK,MAAM,WAAW,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AACjD,aAAO,CAAC,GAAG,CAAC;AAAA,IACd;AACA,aAAS,SAAS,WAAwC;AACxD,aAAO,UAAU,MAAM,SAAS,UAAU,UAAU,MAAM,SAAS;AAAA,IACrE;AACA,aAAS,SAAS,WAAwC;AACxD,aAAO,UAAU,MAAM,SAAS,UAAU,UAAU,MAAM,SAAS;AAAA,IACrE;AAGA,aAAS,mBAAmB,GAAyB,iBAA+B,eAAyD;AAC3I,YAAM,WAAW,OAAO,oBAAoB,YAAY,GAAG,QAAQ,UAAU,GAAG,eAAe,IAAI;AACnG,UAAI,OAAO,oBAAoB,YAAY,CAAC,SAAU,QAAO;AAC7D,YAAM,OAAO,YAAY,aAAa;AAEtC,YAAM,eAAe,cAAc,MAAM,gBAAgB,KAAK,gBAAgB,KAAK,cAAc;AACjG,cAAQ,GAAG,QAAQ,oBAAoB,YAAY,GAAG,cAAc,cAAc,MAAM,KAAK;AAAA,IAC/F;AAGA,aAAS,eAAe,WAA+B,GAAgB,GAAmC;AACxG,YAAM,UAAU,EAAE,GAAG,OAAO,OAAO,UAAU,KAAK;AAClD,YAAM,OAAO,YAAY,SAAS;AAClC,UAAI,SAAS;AAEX,eAAO;AAAA,UACL;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,MACF,OAAO;AAEL,eAAO;AAAA,UACL;AAAA,UACA,QAAQ,IAAI,UAAU;AAAA,UACtB,QAAQ,IAAI,UAAU;AAAA,UACtB,OAAO,UAAU;AAAA,UACjB,OAAO,UAAU;AAAA,UACjB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,aAAS,oBAAoB,WAA2B,UAAiD;AACvG,YAAM,QAAQ,UAAU,MAAM;AAC9B,aAAO;AAAA,QACL,MAAM,SAAS;AAAA,QACf,GAAG,UAAU,MAAM,IAAI,SAAS,SAAS;AAAA,QACzC,GAAG,UAAU,MAAM,IAAI,SAAS,SAAS;AAAA,QACzC,QAAQ,SAAS,SAAS;AAAA,QAC1B,QAAQ,SAAS,SAAS;AAAA,QAC1B,OAAO,UAAU,MAAM;AAAA,QACvB,OAAO,UAAU,MAAM;AAAA,MACzB;AAAA,IACF;AAGA,aAAS,YAAY,QAAiC;AACpD,aAAO;AAAA,QACL,MAAM,OAAO;AAAA,QACb,KAAK,OAAO;AAAA,QACZ,OAAO,OAAO;AAAA,QACd,QAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AACA,aAAS,YAAY,WAA4D;AAC/E,YAAM,OAAO,UAAU,YAAY;AACnC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC5D;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACpJA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,aAAS,MAAM;AACb,UAAI,OAAW,SAAQ,IAAI,GAAG,SAAS;AAAA,IACzC;AAAA;AAAA;;;ACTA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,YAAY,uBAAuB,mBAAoB;AAC3D,QAAI,UAAU;AACd,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,QAAI,OAAO,uBAAuB,aAAsB;AACxD,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AACrmB,aAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,cAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,IAAG;AACnL,aAAS,eAAe,GAAG;AAAE,UAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,aAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAA,IAAI;AAC1G,aAAS,aAAa,GAAG,GAAG;AAAE,UAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AAAG,UAAI,IAAI,EAAE,OAAO,WAAW;AAAG,UAAI,WAAW,GAAG;AAAE,YAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,YAAI,YAAY,OAAO,EAAG,QAAO;AAAG,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAA,IAAG;AAIvT,QAAM,YAAY;AAAA,MAChB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAGA,QAAI,eAAe,UAAU;AAqC7B,QAAM,gBAAN,cAA4B,MAAM,UAAqC;AAAA,MACrE,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,wBAAgB,MAAM,YAAY,KAAK;AAEvC,wBAAgB,MAAM,SAAS,GAAG;AAClC,wBAAgB,MAAM,SAAS,GAAG;AAClC,wBAAgB,MAAM,mBAAmB,IAAI;AAC7C,wBAAgB,MAAM,WAAW,KAAK;AACtC,wBAAgB,MAAM,mBAAmB,OAAK;AAE5C,eAAK,MAAM,YAAY,CAAC;AAGxB,cAAI,CAAC,KAAK,MAAM,iBAAiB,OAAO,EAAE,WAAW,YAAY,EAAE,WAAW,EAAG,QAAO;AAGxF,gBAAM,WAAW,KAAK,YAAY;AAClC,cAAI,CAAC,YAAY,CAAC,SAAS,iBAAiB,CAAC,SAAS,cAAc,MAAM;AACxE,kBAAM,IAAI,MAAM,2CAA2C;AAAA,UAC7D;AACA,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AAGJ,cAAI,KAAK,MAAM,YAAY,EAAE,EAAE,kBAAkB,cAAc,YAAY,SAAS,KAAK,MAAM,UAAU,EAAE,GAAG,QAAQ,6BAA6B,EAAE,QAAQ,KAAK,MAAM,QAAQ,QAAQ,KAAK,KAAK,MAAM,WAAW,GAAG,QAAQ,6BAA6B,EAAE,QAAQ,KAAK,MAAM,QAAQ,QAAQ,GAAG;AACjS;AAAA,UACF;AAIA,cAAI,EAAE,SAAS,gBAAgB,CAAC,KAAK,MAAM,kBAAmB,GAAE,eAAe;AAK/E,gBAAM,mBAAmB,GAAG,QAAQ,oBAAoB,CAAC;AACzD,eAAK,kBAAkB;AAGvB,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,iBAAiB,IAAI;AAC9E,cAAI,YAAY,KAAM;AACtB,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAC7D,WAAC,GAAG,KAAK,SAAS,sCAAsC,SAAS;AAGjE,WAAC,GAAG,KAAK,SAAS,WAAW,KAAK,MAAM,OAAO;AAC/C,gBAAM,eAAe,KAAK,MAAM,QAAQ,GAAG,SAAS;AACpD,cAAI,iBAAiB,SAAS,KAAK,YAAY,MAAO;AAItD,cAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,qBAAqB,aAAa;AAKnF,eAAK,WAAW;AAChB,eAAK,QAAQ;AACb,eAAK,QAAQ;AAKb,WAAC,GAAG,QAAQ,UAAU,eAAe,aAAa,MAAM,KAAK,UAAU;AACvE,WAAC,GAAG,QAAQ,UAAU,eAAe,aAAa,MAAM,KAAK,cAAc;AAAA,QAC7E,CAAC;AACD,wBAAgB,MAAM,cAAc,OAAK;AAEvC,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,KAAK,iBAAiB,IAAI;AACnF,cAAI,YAAY,KAAM;AACtB,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,cAAI,MAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAClC,gBAAI,SAAS,IAAI,KAAK,OACpB,SAAS,IAAI,KAAK;AACpB,aAAC,QAAQ,MAAM,KAAK,GAAG,aAAa,YAAY,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/E,gBAAI,CAAC,UAAU,CAAC,OAAQ;AACxB,gBAAI,KAAK,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AAAA,UAC5C;AACA,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAC7D,WAAC,GAAG,KAAK,SAAS,iCAAiC,SAAS;AAG5D,gBAAM,eAAe,KAAK,MAAM,OAAO,GAAG,SAAS;AACnD,cAAI,iBAAiB,SAAS,KAAK,YAAY,OAAO;AACpD,gBAAI;AAEF,mBAAK,eAAe,IAAI,WAAW,SAAS,CAAC;AAAA,YAC/C,SAAS,KAAK;AAEZ,oBAAM,QAAU,SAAS,YAAY,aAAa;AAGlD,oBAAM,eAAe,WAAW,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,OAAO,OAAO,OAAO,GAAG,IAAI;AACtG,mBAAK,eAAe,KAAK;AAAA,YAC3B;AACA;AAAA,UACF;AACA,eAAK,QAAQ;AACb,eAAK,QAAQ;AAAA,QACf,CAAC;AACD,wBAAgB,MAAM,kBAAkB,OAAK;AAC3C,cAAI,CAAC,KAAK,SAAU;AACpB,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,KAAK,iBAAiB,IAAI;AACnF,cAAI,YAAY,KAAM;AACtB,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,cAAI,MAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAClC,gBAAI,SAAS,IAAI,KAAK,SAAS;AAC/B,gBAAI,SAAS,IAAI,KAAK,SAAS;AAC/B,aAAC,QAAQ,MAAM,KAAK,GAAG,aAAa,YAAY,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/E,gBAAI,KAAK,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AAAA,UAC5C;AACA,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAG7D,gBAAM,iBAAiB,KAAK,MAAM,OAAO,GAAG,SAAS;AACrD,cAAI,mBAAmB,SAAS,KAAK,YAAY,MAAO,QAAO;AAC/D,gBAAM,WAAW,KAAK,YAAY;AAClC,cAAI,UAAU;AAEZ,gBAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,gCAAgC,SAAS,aAAa;AAAA,UACzG;AACA,WAAC,GAAG,KAAK,SAAS,qCAAqC,SAAS;AAGhE,eAAK,WAAW;AAChB,eAAK,QAAQ;AACb,eAAK,QAAQ;AACb,cAAI,UAAU;AAEZ,aAAC,GAAG,KAAK,SAAS,kCAAkC;AACpD,aAAC,GAAG,QAAQ,aAAa,SAAS,eAAe,aAAa,MAAM,KAAK,UAAU;AACnF,aAAC,GAAG,QAAQ,aAAa,SAAS,eAAe,aAAa,MAAM,KAAK,cAAc;AAAA,UACzF;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,eAAe,OAAK;AACxC,yBAAe,UAAU;AAEzB,iBAAO,KAAK,gBAAgB,CAAC;AAAA,QAC/B,CAAC;AACD,wBAAgB,MAAM,aAAa,OAAK;AACtC,yBAAe,UAAU;AACzB,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,CAAC;AAED,wBAAgB,MAAM,gBAAgB,OAAK;AAEzC,yBAAe,UAAU;AACzB,iBAAO,KAAK,gBAAgB,CAAC;AAAA,QAC/B,CAAC;AACD,wBAAgB,MAAM,cAAc,OAAK;AAEvC,yBAAe,UAAU;AACzB,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,UAAU;AAGf,cAAM,WAAW,KAAK,YAAY;AAClC,YAAI,UAAU;AACZ,WAAC,GAAG,QAAQ,UAAU,UAAU,UAAU,MAAM,OAAO,KAAK,cAAc;AAAA,YACxE,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,aAAK,UAAU;AAGf,cAAM,WAAW,KAAK,YAAY;AAClC,YAAI,UAAU;AACZ,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,UAAU;AAC7E,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,UAAU;AAC7E,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,cAAc;AACjF,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,cAAc;AACjF,WAAC,GAAG,QAAQ,aAAa,UAAU,UAAU,MAAM,OAAO,KAAK,cAAc;AAAA,YAC3E,SAAS;AAAA,UACX,CAAC;AACD,cAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,gCAAgC,aAAa;AAAA,QAChG;AAAA,MACF;AAAA;AAAA;AAAA,MAIA,cAAgC;AAC9B,eAAO,KAAK,OAAO,UAAU,KAAK,OAAO,SAAS,UAAU,UAAU,QAAQ,YAAY,IAAI;AAAA,MAChG;AAAA,MACA,SAAiC;AAG/B,eAAoB,MAAM,aAAa,MAAM,SAAS,KAAK,KAAK,MAAM,QAAQ,GAAG;AAAA;AAAA;AAAA,UAG/E,aAAa,KAAK;AAAA,UAClB,WAAW,KAAK;AAAA;AAAA;AAAA;AAAA,UAIhB,YAAY,KAAK;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AACA,YAAQ,UAAU;AAClB,oBAAgB,eAAe,eAAe,eAAe;AAC7D,oBAAgB,eAAe,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAO1C,eAAe,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQlC,mBAAmB,WAAW,QAAQ;AAAA,MACtC,UAAU,WAAW,QAAQ,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,MAKlC,UAAU,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAM7B,sBAAsB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAKzC,cAAc,SAAU,OAAgC,UAA0C;AAChG,YAAI,MAAM,QAAQ,KAAK,MAAM,QAAQ,EAAE,aAAa,GAAG;AACrD,gBAAM,IAAI,MAAM,8CAA+C;AAAA,QACjE;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAIA,MAAM,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqB1D,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqB3B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkB3B,SAAS,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK5B,SAAS,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK5B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK3B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK3B,aAAa,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIhC,OAAO,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAI1B,WAAW,OAAO;AAAA,MAClB,OAAO,OAAO;AAAA,MACd,WAAW,OAAO;AAAA,IACpB,CAAC;AACD,oBAAgB,eAAe,gBAAgB;AAAA,MAC7C,eAAe;AAAA;AAAA,MAEf,mBAAmB;AAAA,MACnB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,SAAS,WAAY;AAAA,MAAC;AAAA,MACtB,QAAQ,WAAY;AAAA,MAAC;AAAA,MACrB,QAAQ,WAAY;AAAA,MAAC;AAAA,MACrB,aAAa,WAAY;AAAA,MAAC;AAAA,MAC1B,OAAO;AAAA,IACT,CAAC;AAAA;AAAA;;;ACzbD;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,YAAY;AAAA,MACZ,KAAK,WAAY;AACf,eAAO,eAAe;AAAA,MACxB;AAAA,IACF,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,YAAY,uBAAuB,mBAAoB;AAC3D,QAAI,QAAQ;AACZ,QAAI,UAAU;AACd,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,QAAI,iBAAiB,uBAAuB,uBAA0B;AACtE,QAAI,OAAO,uBAAuB,aAAsB;AACxD,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AACrmB,aAAS,WAAW;AAAE,aAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AAAE,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,cAAI,IAAI,UAAU,CAAC;AAAG,mBAAS,KAAK,EAAG,EAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAAI;AAAE,eAAO;AAAA,MAAG,GAAG,SAAS,MAAM,MAAM,SAAS;AAAA,IAAG;AACnR,aAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,cAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,IAAG;AACnL,aAAS,eAAe,GAAG;AAAE,UAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,aAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAA,IAAI;AAC1G,aAAS,aAAa,GAAG,GAAG;AAAE,UAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AAAG,UAAI,IAAI,EAAE,OAAO,WAAW;AAAG,UAAI,WAAW,GAAG;AAAE,YAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,YAAI,YAAY,OAAO,EAAG,QAAO;AAAG,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAA,IAAG;AA8BvT,QAAM,YAAN,cAAwB,MAAM,UAAiD;AAAA;AAAA;AAAA,MAG7E,OAAO,yBAAyB,MAAc,OAA6C;AACzF,YAAI;AAAA,UACF;AAAA,QACF,IAAyB;AACzB,YAAI;AAAA,UACF;AAAA,QACF,IAAyB;AAEzB,YAAI,aAAa,CAAC,qBAAqB,SAAS,MAAM,kBAAkB,KAAK,SAAS,MAAM,kBAAkB,IAAI;AAChH,WAAC,GAAG,KAAK,SAAS,0CAA0C;AAAA,YAC1D;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,YACL,GAAG,SAAS;AAAA,YACZ,GAAG,SAAS;AAAA,YACZ,mBAAmB;AAAA,cACjB,GAAG;AAAA,YACL;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,YAAY,OAA4B;AACtC,cAAM,KAAK;AACX,wBAAgB,MAAM,eAAe,CAAC,GAAG,aAAa;AACpD,WAAC,GAAG,KAAK,SAAS,8BAA8B,QAAQ;AAGxD,gBAAM,cAAc,KAAK,MAAM,QAAQ,IAAI,GAAG,aAAa,qBAAqB,MAAM,QAAQ,CAAC;AAE/F,cAAI,gBAAgB,MAAO,QAAO;AAClC,eAAK,SAAS;AAAA,YACZ,UAAU;AAAA,YACV,SAAS;AAAA,UACX,CAAC;AAAA,QACH,CAAC;AACD,wBAAgB,MAAM,UAAU,CAAC,GAAG,aAAa;AAC/C,cAAI,CAAC,KAAK,MAAM,SAAU,QAAO;AACjC,WAAC,GAAG,KAAK,SAAS,yBAAyB,QAAQ;AACnD,gBAAM,UAAU,GAAG,aAAa,qBAAqB,MAAM,QAAQ;AACnE,gBAAM,WAAW;AAAA,YACf,GAAG,OAAO;AAAA,YACV,GAAG,OAAO;AAAA,YACV,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAGA,cAAI,KAAK,MAAM,QAAQ;AAErB,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI;AAKJ,qBAAS,KAAK,KAAK,MAAM;AACzB,qBAAS,KAAK,KAAK,MAAM;AAGzB,kBAAM,CAAC,WAAW,SAAS,KAAK,GAAG,aAAa,kBAAkB,MAAM,SAAS,GAAG,SAAS,CAAC;AAC9F,qBAAS,IAAI;AACb,qBAAS,IAAI;AAGb,qBAAS,SAAS,KAAK,MAAM,UAAU,IAAI,SAAS;AACpD,qBAAS,SAAS,KAAK,MAAM,UAAU,IAAI,SAAS;AAGpD,mBAAO,IAAI,SAAS;AACpB,mBAAO,IAAI,SAAS;AACpB,mBAAO,SAAS,SAAS,IAAI,KAAK,MAAM;AACxC,mBAAO,SAAS,SAAS,IAAI,KAAK,MAAM;AAAA,UAC1C;AAGA,gBAAM,eAAe,KAAK,MAAM,OAAO,GAAG,MAAM;AAChD,cAAI,iBAAiB,MAAO,QAAO;AACnC,eAAK,SAAS,QAAQ;AAAA,QACxB,CAAC;AACD,wBAAgB,MAAM,cAAc,CAAC,GAAG,aAAa;AACnD,cAAI,CAAC,KAAK,MAAM,SAAU,QAAO;AAGjC,gBAAM,iBAAiB,KAAK,MAAM,OAAO,IAAI,GAAG,aAAa,qBAAqB,MAAM,QAAQ,CAAC;AACjG,cAAI,mBAAmB,MAAO,QAAO;AACrC,WAAC,GAAG,KAAK,SAAS,6BAA6B,QAAQ;AACvD,gBAAM,WAAyC;AAAA,YAC7C,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAIA,gBAAM,aAAa,QAAQ,KAAK,MAAM,QAAQ;AAC9C,cAAI,YAAY;AACd,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI,KAAK,MAAM;AACf,qBAAS,IAAI;AACb,qBAAS,IAAI;AAAA,UACf;AACA,eAAK,SAAS,QAAQ;AAAA,QACxB,CAAC;AACD,aAAK,QAAQ;AAAA;AAAA,UAEX,UAAU;AAAA;AAAA,UAEV,SAAS;AAAA;AAAA,UAET,GAAG,MAAM,WAAW,MAAM,SAAS,IAAI,MAAM,gBAAgB;AAAA,UAC7D,GAAG,MAAM,WAAW,MAAM,SAAS,IAAI,MAAM,gBAAgB;AAAA,UAC7D,mBAAmB;AAAA,YACjB,GAAG,MAAM;AAAA,UACX;AAAA;AAAA,UAEA,QAAQ;AAAA,UACR,QAAQ;AAAA;AAAA,UAER,cAAc;AAAA,QAChB;AACA,YAAI,MAAM,YAAY,EAAE,MAAM,UAAU,MAAM,SAAS;AAErD,kBAAQ,KAAK,2NAAqO;AAAA,QACpP;AAAA,MACF;AAAA,MACA,oBAAoB;AAElB,YAAI,OAAO,OAAO,eAAe,eAAe,KAAK,YAAY,aAAa,OAAO,YAAY;AAC/F,eAAK,SAAS;AAAA,YACZ,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,YAAI,KAAK,MAAM,UAAU;AACvB,eAAK,SAAS;AAAA,YACZ,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACF;AAAA;AAAA;AAAA,MAIA,cAAgC;AAC9B,eAAO,KAAK,OAAO,SAAS,WAAW,UAAU,QAAQ,YAAY,IAAI;AAAA,MAC3E;AAAA,MACA,SAAgC;AAC9B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACL,IAAI,KAAK;AACT,YAAI,QAAQ,CAAC;AACb,YAAI,eAAe;AAGnB,cAAM,aAAa,QAAQ,QAAQ;AACnC,cAAM,YAAY,CAAC,cAAc,KAAK,MAAM;AAC5C,cAAM,gBAAgB,YAAY;AAClC,cAAM,gBAAgB;AAAA;AAAA,UAEpB,IAAI,GAAG,aAAa,UAAU,IAAI,KAAK,YAAY,KAAK,MAAM,IAAI,cAAc;AAAA;AAAA,UAEhF,IAAI,GAAG,aAAa,UAAU,IAAI,KAAK,YAAY,KAAK,MAAM,IAAI,cAAc;AAAA,QAClF;AAGA,YAAI,KAAK,MAAM,cAAc;AAC3B,0BAAgB,GAAG,QAAQ,oBAAoB,eAAe,cAAc;AAAA,QAC9E,OAAO;AAKL,mBAAS,GAAG,QAAQ,oBAAoB,eAAe,cAAc;AAAA,QACvE;AAGA,cAAM,aAAa,GAAG,MAAM,MAAM,SAAS,MAAM,aAAa,IAAI,kBAAkB;AAAA,UAClF,CAAC,wBAAwB,GAAG,KAAK,MAAM;AAAA,UACvC,CAAC,uBAAuB,GAAG,KAAK,MAAM;AAAA,QACxC,CAAC;AAID,eAAoB,MAAM,cAAc,eAAe,SAAS,SAAS,CAAC,GAAG,oBAAoB;AAAA,UAC/F,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,QACf,CAAC,GAAgB,MAAM,aAAa,MAAM,SAAS,KAAK,QAAQ,GAAG;AAAA,UACjE;AAAA,UACA,OAAO;AAAA,YACL,GAAG,SAAS,MAAM;AAAA,YAClB,GAAG;AAAA,UACL;AAAA,UACA,WAAW;AAAA,QACb,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,YAAQ,UAAU;AAClB,oBAAgB,WAAW,eAAe,WAAW;AACrD,oBAAgB,WAAW,aAAa;AAAA;AAAA,MAEtC,GAAG,eAAe,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAc1B,MAAM,WAAW,QAAQ,MAAM,CAAC,QAAQ,KAAK,KAAK,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA2BzD,QAAQ,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,MAAM;AAAA,QAC7D,MAAM,WAAW,QAAQ;AAAA,QACzB,OAAO,WAAW,QAAQ;AAAA,QAC1B,KAAK,WAAW,QAAQ;AAAA,QACxB,QAAQ,WAAW,QAAQ;AAAA,MAC7B,CAAC,GAAG,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAAA,MACjE,kBAAkB,WAAW,QAAQ;AAAA,MACrC,0BAA0B,WAAW,QAAQ;AAAA,MAC7C,yBAAyB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkB5C,iBAAiB,WAAW,QAAQ,MAAM;AAAA,QACxC,GAAG,WAAW,QAAQ;AAAA,QACtB,GAAG,WAAW,QAAQ;AAAA,MACxB,CAAC;AAAA,MACD,gBAAgB,WAAW,QAAQ,MAAM;AAAA,QACvC,GAAG,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC;AAAA,QACtF,GAAG,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC;AAAA,MACxF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqBD,UAAU,WAAW,QAAQ,MAAM;AAAA,QACjC,GAAG,WAAW,QAAQ;AAAA,QACtB,GAAG,WAAW,QAAQ;AAAA,MACxB,CAAC;AAAA;AAAA;AAAA;AAAA,MAID,WAAW,OAAO;AAAA,MAClB,OAAO,OAAO;AAAA,MACd,WAAW,OAAO;AAAA,IACpB,CAAC;AACD,oBAAgB,WAAW,gBAAgB;AAAA,MACzC,GAAG,eAAe,QAAQ;AAAA,MAC1B,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,0BAA0B;AAAA,MAC1B,yBAAyB;AAAA,MACzB,iBAAiB;AAAA,QACf,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA,OAAO;AAAA,IACT,CAAC;AAAA;AAAA;;;AC1YD;AAAA;AAAA;AAEA,QAAM;AAAA,MACJ,SAAS;AAAA,MACT;AAAA,IACF,IAAI;AAKJ,WAAO,UAAU;AACjB,WAAO,QAAQ,UAAU;AACzB,WAAO,QAAQ,gBAAgB;AAAA;AAAA;;;ACZ/B,IAAAC,iBAAA;AAAA;AAAA;AAEA,YAAQ,aAAa;AACrB,YAAQ,eAAe;AACvB,QAAI,SAAS,uBAAuB,eAAgB;AACpD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAC9F,aAAS,QAAQ,QAAQ,gBAAgB;AAAE,UAAI,OAAO,OAAO,KAAK,MAAM;AAAG,UAAI,OAAO,uBAAuB;AAAE,YAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,2BAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,iBAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,QAAY,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,MAAG;AAAE,aAAO;AAAA,IAAM;AACpV,aAAS,cAAc,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,YAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AAAE,0BAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,iBAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AACzf,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,YAAM,eAAe,GAAG;AAAG,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAC3O,aAAS,eAAe,KAAK;AAAE,UAAI,MAAM,aAAa,KAAK,QAAQ;AAAG,aAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AAAA,IAAG;AAC1H,aAAS,aAAa,OAAO,MAAM;AAAE,UAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AAAO,UAAI,OAAO,MAAM,OAAO,WAAW;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAAG,YAAI,OAAO,QAAQ,SAAU,QAAO;AAAK,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AAAA,IAAG;AAExX,aAAS,aAAa,SAAS,OAAO;AACpC,UAAI,MAAM,SAAS,QAAQ,MAAM,OAAO;AACtC,cAAM,QAAQ,cAAc,cAAc,CAAC,GAAG,QAAQ,MAAM,KAAK,GAAG,MAAM,KAAK;AAAA,MACjF;AACA,UAAI,MAAM,aAAa,QAAQ,MAAM,WAAW;AAC9C,cAAM,YAAY,QAAQ,MAAM,YAAY,MAAM,MAAM;AAAA,MAC1D;AACA,aAAoB,OAAO,QAAQ,aAAa,SAAS,KAAK;AAAA,IAChE;AAAA;AAAA;;;ACpBA;AAAA;AAAA;AAEA,YAAQ,aAAa;AACrB,YAAQ,iBAAiB;AACzB,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,kBAAkB;AACtB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAC9F,QAAI,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQnB,MAAM,WAAW,QAAQ,MAAM,CAAC,QAAQ,KAAK,KAAK,MAAM,CAAC;AAAA,MACzD,WAAW,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAI9B,UAAU,WAAW,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIrC,eAAe,WAAW,QAAQ,MAAM;AAAA,QACtC,eAAe,WAAW,QAAQ;AAAA,QAClC,QAAQ,WAAW,QAAQ;AAAA,QAC3B,UAAU,WAAW,QAAQ;AAAA,QAC7B,UAAU,WAAW,QAAQ;AAAA,QAC7B,sBAAsB,WAAW,QAAQ;AAAA,QACzC,cAAc,WAAW,QAAQ;AAAA,QACjC,MAAM,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM;AAAA,QAC1D,QAAQ,WAAW,QAAQ;AAAA,QAC3B,SAAS,WAAW,QAAQ;AAAA,QAC5B,SAAS,WAAW,QAAQ;AAAA,QAC5B,QAAQ,WAAW,QAAQ;AAAA,QAC3B,QAAQ,WAAW,QAAQ;AAAA,QAC3B,aAAa,WAAW,QAAQ;AAAA,QAChC,OAAO,WAAW,QAAQ;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA,MAID,QAAQ,SAAS,SAAS;AACxB,iBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,eAAK,IAAI,IAAI,UAAU,IAAI;AAAA,QAC7B;AACA,YAAI,QAAQ,KAAK,CAAC;AAElB,YAAI,MAAM,SAAS,UAAU,MAAM,SAAS,KAAK;AAC/C,cAAI;AACJ,kBAAQ,oBAAoB,WAAW,QAAQ,QAAQ,WAAW,MAAM,mBAAmB,IAAI;AAAA,QACjG;AACA,eAAO,WAAW,QAAQ,OAAO,MAAM,WAAW,SAAS,IAAI;AAAA,MACjE;AAAA;AAAA;AAAA;AAAA,MAIA,QAAQ,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,MAAM,WAAW,QAAQ,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,MAIvF,YAAY,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM;AAAA,MAChE,iBAAiB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIpC,gBAAgB,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA,MAIpE,gBAAgB,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA,MAIpE,cAAc,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIjC,eAAe,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIlC,UAAU,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAY7B,eAAe,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,MAIhH,gBAAgB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAInC,OAAO,SAAS,QAAQ;AACtB,iBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,eAAK,KAAK,IAAI,UAAU,KAAK;AAAA,QAC/B;AACA,YAAI,QAAQ,KAAK,CAAC;AAElB,YAAI,MAAM,SAAS,UAAU,MAAM,SAAS,KAAK;AAC/C,cAAI;AACJ,kBAAQ,qBAAqB,WAAW,QAAQ,QAAQ,WAAW,MAAM,oBAAoB,IAAI;AAAA,QACnG;AACA,eAAO,WAAW,QAAQ,OAAO,MAAM,WAAW,SAAS,IAAI;AAAA,MACjE;AAAA,IACF;AACA,YAAQ,iBAAiB;AAAA;AAAA;;;ACpHzB;AAAA;AAAA;AAEA,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,kBAAkB;AACtB,QAAI,SAAS;AACb,QAAI,aAAa;AACjB,QAAI,YAAY,CAAC,YAAY,aAAa,iBAAiB,SAAS,UAAU,UAAU,cAAc,mBAAmB,QAAQ,kBAAkB,kBAAkB,YAAY,gBAAgB,iBAAiB,iBAAiB,gBAAgB;AACnP,aAAS,yBAAyB,aAAa;AAAE,UAAI,OAAO,YAAY,WAAY,QAAO;AAAM,UAAI,oBAAoB,oBAAI,QAAQ;AAAG,UAAI,mBAAmB,oBAAI,QAAQ;AAAG,cAAQ,2BAA2B,SAASC,0BAAyBC,cAAa;AAAE,eAAOA,eAAc,mBAAmB;AAAA,MAAmB,GAAG,WAAW;AAAA,IAAG;AAC9U,aAAS,wBAAwB,KAAK,aAAa;AAAE,UAAI,CAAC,eAAe,OAAO,IAAI,YAAY;AAAE,eAAO;AAAA,MAAK;AAAE,UAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;AAAE,eAAO,EAAE,SAAS,IAAI;AAAA,MAAG;AAAE,UAAI,QAAQ,yBAAyB,WAAW;AAAG,UAAI,SAAS,MAAM,IAAI,GAAG,GAAG;AAAE,eAAO,MAAM,IAAI,GAAG;AAAA,MAAG;AAAE,UAAI,SAAS,CAAC;AAAG,UAAI,wBAAwB,OAAO,kBAAkB,OAAO;AAA0B,eAAS,OAAO,KAAK;AAAE,YAAI,QAAQ,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAAE,cAAI,OAAO,wBAAwB,OAAO,yBAAyB,KAAK,GAAG,IAAI;AAAM,cAAI,SAAS,KAAK,OAAO,KAAK,MAAM;AAAE,mBAAO,eAAe,QAAQ,KAAK,IAAI;AAAA,UAAG,OAAO;AAAE,mBAAO,GAAG,IAAI,IAAI,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO,UAAU;AAAK,UAAI,OAAO;AAAE,cAAM,IAAI,KAAK,MAAM;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AACnyB,aAAS,WAAW;AAAE,iBAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,cAAI,SAAS,UAAU,CAAC;AAAG,mBAAS,OAAO,QAAQ;AAAE,gBAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,qBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,YAAG;AAAA,UAAE;AAAA,QAAE;AAAE,eAAO;AAAA,MAAQ;AAAG,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IAAG;AAClV,aAAS,8BAA8B,QAAQ,UAAU;AAAE,UAAI,UAAU,KAAM,QAAO,CAAC;AAAG,UAAI,SAAS,CAAC;AAAG,UAAI,aAAa,OAAO,KAAK,MAAM;AAAG,UAAI,KAAK;AAAG,WAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAAE,cAAM,WAAW,CAAC;AAAG,YAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,eAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAClT,aAAS,QAAQ,QAAQ,gBAAgB;AAAE,UAAI,OAAO,OAAO,KAAK,MAAM;AAAG,UAAI,OAAO,uBAAuB;AAAE,YAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,2BAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,iBAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,QAAY,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,MAAG;AAAE,aAAO;AAAA,IAAM;AACpV,aAAS,cAAc,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,YAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AAAE,0BAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,iBAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AACzf,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,YAAM,eAAe,GAAG;AAAG,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAC3O,aAAS,eAAe,KAAK;AAAE,UAAI,MAAM,aAAa,KAAK,QAAQ;AAAG,aAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AAAA,IAAG;AAC1H,aAAS,aAAa,OAAO,MAAM;AAAE,UAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AAAO,UAAI,OAAO,MAAM,OAAO,WAAW;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAAG,YAAI,OAAO,QAAQ,SAAU,QAAO;AAAK,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AAAA,IAAG;AACxX,aAAS,eAAe,UAAU,YAAY;AAAE,eAAS,YAAY,OAAO,OAAO,WAAW,SAAS;AAAG,eAAS,UAAU,cAAc;AAAU,sBAAgB,UAAU,UAAU;AAAA,IAAG;AAC5L,aAAS,gBAAgB,GAAG,GAAG;AAAE,wBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAASC,iBAAgBC,IAAGC,IAAG;AAAE,QAAAD,GAAE,YAAYC;AAAG,eAAOD;AAAA,MAAG;AAAG,aAAO,gBAAgB,GAAG,CAAC;AAAA,IAAG;AAGvM,QAAI,YAAyB,SAAU,kBAAkB;AACvD,qBAAeE,YAAW,gBAAgB;AAC1C,eAASA,aAAY;AACnB,YAAI;AACJ,iBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,eAAK,IAAI,IAAI,UAAU,IAAI;AAAA,QAC7B;AACA,gBAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAC9E,cAAM,aAAa,CAAC;AACpB,cAAM,iBAAiB;AACvB,cAAM,QAAQ;AACd,eAAO;AAAA,MACT;AACA,UAAI,SAASA,WAAU;AACvB,aAAO,uBAAuB,SAAS,uBAAuB;AAC5D,aAAK,UAAU;AAAA,MACjB;AACA,aAAO,YAAY,SAAS,YAAY;AACtC,aAAK,iBAAiB,KAAK,QAAQ;AAAA,MACrC;AAIA,aAAO,iBAAiB,SAAS,eAAe,OAAO,QAAQ;AAC7D,YAAI,cAAc,KAAK,OACrB,iBAAiB,YAAY,gBAC7B,iBAAiB,YAAY,gBAC7B,kBAAkB,YAAY;AAEhC,YAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,gBAAiB,QAAO,CAAC,OAAO,MAAM;AAGjF,YAAI,iBAAiB;AACnB,cAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,MAAM;AAC1C,cAAI,SAAS,QAAQ,KAAK,MAAM;AAChC,cAAI,SAAS,SAAS,KAAK,MAAM;AAMjC,cAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,SAAS,KAAK,GAAG;AAC/C,qBAAS,QAAQ;AAAA,UACnB,OAAO;AACL,oBAAQ,SAAS;AAAA,UACnB;AAAA,QACF;AACA,YAAI,OAAO,OACT,OAAO;AAKT,YAAI,OAAO,KAAK,SAAS,CAAC,GAAG,CAAC,GAC5B,SAAS,KAAK,CAAC,GACf,SAAS,KAAK,CAAC;AACjB,iBAAS;AACT,kBAAU;AACV,YAAI,gBAAgB;AAClB,kBAAQ,KAAK,IAAI,eAAe,CAAC,GAAG,KAAK;AACzC,mBAAS,KAAK,IAAI,eAAe,CAAC,GAAG,MAAM;AAAA,QAC7C;AACA,YAAI,gBAAgB;AAClB,kBAAQ,KAAK,IAAI,eAAe,CAAC,GAAG,KAAK;AACzC,mBAAS,KAAK,IAAI,eAAe,CAAC,GAAG,MAAM;AAAA,QAC7C;AAGA,aAAK,QAAQ,CAAC,UAAU,OAAO,QAAQ,UAAU,OAAO,OAAO;AAC/D,eAAO,CAAC,OAAO,MAAM;AAAA,MACvB;AAQA,aAAO,gBAAgB,SAAS,cAAc,aAAa,MAAM;AAC/D,YAAI,SAAS;AACb,eAAO,SAAU,GAAG,OAAO;AACzB,cAAI,OAAO,MAAM,MACf,SAAS,MAAM,QACf,SAAS,MAAM;AAEjB,cAAI,gBAAgB,gBAAiB,QAAO,UAAU;AAGtD,cAAI,YAAY,OAAO,MAAM,SAAS,UAAU,OAAO,MAAM,SAAS,QAAQ,SAAS,OAAO,SAAS;AACvG,cAAI,YAAY,OAAO,MAAM,SAAS,UAAU,OAAO,MAAM,SAAS,QAAQ,SAAS,OAAO,SAAS;AAEvG,cAAI,CAAC,YAAY,CAAC,SAAU;AAG5B,cAAI,QAAQ,KAAK,CAAC;AAClB,cAAI,QAAQ,KAAK,KAAK,SAAS,CAAC;AAKhC,cAAI,aAAa,KAAK,sBAAsB;AAC5C,cAAI,OAAO,kBAAkB,MAAM;AAIjC,gBAAI,UAAU,KAAK;AACjB,kBAAI,qBAAqB,WAAW,OAAO,OAAO,eAAe;AACjE,wBAAU;AAAA,YACZ;AACA,gBAAI,UAAU,KAAK;AACjB,kBAAI,oBAAoB,WAAW,MAAM,OAAO,eAAe;AAC/D,wBAAU;AAAA,YACZ;AAAA,UACF;AAEA,iBAAO,iBAAiB;AAGxB,cAAI,UAAU,IAAK,UAAS,CAAC;AAC7B,cAAI,UAAU,IAAK,UAAS,CAAC;AAG7B,cAAI,QAAQ,OAAO,MAAM,SAAS,WAAW,SAAS,OAAO,MAAM,iBAAiB;AACpF,cAAI,SAAS,OAAO,MAAM,UAAU,WAAW,SAAS,OAAO,MAAM,iBAAiB;AAGtF,cAAI,wBAAwB,OAAO,eAAe,OAAO,MAAM;AAC/D,kBAAQ,sBAAsB,CAAC;AAC/B,mBAAS,sBAAsB,CAAC;AAChC,cAAI,oBAAoB,UAAU,OAAO,MAAM,SAAS,WAAW,OAAO,MAAM;AAGhF,cAAI,KAAK,OAAO,OAAO,MAAM,WAAW,MAAM,aAAa,OAAO,MAAM,WAAW,IAAI;AAEvF,cAAI,eAAe,gBAAgB,cAAc,CAAC;AAClD,cAAI,MAAM,CAAC,cAAc;AACvB,cAAE,WAAW,OAAO,SAAS,EAAE,QAAQ;AACvC,eAAG,GAAG;AAAA,cACJ;AAAA,cACA,MAAM;AAAA,gBACJ;AAAA,gBACA;AAAA,cACF;AAAA,cACA,QAAQ;AAAA,YACV,CAAC;AAAA,UACH;AAGA,cAAI,gBAAgB,eAAgB,QAAO,UAAU;AAAA,QACvD;AAAA,MACF;AAKA,aAAO,qBAAqB,SAAS,mBAAmB,YAAY,KAAK;AACvE,YAAI,SAAS,KAAK,MAAM;AAExB,YAAI,CAAC,QAAQ;AACX,iBAAoB,MAAM,cAAc,QAAQ;AAAA,YAC9C,WAAW,mDAAmD;AAAA,YAC9D;AAAA,UACF,CAAC;AAAA,QACH;AAGA,YAAI,OAAO,WAAW,YAAY;AAChC,iBAAO,OAAO,YAAY,GAAG;AAAA,QAC/B;AAEA,YAAI,eAAe,OAAO,OAAO,SAAS;AAC1C,YAAI,QAAQ,cAAc;AAAA,UACxB;AAAA,QACF,GAAG,eAAe,CAAC,IAAI;AAAA,UACrB;AAAA,QACF,CAAC;AACD,eAAoB,MAAM,aAAa,QAAQ,KAAK;AAAA,MACtD;AACA,aAAO,SAAS,SAAS,SAAS;AAChC,YAAI,SAAS;AAGb,YAAI,eAAe,KAAK,OACtB,WAAW,aAAa,UACxB,YAAY,aAAa,WACzB,gBAAgB,aAAa,eAC7B,QAAQ,aAAa,OACrB,SAAS,aAAa,QACtB,SAAS,aAAa,QACtB,aAAa,aAAa,YAC1B,kBAAkB,aAAa,iBAC/B,OAAO,aAAa,MACpB,iBAAiB,aAAa,gBAC9B,iBAAiB,aAAa,gBAC9B,WAAW,aAAa,UACxB,eAAe,aAAa,cAC5B,gBAAgB,aAAa,eAC7B,gBAAgB,aAAa,eAC7B,iBAAiB,aAAa,gBAC9B,IAAI,8BAA8B,cAAc,SAAS;AAM3D,gBAAQ,GAAG,OAAO,cAAc,UAAU,cAAc,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;AAAA,UAChF,YAAY,YAAY,YAAY,MAAM,MAAM;AAAA,UAChD,UAAU,CAAC,EAAE,OAAO,SAAS,MAAM,UAAU,cAAc,IAAI,SAAU,YAAY;AACnF,gBAAI;AAEJ,gBAAI,OAAO,wBAAwB,OAAO,WAAW,UAAU,MAAM,OAAO,wBAAwB,OAAO,WAAW,UAAU,IAAiB,MAAM,UAAU;AACjK,mBAAoB,MAAM,cAAc,gBAAgB,eAAe,SAAS,CAAC,GAAG,eAAe;AAAA,cACjG,SAAS;AAAA,cACT,KAAK,qBAAqB;AAAA,cAC1B,QAAQ,OAAO,cAAc,gBAAgB,UAAU;AAAA,cACvD,SAAS,OAAO,cAAc,iBAAiB,UAAU;AAAA,cACzD,QAAQ,OAAO,cAAc,YAAY,UAAU;AAAA,YACrD,CAAC,GAAG,OAAO,mBAAmB,YAAY,GAAG,CAAC;AAAA,UAChD,CAAC,CAAC;AAAA,QACJ,CAAC,CAAC;AAAA,MACJ;AACA,aAAOA;AAAA,IACT,EAAE,MAAM,SAAS;AACjB,YAAQ,UAAU;AAClB,cAAU,YAAY,WAAW;AACjC,cAAU,eAAe;AAAA,MACvB,MAAM;AAAA,MACN,YAAY,CAAC,IAAI,EAAE;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB,CAAC,IAAI,EAAE;AAAA,MACvB,gBAAgB,CAAC,UAAU,QAAQ;AAAA,MACnC,eAAe,CAAC,IAAI;AAAA,MACpB,gBAAgB;AAAA,IAClB;AAAA;AAAA;;;AC/PA;AAAA;AAAA;AAEA,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,aAAa,uBAAuB,mBAAsB;AAC9D,QAAI,cAAc;AAClB,QAAI,YAAY,CAAC,UAAU,cAAc,YAAY,iBAAiB,gBAAgB,iBAAiB,kBAAkB,kBAAkB,mBAAmB,QAAQ,SAAS,UAAU,iBAAiB,SAAS,gBAAgB;AACnO,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAC9F,aAAS,yBAAyB,aAAa;AAAE,UAAI,OAAO,YAAY,WAAY,QAAO;AAAM,UAAI,oBAAoB,oBAAI,QAAQ;AAAG,UAAI,mBAAmB,oBAAI,QAAQ;AAAG,cAAQ,2BAA2B,SAASC,0BAAyBC,cAAa;AAAE,eAAOA,eAAc,mBAAmB;AAAA,MAAmB,GAAG,WAAW;AAAA,IAAG;AAC9U,aAAS,wBAAwB,KAAK,aAAa;AAAE,UAAI,CAAC,eAAe,OAAO,IAAI,YAAY;AAAE,eAAO;AAAA,MAAK;AAAE,UAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;AAAE,eAAO,EAAE,SAAS,IAAI;AAAA,MAAG;AAAE,UAAI,QAAQ,yBAAyB,WAAW;AAAG,UAAI,SAAS,MAAM,IAAI,GAAG,GAAG;AAAE,eAAO,MAAM,IAAI,GAAG;AAAA,MAAG;AAAE,UAAI,SAAS,CAAC;AAAG,UAAI,wBAAwB,OAAO,kBAAkB,OAAO;AAA0B,eAAS,OAAO,KAAK;AAAE,YAAI,QAAQ,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAAE,cAAI,OAAO,wBAAwB,OAAO,yBAAyB,KAAK,GAAG,IAAI;AAAM,cAAI,SAAS,KAAK,OAAO,KAAK,MAAM;AAAE,mBAAO,eAAe,QAAQ,KAAK,IAAI;AAAA,UAAG,OAAO;AAAE,mBAAO,GAAG,IAAI,IAAI,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO,UAAU;AAAK,UAAI,OAAO;AAAE,cAAM,IAAI,KAAK,MAAM;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AACnyB,aAAS,WAAW;AAAE,iBAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,cAAI,SAAS,UAAU,CAAC;AAAG,mBAAS,OAAO,QAAQ;AAAE,gBAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,qBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,YAAG;AAAA,UAAE;AAAA,QAAE;AAAE,eAAO;AAAA,MAAQ;AAAG,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IAAG;AAClV,aAAS,QAAQ,QAAQ,gBAAgB;AAAE,UAAI,OAAO,OAAO,KAAK,MAAM;AAAG,UAAI,OAAO,uBAAuB;AAAE,YAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,2BAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,iBAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,QAAY,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,MAAG;AAAE,aAAO;AAAA,IAAM;AACpV,aAAS,cAAc,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,YAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AAAE,0BAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,iBAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AACzf,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,YAAM,eAAe,GAAG;AAAG,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAC3O,aAAS,eAAe,KAAK;AAAE,UAAI,MAAM,aAAa,KAAK,QAAQ;AAAG,aAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AAAA,IAAG;AAC1H,aAAS,aAAa,OAAO,MAAM;AAAE,UAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AAAO,UAAI,OAAO,MAAM,OAAO,WAAW;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAAG,YAAI,OAAO,QAAQ,SAAU,QAAO;AAAK,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AAAA,IAAG;AACxX,aAAS,8BAA8B,QAAQ,UAAU;AAAE,UAAI,UAAU,KAAM,QAAO,CAAC;AAAG,UAAI,SAAS,CAAC;AAAG,UAAI,aAAa,OAAO,KAAK,MAAM;AAAG,UAAI,KAAK;AAAG,WAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAAE,cAAM,WAAW,CAAC;AAAG,YAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,eAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAClT,aAAS,eAAe,UAAU,YAAY;AAAE,eAAS,YAAY,OAAO,OAAO,WAAW,SAAS;AAAG,eAAS,UAAU,cAAc;AAAU,sBAAgB,UAAU,UAAU;AAAA,IAAG;AAC5L,aAAS,gBAAgB,GAAG,GAAG;AAAE,wBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAASC,iBAAgBC,IAAGC,IAAG;AAAE,QAAAD,GAAE,YAAYC;AAAG,eAAOD;AAAA,MAAG;AAAG,aAAO,gBAAgB,GAAG,CAAC;AAAA,IAAG;AACvM,QAAI,eAA4B,SAAU,kBAAkB;AAC1D,qBAAeE,eAAc,gBAAgB;AAC7C,eAASA,gBAAe;AACtB,YAAI;AACJ,iBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,eAAK,IAAI,IAAI,UAAU,IAAI;AAAA,QAC7B;AACA,gBAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAC9E,cAAM,QAAQ;AAAA,UACZ,OAAO,MAAM,MAAM;AAAA,UACnB,QAAQ,MAAM,MAAM;AAAA,UACpB,YAAY,MAAM,MAAM;AAAA,UACxB,aAAa,MAAM,MAAM;AAAA,QAC3B;AACA,cAAM,WAAW,SAAU,GAAG,MAAM;AAClC,cAAI,OAAO,KAAK;AAChB,cAAI,MAAM,MAAM,UAAU;AACxB,cAAE,WAAW,OAAO,SAAS,EAAE,QAAQ;AACvC,kBAAM,SAAS,MAAM,WAAY;AAC/B,qBAAO,MAAM,MAAM,YAAY,MAAM,MAAM,SAAS,GAAG,IAAI;AAAA,YAC7D,CAAC;AAAA,UACH,OAAO;AACL,kBAAM,SAAS,IAAI;AAAA,UACrB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,MAAAA,cAAa,2BAA2B,SAAS,yBAAyB,OAAO,OAAO;AAEtF,YAAI,MAAM,eAAe,MAAM,SAAS,MAAM,gBAAgB,MAAM,QAAQ;AAC1E,iBAAO;AAAA,YACL,OAAO,MAAM;AAAA,YACb,QAAQ,MAAM;AAAA,YACd,YAAY,MAAM;AAAA,YAClB,aAAa,MAAM;AAAA,UACrB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,UAAI,SAASA,cAAa;AAC1B,aAAO,SAAS,SAAS,SAAS;AAIhC,YAAI,cAAc,KAAK,OACrB,SAAS,YAAY,QACrB,aAAa,YAAY,YACzB,WAAW,YAAY,UACvB,gBAAgB,YAAY,eAC5B,eAAe,YAAY,cAC3B,gBAAgB,YAAY,eAC5B,iBAAiB,YAAY,gBAC7B,iBAAiB,YAAY,gBAC7B,kBAAkB,YAAY,iBAC9B,OAAO,YAAY,MACnB,QAAQ,YAAY,OACpB,SAAS,YAAY,QACrB,gBAAgB,YAAY,eAC5B,QAAQ,YAAY,OACpB,iBAAiB,YAAY,gBAC7B,QAAQ,8BAA8B,aAAa,SAAS;AAC9D,eAAoB,MAAM,cAAc,WAAW,SAAS;AAAA,UAC1D;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,QAAQ,KAAK,MAAM;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU,KAAK;AAAA,UACf;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,KAAK,MAAM;AAAA,QACpB,GAAgB,MAAM,cAAc,OAAO,SAAS,CAAC,GAAG,OAAO;AAAA,UAC7D,OAAO,cAAc,cAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,YACjD,OAAO,KAAK,MAAM,QAAQ;AAAA,YAC1B,QAAQ,KAAK,MAAM,SAAS;AAAA,UAC9B,CAAC;AAAA,QACH,CAAC,CAAC,CAAC;AAAA,MACL;AACA,aAAOA;AAAA,IACT,EAAE,MAAM,SAAS;AACjB,YAAQ,UAAU;AAElB,iBAAa,YAAY,cAAc,cAAc,CAAC,GAAG,YAAY,cAAc,GAAG,CAAC,GAAG;AAAA,MACxF,UAAU,WAAW,QAAQ;AAAA,IAC/B,CAAC;AAAA;AAAA;;;AC9GD;AAAA;AAAA;AACA,WAAO,UAAU,WAAW;AAC1B,YAAM,IAAI,MAAM,gFAAgF;AAAA,IAClG;AAEA,WAAO,QAAQ,YAAY,oBAA6B;AACxD,WAAO,QAAQ,eAAe,uBAAgC;AAAA;AAAA;;;ACN9D;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,mBAAmB,QAAQ,uBAAuB,QAAQ,UAAU;AAC5E,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,SAAS,uBAAuB,eAAgB;AACpD,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AAkCpF,QAAM,uBAA4D,QAAQ,uBAAuB,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,CAAC,CAAC;AAElM,QAAM,mBAAwD,QAAQ,mBAAmB,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,MAAM,WAAW,QAAQ,IAAI,CAAC;AAgDxK,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,MAI/B,WAAW,WAAW,QAAQ;AAAA,MAC9B,OAAO,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAI1B,OAAO,WAAW,QAAQ;AAAA;AAAA,MAE1B,UAAU,WAAW,QAAQ;AAAA;AAAA,MAE7B,MAAM,WAAW,QAAQ;AAAA;AAAA,MAEzB,iBAAiB,WAAW,QAAQ;AAAA;AAAA,MAEpC,iBAAiB,WAAW,QAAQ;AAAA;AAAA,MAEpC,iBAAiB,SAAU,OAAmB;AAC5C,YAAI,MAAM,oBAAoB,SAAS,MAAuC;AAC5E,kBAAQ;AAAA;AAAA,YAER;AAAA,UAA0I;AAAA,QAC5I;AAAA,MACF;AAAA;AAAA,MAEA,aAAc,WAAW,QAAQ,MAAM,CAAC,YAAY,YAAY,CAAC;AAAA;AAAA;AAAA,MAGjE,QAAQ,SAAU,OAAmB;AACnC,YAAI,SAAS,MAAM;AAEnB,YAAI,WAAW,OAAW;AAC1B,wBAAmB,eAAe,QAAQ,QAAQ;AAAA,MACpD;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,QAAS,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM;AAAA;AAAA,MAE7D,kBAAmB,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM;AAAA;AAAA,MAEvE,WAAW,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAM9B,SAAS,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAI5B,WAAW,WAAW,QAAQ;AAAA,MAC9B,aAAa,WAAW,QAAQ;AAAA,MAChC,aAAa,WAAW,QAAQ;AAAA;AAAA,MAEhC,cAAc,WAAW,QAAQ;AAAA;AAAA,MAEjC,kBAAkB,WAAW,QAAQ;AAAA;AAAA,MAErC,kBAAkB,WAAW,QAAQ;AAAA;AAAA,MAErC,gBAAgB,WAAW,QAAQ;AAAA;AAAA,MAEnC,aAAa,WAAW,QAAQ;AAAA;AAAA,MAEhC,eAAe;AAAA,MACf,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,MAMd,gBAAgB,WAAW,QAAQ;AAAA;AAAA;AAAA,MAGnC,aAAa,WAAW,QAAQ;AAAA;AAAA,MAEhC,QAAQ,WAAW,QAAQ;AAAA;AAAA,MAE3B,YAAY,WAAW,QAAQ;AAAA;AAAA,MAE/B,eAAe,WAAW,QAAQ;AAAA;AAAA,MAElC,UAAU,WAAW,QAAQ;AAAA;AAAA,MAE7B,cAAc,WAAW,QAAQ;AAAA;AAAA,MAEjC,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAK3B,cAAe,WAAW,QAAQ,MAAM;AAAA,QACtC,GAAG,WAAW,QAAQ,OAAO;AAAA,QAC7B,GAAG,WAAW,QAAQ,OAAO;AAAA,QAC7B,GAAG,WAAW,QAAQ,OAAO;AAAA,MAC/B,CAAC;AAAA;AAAA,MAED,UAAU,SAAU,OAAmB,UAAuB;AAC5D,cAAM,WAAW,MAAM,QAAQ;AAG/B,cAAM,OAAO,CAAC;AACd,eAAO,QAAQ,SAAS,QAAQ,UAAU,SAAU,OAAO;AACzD,cAAI,OAAO,OAAO,KAAM;AACxB,cAAI,KAAK,MAAM,GAAG,GAAG;AACnB,kBAAM,IAAI,MAAM,0BAA0B,MAAM,MAAM,uDAAuD;AAAA,UAC/G;AACA,eAAK,MAAM,GAAG,IAAI;AAAA,QACpB,CAAC;AAAA,MACH;AAAA;AAAA,MAEA,UAAU,WAAW,QAAQ;AAAA,IAC/B;AAAA;AAAA;;;ACjNA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,SAAS,uBAAuB,eAAgB;AACpD,QAAI,YAAY;AAChB,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AACtB,QAAI,SAAS;AACb,QAAI,kBAAkB;AACtB,QAAI,4BAA4B;AAChC,QAAI,QAAQ,uBAAuB,cAAe;AAClD,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,cAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,IAAG;AACnL,aAAS,eAAe,GAAG;AAAE,UAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,aAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAA,IAAI;AAC1G,aAAS,aAAa,GAAG,GAAG;AAAE,UAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AAAG,UAAI,IAAI,EAAE,OAAO,WAAW;AAAG,UAAI,WAAW,GAAG;AAAE,YAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,YAAI,YAAY,OAAO,EAAG,QAAO;AAAG,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAA,IAAG;AA2FvT,QAAM,WAAN,cAAuB,OAAO,QAAQ,UAA+B;AAAA,MACnE,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,wBAAgB,MAAM,SAAS;AAAA,UAC7B,UAAU;AAAA,UACV,UAAU;AAAA,UACV,WAAW;AAAA,QACb,CAAC;AACD,wBAAgB,MAAM,cAA2B,OAAO,QAAQ,UAAU,CAAC;AAM3E,wBAAgB,MAAM,eAAe,CAAC,GAAG,SAAS;AAChD,cAAI;AAAA,YACF;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,KAAK;AACT,cAAI,CAAC,YAAa;AAClB,gBAAM,cAAoC;AAAA,YACxC,KAAK;AAAA,YACL,MAAM;AAAA,UACR;AAGA,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,cAAI,CAAC,aAAc;AACnB,gBAAM,aAAa,aAAa,sBAAsB;AACtD,gBAAM,aAAa,KAAK,sBAAsB;AAC9C,gBAAM,QAAQ,WAAW,OAAO;AAChC,gBAAM,QAAQ,WAAW,OAAO;AAChC,gBAAM,OAAO,WAAW,MAAM;AAC9B,gBAAM,OAAO,WAAW,MAAM;AAC9B,sBAAY,OAAO,QAAQ,QAAQ,aAAa;AAChD,sBAAY,MAAM,OAAO,OAAO,aAAa;AAC7C,eAAK,SAAS;AAAA,YACZ,UAAU;AAAA,UACZ,CAAC;AAGD,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,KAAK,GAAG,gBAAgB,QAAQ,KAAK,kBAAkB,GAAG,YAAY,KAAK,YAAY,MAAM,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC;AACvH,iBAAO,YAAY,KAAK,MAAM,KAAK,MAAM,GAAG,GAAG,GAAG;AAAA,YAChD;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAOD,wBAAgB,MAAM,UAAU,CAAC,GAAG,OAAO,cAAc;AACvD,cAAI;AAAA,YACF;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,cAAI,CAAC,OAAQ;AACb,cAAI,CAAC,KAAK,MAAM,UAAU;AACxB,kBAAM,IAAI,MAAM,mCAAmC;AAAA,UACrD;AACA,cAAI,MAAM,KAAK,MAAM,SAAS,MAAM;AACpC,cAAI,OAAO,KAAK,MAAM,SAAS,OAAO;AACtC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI,KAAK;AACT,gBAAM,iBAAiB,KAAK,kBAAkB;AAG9C,cAAI,WAAW;AACb,kBAAM;AAAA,cACJ;AAAA,YACF,IAAI;AACJ,gBAAI,cAAc;AAChB,oBAAM;AAAA,gBACJ;AAAA,gBACA;AAAA,cACF,IAAI,KAAK;AACT,oBAAM,iBAAiB,aAAa,gBAAgB,GAAG,gBAAgB,kBAAkB,GAAG,WAAW,OAAO,CAAC,CAAC;AAChH,qBAAO,GAAG,gBAAgB,OAAO,KAAK,GAAG,cAAc;AACvD,oBAAM,YAAY,GAAG,gBAAgB,kBAAkB,cAAc;AACrE,oBAAM,gBAAgB,kBAAkB,GAAG,gBAAgB,kBAAkB,GAAG,UAAU,OAAO,CAAC,CAAC;AACnG,sBAAQ,GAAG,gBAAgB,OAAO,MAAM,GAAG,aAAa;AAAA,YAC1D;AAAA,UACF;AACA,gBAAM,cAAoC;AAAA,YACxC;AAAA,YACA;AAAA,UACF;AAGA,cAAI,WAAW;AACb,iBAAK,SAAS;AAAA,cACZ,UAAU;AAAA,YACZ,CAAC;AAAA,UACH,OAAO;AACL,aAAC,GAAG,UAAU,WAAW,MAAM;AAC7B,mBAAK,SAAS;AAAA,gBACZ,UAAU;AAAA,cACZ,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AAGA,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,KAAK,GAAG,gBAAgB,QAAQ,gBAAgB,KAAK,MAAM,GAAG,CAAC;AAC/D,iBAAO,OAAO,KAAK,MAAM,GAAG,GAAG,GAAG;AAAA,YAChC;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAMD,wBAAgB,MAAM,cAAc,CAAC,GAAG,UAAU;AAChD,cAAI;AAAA,YACF;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,cAAI,CAAC,WAAY;AACjB,cAAI,CAAC,KAAK,MAAM,UAAU;AACxB,kBAAM,IAAI,MAAM,sCAAsC;AAAA,UACxD;AACA,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI,KAAK;AACT,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,KAAK,MAAM;AACf,gBAAM,cAAoC;AAAA,YACxC;AAAA,YACA;AAAA,UACF;AACA,eAAK,SAAS;AAAA,YACZ,UAAU;AAAA,UACZ,CAAC;AACD,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,KAAK,GAAG,gBAAgB,QAAQ,KAAK,kBAAkB,GAAG,KAAK,MAAM,GAAG,CAAC;AACzE,iBAAO,WAAW,KAAK,MAAM,GAAG,GAAG,GAAG;AAAA,YACpC;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAMD,wBAAgB,MAAM,gBAAgB,CAAC,GAAG,cAAc,aAAa,KAAK,gBAAgB,GAAG,cAAc,UAAU,cAAc,CAAC;AAEpI,wBAAgB,MAAM,iBAAiB,CAAC,GAAG,cAAc,aAAa,KAAK,gBAAgB,GAAG,cAAc,UAAU,eAAe,CAAC;AAEtI,wBAAgB,MAAM,YAAY,CAAC,GAAG,cAAc,aAAa,KAAK,gBAAgB,GAAG,cAAc,UAAU,UAAU,CAAC;AAAA,MAC9H;AAAA,MACA,sBAAsB,WAAuB,WAAoC;AAG/E,YAAI,KAAK,MAAM,aAAa,UAAU,SAAU,QAAO;AACvD,YAAI,KAAK,MAAM,qBAAqB,UAAU,iBAAkB,QAAO;AAEvE,cAAM,eAAe,GAAG,gBAAgB,sBAAsB,KAAK,kBAAkB,KAAK,KAAK,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,KAAK;AACpK,cAAM,eAAe,GAAG,gBAAgB,sBAAsB,KAAK,kBAAkB,SAAS,GAAG,UAAU,GAAG,UAAU,GAAG,UAAU,GAAG,UAAU,GAAG,SAAS;AAC9J,eAAO,EAAE,GAAG,OAAO,mBAAmB,aAAa,WAAW,KAAK,KAAK,MAAM,qBAAqB,UAAU;AAAA,MAC/G;AAAA,MACA,oBAAoB;AAClB,aAAK,iBAAiB,CAAC,CAAC;AAAA,MAC1B;AAAA,MACA,mBAAmB,WAAuB;AACxC,aAAK,iBAAiB,SAAS;AAAA,MACjC;AAAA;AAAA;AAAA,MAIA,iBAAiB,WAAuB;AACtC,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,KAAK;AACT,YAAI,CAAC,iBAAkB;AACvB,cAAM,OAAO,KAAK,WAAW;AAE7B,YAAI,CAAC,KAAM;AACX,cAAM,uBAAuB,UAAU,oBAAoB;AAAA,UACzD,MAAM;AAAA,UACN,KAAK;AAAA,QACP;AACA,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,KAAK;AACT,cAAM,aAAa,YAAY,iBAAiB,SAAS,qBAAqB,QAAQ,iBAAiB,QAAQ,qBAAqB;AACpI,YAAI,CAAC,UAAU;AACb,eAAK,YAAY,iBAAiB,GAAG;AAAA,YACnC;AAAA,YACA,QAAQ,iBAAiB;AAAA,YACzB,QAAQ,iBAAiB;AAAA,UAC3B,CAAC;AAAA,QACH,WAAW,YAAY;AACrB,gBAAM,SAAS,iBAAiB,OAAO,SAAS;AAChD,gBAAM,SAAS,iBAAiB,MAAM,SAAS;AAC/C,eAAK;AAAA,YAAO,iBAAiB;AAAA,YAAG;AAAA,cAC9B;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,YAAG;AAAA;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,MACA,oBAAwC;AACtC,YAAI,QAAoB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,KAAK;AACjG,eAAO;AAAA,UACL,MAAM,MAAM;AAAA,UACZ,kBAAkB,MAAM;AAAA,UACxB,gBAAgB,MAAM;AAAA,UACtB,QAAQ,MAAM;AAAA,UACd,SAAS,MAAM;AAAA,UACf,WAAW,MAAM;AAAA,QACnB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYA,YAAY,KAAoD;AAC9D,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AACT,YAAI;AAEJ,YAAI,kBAAkB;AACpB,mBAAS,GAAG,OAAO,cAAc,GAAG;AAAA,QACtC,OAAO;AAEL,mBAAS,GAAG,OAAO,YAAY,GAAG;AAGlC,cAAI,gBAAgB;AAClB,kBAAM,QAAQ,GAAG,OAAO,MAAM,IAAI,OAAO,cAAc;AACvD,kBAAM,SAAS,GAAG,OAAO,MAAM,IAAI,QAAQ,cAAc;AAAA,UAC3D;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,eAAe,OAA+B,aAAkD;AAC9F,eAAoB,OAAO,QAAQ,cAAc,gBAAgB,eAAe;AAAA,UAC9E,UAAU,CAAC;AAAA,UACX,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK,MAAM;AAAA,UACnB,QAAQ,6BAA6B,KAAK,MAAM,SAAS,MAAM,KAAK,MAAM,SAAS;AAAA,UACnF,OAAO,KAAK,MAAM;AAAA,UAClB,SAAS,KAAK;AAAA,QAChB,GAAG,KAAK;AAAA,MACV;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,mBAAmB,UAAyB,SAAsC;AAChF,eAAO,CAAC,GAAe;AAAA;AAAA,UAAgD,QAAQ,GAAG,MAAM,QAAQ;AAAA;AAAA,MAClG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,eAAe,OAA+B,UAAyB,aAAkD;AACvH,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AACT,cAAM,iBAAiB,KAAK,kBAAkB;AAG9C,cAAM,YAAY,GAAG,gBAAgB,sBAAsB,gBAAgB,GAAG,GAAG,MAAM,CAAC,EAAE;AAG1F,cAAM,QAAQ,GAAG,gBAAgB,sBAAsB,gBAAgB,GAAG,GAAG,MAAM,IAAI;AACvF,cAAM,SAAS,GAAG,gBAAgB,sBAAsB,gBAAgB,GAAG,GAAG,MAAM,IAAI;AACxF,cAAM,iBAAiB,CAAC,KAAK,OAAO,KAAK,MAAM;AAC/C,cAAM,iBAAiB,CAAC,KAAK,IAAI,MAAM,OAAO,QAAQ,GAAG,KAAK,IAAI,MAAM,QAAQ,QAAQ,CAAC;AACzF,eAAoB,OAAO,QAAQ;AAAA,UAAc,gBAAgB;AAAA,UAE/D;AAAA,YACA,eAAe;AAAA,cACb,UAAU,CAAC;AAAA,YACb;AAAA,YACA,WAAW,cAAc,SAAY;AAAA,YACrC,OAAO,SAAS;AAAA,YAChB,QAAQ,SAAS;AAAA,YACjB;AAAA,YACA;AAAA,YACA,cAAc,KAAK,mBAAmB,UAAU,KAAK,YAAY;AAAA,YACjE,eAAe,KAAK,mBAAmB,UAAU,KAAK,aAAa;AAAA,YACnE,UAAU,KAAK,mBAAmB,UAAU,KAAK,QAAQ;AAAA,YACzD;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,UAAG;AAAA,QAAK;AAAA,MACV;AAAA;AAAA;AAAA;AAAA,MAIA,gBAAgB,GAAe,OAE/B,UAEA,aAAoC;AAClC,YAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAA6B;AAC7B,cAAM,UAAU,KAAK,MAAM,WAAW;AACtC,YAAI,CAAC,QAAS;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AACT,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AAGT,YAAI,cAAc;AAClB,YAAI,MAAM;AACR,yBAAe,GAAG,OAAO,uBAAuB,QAAQ,UAAU,MAAM,cAAc;AACtF,WAAC,GAAG,UAAU,WAAW,MAAM;AAC7B,iBAAK,SAAS;AAAA,cACZ,UAAU,gBAAgB,iBAAiB,OAAO;AAAA,YACpD,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAGA,YAAI;AAAA,UACF;AAAA,UACA;AAAA,QACF,KAAK,GAAG,gBAAgB,QAAQ,KAAK,kBAAkB,GAAG,YAAY,OAAO,YAAY,QAAQ,GAAG,GAAG,MAAM;AAI7G,aAAK,GAAG,gBAAgB,OAAO,GAAG,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI;AACzD,aAAK,GAAG,gBAAgB,OAAO,GAAG,MAAM,IAAI;AAC5C,gBAAQ,KAAK,MAAM,GAAG,GAAG,GAAG;AAAA,UAC1B;AAAA,UACA;AAAA,UACA,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAwB;AACtB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AACT,cAAM,OAAO,GAAG,gBAAgB,sBAAsB,KAAK,kBAAkB,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,KAAK;AACtG,cAAM,QAAQ,OAAO,QAAQ,SAAS,KAAK,KAAK,MAAM,QAAQ;AAG9D,YAAI,WAAwB,OAAO,QAAQ,aAAa,OAAO;AAAA,UAC7D,KAAK,KAAK;AAAA,UACV,YAAY,GAAG,MAAM,SAAS,mBAAmB,MAAM,MAAM,WAAW,KAAK,MAAM,WAAW;AAAA,YAC5F,QAAQ,KAAK,MAAM;AAAA,YACnB,UAAU,QAAQ,KAAK,MAAM,QAAQ;AAAA,YACrC,mBAAmB;AAAA,YACnB,4BAA4B,QAAQ,KAAK,MAAM,QAAQ;AAAA,YACvD,UAAU,QAAQ,gBAAgB;AAAA,YAClC,eAAe;AAAA,UACjB,CAAC;AAAA;AAAA,UAED,OAAO;AAAA,YACL,GAAG,KAAK,MAAM;AAAA,YACd,GAAG,MAAM,MAAM;AAAA,YACf,GAAG,KAAK,YAAY,GAAG;AAAA,UACzB;AAAA,QACF,CAAC;AAGD,mBAAW,KAAK,eAAe,UAAU,KAAK,WAAW;AAGzD,mBAAW,KAAK,eAAe,UAAU,WAAW;AACpD,eAAO;AAAA,MACT;AAAA,IACF;AACA,YAAQ,UAAU;AAClB,oBAAgB,UAAU,aAAa;AAAA;AAAA,MAErC,UAAU,WAAW,QAAQ;AAAA;AAAA,MAE7B,MAAM,WAAW,QAAQ,OAAO;AAAA,MAChC,gBAAgB,WAAW,QAAQ,OAAO;AAAA,MAC1C,WAAW,WAAW,QAAQ,OAAO;AAAA,MACrC,QAAQ,WAAW,QAAQ,MAAM;AAAA,MACjC,SAAS,WAAW,QAAQ,OAAO;AAAA,MACnC,kBAAkB,WAAW,QAAQ,MAAM;AAAA;AAAA,MAE3C,GAAG,WAAW,QAAQ,OAAO;AAAA,MAC7B,GAAG,WAAW,QAAQ,OAAO;AAAA,MAC7B,GAAG,WAAW,QAAQ,OAAO;AAAA,MAC7B,GAAG,WAAW,QAAQ,OAAO;AAAA;AAAA,MAE7B,MAAM,SAAU,OAAmB,UAAuB;AACxD,cAAM,QAAQ,MAAM,QAAQ;AAC5B,YAAI,OAAO,UAAU,SAAU,QAAO,IAAI,MAAM,qBAAqB;AACrE,YAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,KAAM,QAAO,IAAI,MAAM,0CAA0C;AAAA,MACxG;AAAA,MACA,MAAM,SAAU,OAAmB,UAAuB;AACxD,cAAM,QAAQ,MAAM,QAAQ;AAC5B,YAAI,OAAO,UAAU,SAAU,QAAO,IAAI,MAAM,qBAAqB;AACrE,YAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,KAAM,QAAO,IAAI,MAAM,2CAA2C;AAAA,MACzG;AAAA,MACA,MAAM,SAAU,OAAmB,UAAuB;AACxD,cAAM,QAAQ,MAAM,QAAQ;AAC5B,YAAI,OAAO,UAAU,SAAU,QAAO,IAAI,MAAM,sBAAsB;AACtE,YAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,KAAM,QAAO,IAAI,MAAM,6CAA6C;AAAA,MAC3G;AAAA,MACA,MAAM,SAAU,OAAmB,UAAuB;AACxD,cAAM,QAAQ,MAAM,QAAQ;AAC5B,YAAI,OAAO,UAAU,SAAU,QAAO,IAAI,MAAM,sBAAsB;AACtE,YAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,KAAM,QAAO,IAAI,MAAM,8CAA8C;AAAA,MAC5G;AAAA;AAAA,MAEA,GAAG,WAAW,QAAQ,OAAO;AAAA;AAAA,MAE7B,eAAe,0BAA0B;AAAA,MACzC,cAAc,0BAA0B;AAAA;AAAA,MAExC,YAAY,WAAW,QAAQ;AAAA,MAC/B,aAAa,WAAW,QAAQ;AAAA,MAChC,QAAQ,WAAW,QAAQ;AAAA,MAC3B,cAAc,WAAW,QAAQ;AAAA,MACjC,eAAe,WAAW,QAAQ;AAAA,MAClC,UAAU,WAAW,QAAQ;AAAA;AAAA,MAE7B,aAAa,WAAW,QAAQ,KAAK;AAAA,MACrC,aAAa,WAAW,QAAQ,KAAK;AAAA,MACrC,WAAW,WAAW,QAAQ,KAAK;AAAA,MACnC,QAAQ,WAAW,QAAQ;AAAA;AAAA,MAE3B,kBAAkB,WAAW,QAAQ,KAAK;AAAA,MAC1C,gBAAgB,WAAW,QAAQ;AAAA;AAAA,MAEnC,WAAW,WAAW,QAAQ;AAAA;AAAA,MAE9B,QAAQ,WAAW,QAAQ;AAAA;AAAA,MAE3B,QAAQ,WAAW,QAAQ;AAAA;AAAA,MAE3B,kBAAkB,WAAW,QAAQ,MAAM;AAAA,QACzC,GAAG,WAAW,QAAQ,OAAO;AAAA,QAC7B,MAAM,WAAW,QAAQ,OAAO;AAAA,QAChC,KAAK,WAAW,QAAQ,OAAO;AAAA,MACjC,CAAC;AAAA,IACH,CAAC;AACD,oBAAgB,UAAU,gBAAgB;AAAA,MACxC,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,gBAAgB;AAAA,IAClB,CAAC;AAAA;AAAA;;;AC/nBD;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,cAAc;AAClB,QAAI,QAAQ,uBAAuB,cAAe;AAClD,QAAI,SAAS;AACb,QAAI,kBAAkB;AACtB,QAAI,YAAY,uBAAuB,kBAAqB;AAC5D,QAAI,4BAA4B,uBAAuB,kCAAqC;AAC5F,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AACrmB,aAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,cAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,IAAG;AACnL,aAAS,eAAe,GAAG;AAAE,UAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,aAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAA,IAAI;AAC1G,aAAS,aAAa,GAAG,GAAG;AAAE,UAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AAAG,UAAI,IAAI,EAAE,OAAO,WAAW;AAAG,UAAI,WAAW,GAAG;AAAE,YAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,YAAI,YAAY,OAAO,EAAG,QAAO;AAAG,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAA,IAAG;AAiCvT,QAAM,kBAAkB;AACxB,QAAI,YAAY;AAEhB,QAAI;AACF,kBAAY,WAAW,KAAK,UAAU,SAAS;AAAA,IACjD,SAAS,GAAG;AAAA,IAEZ;AAMA,QAAM,kBAAN,cAA8B,MAAM,UAA+B;AAAA,MACjE,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,wBAAgB,MAAM,SAAS;AAAA,UAC7B,YAAY;AAAA,UACZ,SAAS,GAAG,OAAO;AAAA,YAA+B,KAAK,MAAM;AAAA,YAAQ,KAAK,MAAM;AAAA,YAAU,KAAK,MAAM;AAAA;AAAA,aAEpG,GAAG,OAAO,aAAa,KAAK,KAAK;AAAA,YAAG,KAAK,MAAM;AAAA,UAAY;AAAA,UAC5D,SAAS;AAAA,UACT,aAAa;AAAA,UACb,WAAW;AAAA,UACX,eAAe;AAAA,UACf,UAAU;AAAA,UACV,iBAAiB;AAAA,UACjB,UAAU,CAAC;AAAA,QACb,CAAC;AACD,wBAAgB,MAAM,oBAAoB,CAAC;AAS3C,wBAAgB,MAAM,eAAe,CAAC,GAAgB,GAAgB,GAAgB,SAAiB;AACrG,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAwB;AACxB,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,gBAAM,KAAK,GAAG,OAAO,eAAe,QAAQ,CAAC;AAC7C,cAAI,CAAC,EAAG;AAGR,gBAAM,cAAc;AAAA,YAClB,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,aAAa;AAAA,YACb;AAAA,UACF;AACA,eAAK,SAAS;AAAA,YACZ,cAAc,GAAG,OAAO,iBAAiB,CAAC;AAAA,YAC1C,WAAW;AAAA,YACX,YAAY;AAAA,UACd,CAAC;AACD,iBAAO,KAAK,MAAM,YAAY,QAAQ,GAAG,GAAG,MAAM,GAAG,IAAI;AAAA,QAC3D,CAAC;AASD,wBAAgB,MAAM,UAAU,CAAC,GAAG,GAAG,GAAG,UAAU;AAClD,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,cAAI;AAAA,YACF;AAAA,UACF,IAAI,KAAK;AACT,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI,KAAK;AACT,gBAAM,KAAK,GAAG,OAAO,eAAe,QAAQ,CAAC;AAC7C,cAAI,CAAC,EAAG;AAGR,gBAAM,cAAc;AAAA,YAClB,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,aAAa;AAAA,YACb;AAAA,UACF;AAGA,gBAAM,eAAe;AACrB,oBAAU,GAAG,OAAO,aAAa,QAAQ,GAAG,GAAG,GAAG,cAAc,mBAAmB,GAAG,OAAO,aAAa,KAAK,KAAK,GAAG,MAAM,YAAY;AACzI,eAAK,MAAM,OAAO,QAAQ,aAAa,GAAG,aAAa,GAAG,IAAI;AAC9D,eAAK,SAAS;AAAA,YACZ,QAAQ,eAAe,UAAU,GAAG,OAAO,SAAS,SAAS,GAAG,OAAO,aAAa,KAAK,KAAK,GAAG,IAAI;AAAA,YACrG,YAAY;AAAA,UACd,CAAC;AAAA,QACH,CAAC;AASD,wBAAgB,MAAM,cAAc,CAAC,GAAG,GAAG,GAAG,UAAU;AACtD,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,CAAC,KAAK,MAAM,WAAY;AAC5B,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,cAAI;AAAA,YACF;AAAA,UACF,IAAI,KAAK;AACT,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI,KAAK;AACT,gBAAM,KAAK,GAAG,OAAO,eAAe,QAAQ,CAAC;AAC7C,cAAI,CAAC,EAAG;AAGR,gBAAM,eAAe;AACrB,oBAAU,GAAG,OAAO,aAAa,QAAQ,GAAG,GAAG,GAAG,cAAc,mBAAmB,GAAG,OAAO,aAAa,KAAK,KAAK,GAAG,MAAM,YAAY;AAGzI,gBAAM,YAAY,eAAe,UAAU,GAAG,OAAO,SAAS,SAAS,GAAG,OAAO,aAAa,KAAK,KAAK,GAAG,IAAI;AAC/G,eAAK,MAAM,WAAW,WAAW,aAAa,GAAG,MAAM,GAAG,IAAI;AAC9D,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,eAAK,SAAS;AAAA,YACZ,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,WAAW;AAAA,UACb,CAAC;AACD,eAAK,qBAAqB,WAAW,SAAS;AAAA,QAChD,CAAC;AACD,wBAAgB,MAAM,iBAAiB,CAAC,GAAG,GAAG,GAAG,UAAU;AACzD,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,gBAAM,KAAK,GAAG,OAAO,eAAe,QAAQ,CAAC;AAC7C,cAAI,CAAC,EAAG;AACR,eAAK,SAAS;AAAA,YACZ,gBAAgB,GAAG,OAAO,iBAAiB,CAAC;AAAA,YAC5C,WAAW,KAAK,MAAM;AAAA,YACtB,UAAU;AAAA,UACZ,CAAC;AACD,eAAK,MAAM,cAAc,QAAQ,GAAG,GAAG,MAAM,GAAG,IAAI;AAAA,QACtD,CAAC;AACD,wBAAgB,MAAM,YAAY,CAAC,GAAG,GAAG,GAAG,UAAU;AACpD,cAAI;AAAA,YACF;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI,KAAK;AACT,cAAI,iBAAiB;AACrB,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,gBAAM,CAAC,WAAW,CAAC,KAAK,GAAG,OAAO,gBAAgB,QAAQ,GAAG,CAAAC,OAAK;AAChE,gBAAI;AACJ,gBAAIA,GAAE;AACN,gBAAIA,GAAE;AACN,gBAAI,CAAC,MAAM,KAAK,MAAM,KAAK,IAAI,EAAE,QAAQ,MAAM,MAAM,IAAI;AACvD,kBAAI,CAAC,MAAM,MAAM,GAAG,EAAE,QAAQ,MAAM,MAAM,IAAI;AAC5C,oBAAIA,GAAE,KAAKA,GAAE,IAAI;AACjB,oBAAIA,GAAE,MAAM,KAAK,IAAI,IAAIA,GAAE,IAAI;AAC/B,oBAAI,IAAI,IAAI,IAAI;AAAA,cAClB;AACA,kBAAI,CAAC,MAAM,KAAK,IAAI,EAAE,QAAQ,MAAM,MAAM,IAAI;AAC5C,oBAAIA,GAAE,KAAKA,GAAE,IAAI;AACjB,oBAAIA,GAAE,MAAM,KAAK,IAAI,IAAIA,GAAE,IAAI;AAC/B,oBAAI,IAAI,IAAI,IAAI;AAAA,cAClB;AACA,+BAAiB;AAAA,YACnB;AAIA,gBAAI,oBAAoB,CAAC,cAAc;AACrC,oBAAM,cAAc,GAAG,OAAO,kBAAkB,QAAQ;AAAA,gBACtD,GAAGA;AAAA,gBACH;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,CAAC,EAAE,OAAO,gBAAc,WAAW,MAAMA,GAAE,CAAC;AAC5C,8BAAgB,WAAW,SAAS;AAGpC,kBAAI,eAAe;AAEjB,oBAAIA,GAAE;AACN,oBAAIA,GAAE;AACN,oBAAIA,GAAE;AACN,oBAAIA,GAAE;AACN,iCAAiB;AAAA,cACnB;AAAA,YACF;AACA,YAAAA,GAAE,IAAI;AACN,YAAAA,GAAE,IAAI;AACN,mBAAOA;AAAA,UACT,CAAC;AAGD,cAAI,CAAC,EAAG;AACR,wBAAc;AACd,cAAI,gBAAgB;AAElB,kBAAM,eAAe;AACrB,2BAAe,GAAG,OAAO,aAAa,WAAW,GAAG,GAAG,GAAG,cAAc,KAAK,MAAM,mBAAmB,GAAG,OAAO,aAAa,KAAK,KAAK,GAAG,MAAM,YAAY;AAAA,UAC9J;AAGA,gBAAM,cAAc;AAAA,YAClB,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,QAAQ;AAAA,YACR;AAAA,UACF;AACA,eAAK,MAAM,SAAS,aAAa,eAAe,GAAG,aAAa,GAAG,IAAI;AAGvE,eAAK,SAAS;AAAA,YACZ,QAAQ,eAAe,eAAe,GAAG,OAAO,SAAS,cAAc,GAAG,OAAO,aAAa,KAAK,KAAK,GAAG,IAAI;AAAA,YAC/G,YAAY;AAAA,UACd,CAAC;AAAA,QACH,CAAC;AACD,wBAAgB,MAAM,gBAAgB,CAAC,GAAG,GAAG,GAAG,UAAU;AACxD,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,KAAK;AACT,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,KAAK;AACT,gBAAM,KAAK,GAAG,OAAO,eAAe,QAAQ,CAAC;AAG7C,gBAAM,YAAY,eAAe,UAAU,GAAG,OAAO,SAAS,SAAS,GAAG,OAAO,aAAa,KAAK,KAAK,GAAG,IAAI;AAC/G,eAAK,MAAM,aAAa,WAAW,eAAe,GAAG,MAAM,GAAG,IAAI;AAClE,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,eAAK,SAAS;AAAA,YACZ,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,eAAe;AAAA,YACf,WAAW;AAAA,YACX,UAAU;AAAA,UACZ,CAAC;AACD,eAAK,qBAAqB,WAAW,SAAS;AAAA,QAChD,CAAC;AAGD,wBAAgB,MAAM,cAAc,OAAK;AACvC,YAAE,eAAe;AACjB,YAAE,gBAAgB;AAKlB,cAAI;AAAA,UAEJ,CAAC,EAAE,YAAY,QAAQ,UAAU,SAAS,eAAe,GAAG;AAC1D,mBAAO;AAAA,UACT;AACA,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI,KAAK;AAGT,gBAAM,mBAAmB,iBAAiB,CAAC;AAC3C,cAAI,qBAAqB,OAAO;AAC9B,gBAAI,KAAK,MAAM,iBAAiB;AAC9B,mBAAK,0BAA0B;AAAA,YACjC;AACA,mBAAO;AAAA,UACT;AACA,gBAAM,oBAAoB;AAAA,YACxB,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AACA,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AAGT,gBAAM,WAAW,EAAE,cAAc,sBAAsB;AAGvD,gBAAM,SAAS,EAAE,UAAU,SAAS;AACpC,gBAAM,SAAS,EAAE,UAAU,SAAS;AACpC,gBAAM,mBAAmB;AAAA,YACvB,MAAM,SAAS;AAAA,YACf,KAAK,SAAS;AAAA,YACd;AAAA,UACF;AACA,cAAI,CAAC,KAAK,MAAM,iBAAiB;AAC/B,kBAAM,iBAAsC;AAAA,cAC1C;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,gBAAgB;AAAA,cAChB,kBAAkB,oBAAoB;AAAA,YACxC;AACA,kBAAM,sBAAsB,GAAG,gBAAgB,QAAQ,gBAAgB,QAAQ,QAAQ,kBAAkB,GAAG,kBAAkB,CAAC;AAC/H,iBAAK,SAAS;AAAA,cACZ,iBAA8B,MAAM,cAAc,OAAO;AAAA,gBACvD,KAAK,kBAAkB;AAAA,cACzB,CAAC;AAAA,cACD;AAAA,cACA,QAAQ,CAAC,GAAG,QAAQ;AAAA,gBAClB,GAAG;AAAA,gBACH,GAAG,mBAAmB;AAAA,gBACtB,GAAG,mBAAmB;AAAA,gBACtB,QAAQ;AAAA,gBACR,aAAa;AAAA,cACf,CAAC;AAAA,YACH,CAAC;AAAA,UACH,WAAW,KAAK,MAAM,kBAAkB;AACtC,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI,KAAK,MAAM;AACf,kBAAM,uBAAuB,QAAQ,UAAU,OAAO;AACtD,gBAAI,sBAAsB;AACxB,mBAAK,SAAS;AAAA,gBACZ;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,6BAA6B,MAAM;AACvD,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,KAAK;AACT,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,gBAAM,aAAa,GAAG,OAAO,SAAS,OAAO,OAAO,OAAK,EAAE,MAAM,aAAa,CAAC,IAAI,GAAG,OAAO,aAAa,KAAK,KAAK,GAAG,MAAM,KAAK,MAAM,YAAY;AACpJ,eAAK,SAAS;AAAA,YACZ,QAAQ;AAAA,YACR,iBAAiB;AAAA,YACjB,YAAY;AAAA,YACZ,kBAAkB;AAAA,UACpB,CAAC;AAAA,QACH,CAAC;AACD,wBAAgB,MAAM,eAAe,OAAK;AACxC,YAAE,eAAe;AACjB,YAAE,gBAAgB;AAClB,eAAK;AAOL,cAAI,KAAK,qBAAqB,GAAG;AAC/B,iBAAK,0BAA0B;AAAA,UACjC;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,eAAe,OAAK;AACxC,YAAE,eAAe;AACjB,YAAE,gBAAgB;AAClB,eAAK;AAAA,QACP,CAAC;AACD,wBAAgB,MAAM,UAAU,CAAC,MAAkB;AACjD,YAAE,eAAe;AACjB,YAAE,gBAAgB;AAClB,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,gBAAM,OAAO,OAAO,KAAK,OAAK,EAAE,MAAM,aAAa,CAAC;AAGpD,eAAK,mBAAmB;AACxB,eAAK,0BAA0B;AAC/B,eAAK,MAAM,OAAO,QAAQ,MAAM,CAAC;AAAA,QACnC,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,SAAS;AAAA,UACZ,SAAS;AAAA,QACX,CAAC;AAGD,aAAK,qBAAqB,KAAK,MAAM,QAAQ,KAAK,MAAM,MAAM;AAAA,MAChE;AAAA,MACA,OAAO,yBAAyB,WAAuB,WAAiD;AACtG,YAAI;AACJ,YAAI,UAAU,YAAY;AACxB,iBAAO;AAAA,QACT;AAIA,YAAI,EAAE,GAAG,YAAY,WAAW,UAAU,QAAQ,UAAU,WAAW,KAAK,UAAU,gBAAgB,UAAU,aAAa;AAC3H,0BAAgB,UAAU;AAAA,QAC5B,WAAW,EAAE,GAAG,OAAO,eAAe,UAAU,UAAU,UAAU,QAAQ,GAAG;AAI7E,0BAAgB,UAAU;AAAA,QAC5B;AAGA,YAAI,eAAe;AACjB,gBAAM,aAAa,GAAG,OAAO,+BAA+B,eAAe,UAAU,UAAU,UAAU,OAAO,GAAG,OAAO,aAAa,SAAS,GAAG,UAAU,YAAY;AACzK,iBAAO;AAAA,YACL,QAAQ;AAAA;AAAA;AAAA,YAGR,aAAa,UAAU;AAAA,YACvB,UAAU,UAAU;AAAA,YACpB,aAAa,UAAU;AAAA,UACzB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,sBAAsB,WAAuB,WAAoC;AAC/E;AAAA;AAAA;AAAA;AAAA,UAIE,KAAK,MAAM,aAAa,UAAU,YAAY,EAAE,GAAG,OAAO,mBAAmB,KAAK,OAAO,WAAW,YAAY,SAAS,KAAK,KAAK,MAAM,eAAe,UAAU,cAAc,KAAK,MAAM,YAAY,UAAU,WAAW,KAAK,MAAM,qBAAqB,UAAU;AAAA;AAAA,MAE1Q;AAAA,MACA,mBAAmB,WAAuB,WAAuB;AAC/D,YAAI,CAAC,KAAK,MAAM,YAAY;AAC1B,gBAAM,YAAY,KAAK,MAAM;AAC7B,gBAAM,YAAY,UAAU;AAC5B,eAAK,qBAAqB,WAAW,SAAS;AAAA,QAChD;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,kBAA+B;AAC7B,YAAI,CAAC,KAAK,MAAM,SAAU;AAC1B,cAAM,SAAS,GAAG,OAAO,QAAQ,KAAK,MAAM,MAAM;AAClD,cAAM,oBAAoB,KAAK,MAAM,mBAAmB,KAAK,MAAM,iBAAiB,CAAC,IAAI,KAAK,MAAM,OAAO,CAAC;AAC5G,eAAO,QAAQ,KAAK,MAAM,aAAa,QAAQ,KAAK,KAAK,MAAM,OAAO,CAAC,IAAI,oBAAoB,IAAI;AAAA,MACrG;AAAA,MACA,qBAAqB,WAAwB,WAAyB;AACpE,YAAI,CAAC,UAAW,aAAY,KAAK,MAAM;AACvC,YAAI,EAAE,GAAG,YAAY,WAAW,WAAW,SAAS,GAAG;AACrD,eAAK,MAAM,eAAe,SAAS;AAAA,QACrC;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,cAAsC;AACpC,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,KAAK;AACT,YAAI,CAAC,WAAY,QAAO;AACxB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AAGT,eAAoB,MAAM,cAAc,UAAU,SAAS;AAAA,UACzD,GAAG,WAAW;AAAA,UACd,GAAG,WAAW;AAAA,UACd,GAAG,WAAW;AAAA,UACd,GAAG,WAAW;AAAA,UACd,GAAG,WAAW;AAAA,UACd,WAAW,0BAA0B,KAAK,MAAM,WAAW,yBAAyB,EAAE;AAAA,UACtF,gBAAgB;AAAA,UAChB;AAAA,UACA;AAAA,UACA,kBAAkB,oBAAoB;AAAA,UACtC;AAAA,UACA;AAAA,UACA,aAAa;AAAA,UACb,aAAa;AAAA,UACb,WAAW;AAAA,UACX;AAAA,UACA;AAAA,QACF,GAAgB,MAAM,cAAc,OAAO,IAAI,CAAC;AAAA,MAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,gBAAgB,OAA+B,gBAAsD;AACnG,YAAI,CAAC,SAAS,CAAC,MAAM,IAAK;AAC1B,cAAM,KAAK,GAAG,OAAO,eAAe,KAAK,MAAM,QAAQ,OAAO,MAAM,GAAG,CAAC;AACxE,YAAI,CAAC,EAAG,QAAO;AACf,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AACT,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AAKT,cAAM,YAAY,OAAO,EAAE,gBAAgB,YAAY,EAAE,cAAc,CAAC,EAAE,UAAU;AACpF,cAAM,YAAY,OAAO,EAAE,gBAAgB,YAAY,EAAE,cAAc,CAAC,EAAE,UAAU;AACpF,cAAM,uBAAuB,EAAE,iBAAiB;AAGhD,cAAM,UAAU,aAAa,aAAa,EAAE,cAAc;AAC1D,eAAoB,MAAM,cAAc,UAAU,SAAS;AAAA,UACzD,gBAAgB;AAAA,UAChB;AAAA,UACA;AAAA,UACA,kBAAkB,oBAAoB;AAAA,UACtC;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,YAAY,KAAK;AAAA,UACjB,aAAa,KAAK;AAAA,UAClB,QAAQ,KAAK;AAAA,UACb,eAAe,KAAK;AAAA,UACpB,UAAU,KAAK;AAAA,UACf,cAAc,KAAK;AAAA,UACnB,aAAa;AAAA,UACb,aAAa;AAAA,UACb,WAAW;AAAA,UACX,kBAAkB,oBAAoB;AAAA,UACtC,gBAAgB,CAAC;AAAA,UACjB;AAAA,UACA,GAAG,EAAE;AAAA,UACL,GAAG,EAAE;AAAA,UACL,GAAG,EAAE;AAAA,UACL,GAAG,EAAE;AAAA,UACL,GAAG,EAAE;AAAA,UACL,MAAM,EAAE;AAAA,UACR,MAAM,EAAE;AAAA,UACR,MAAM,EAAE;AAAA,UACR,MAAM,EAAE;AAAA,UACR,QAAQ,EAAE;AAAA,UACV,kBAAkB,iBAAiB,mBAAmB;AAAA,UACtD,eAAe;AAAA,UACf;AAAA,QACF,GAAG,KAAK;AAAA,MACV;AAAA,MACA,SAAmC;AACjC,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AACT,cAAM,mBAAmB,GAAG,MAAM,SAAS,iBAAiB,SAAS;AACrE,cAAM,cAAc;AAAA,UAClB,QAAQ,KAAK,gBAAgB;AAAA,UAC7B,GAAG;AAAA,QACL;AACA,eAAoB,MAAM,cAAc,OAAO;AAAA,UAC7C,KAAK;AAAA,UACL,WAAW;AAAA,UACX,OAAO;AAAA,UACP,QAAQ,cAAc,KAAK,SAAS,OAAO;AAAA,UAC3C,aAAa,cAAc,KAAK,cAAc,OAAO;AAAA,UACrD,aAAa,cAAc,KAAK,cAAc,OAAO;AAAA,UACrD,YAAY,cAAc,KAAK,aAAa,OAAO;AAAA,QACrD,GAAG,MAAM,SAAS,IAAI,KAAK,MAAM,UAAU,WAAS,KAAK,gBAAgB,KAAK,CAAC,GAAG,eAAe,KAAK,MAAM,mBAAmB,KAAK,gBAAgB,KAAK,MAAM,iBAAiB,IAAI,GAAG,KAAK,YAAY,CAAC;AAAA,MAC3M;AAAA,IACF;AACA,YAAQ,UAAU;AAElB,oBAAgB,iBAAiB,eAAe,iBAAiB;AAEjE,oBAAgB,iBAAiB,aAAa,0BAA0B,OAAO;AAC/E,oBAAgB,iBAAiB,gBAAgB;AAAA,MAC/C,UAAU;AAAA,MACV,MAAM;AAAA,MACN,WAAW;AAAA,MACX,OAAO,CAAC;AAAA,MACR,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,SAAS;AAAA;AAAA,MAET,QAAQ,CAAC;AAAA,MACT,QAAQ,CAAC,IAAI,EAAE;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,cAAc;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA,eAAe,CAAC,IAAI;AAAA,MACpB,gBAAgB,OAAO;AAAA,MACvB,aAAa,OAAO;AAAA,MACpB,QAAQ,OAAO;AAAA,MACf,YAAY,OAAO;AAAA,MACnB,eAAe,OAAO;AAAA,MACtB,UAAU,OAAO;AAAA,MACjB,cAAc,OAAO;AAAA,MACrB,QAAQ,OAAO;AAAA,MACf,gBAAgB,OAAO;AAAA,IACzB,CAAC;AAAA;AAAA;;;ACvuBD;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,iCAAiC;AACzC,YAAQ,yBAAyB;AACjC,YAAQ,wBAAwB;AAChC,YAAQ,kBAAkB;AAC1B,QAAI,SAAS;AAsBb,aAAS,uBAAuB,aAA2C,OAAoC;AAC7G,YAAM,SAAS,gBAAgB,WAAW;AAC1C,UAAI,WAAW,OAAO,CAAC;AACvB,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,cAAM,iBAAiB,OAAO,CAAC;AAC/B,YAAI,QAAQ,YAAY,cAAc,EAAG,YAAW;AAAA,MACtD;AACA,aAAO;AAAA,IACT;AAQA,aAAS,sBAAsB,YAA6B,MAAgD;AAC1G,UAAI,CAAC,KAAK,UAAU,GAAG;AACrB,cAAM,IAAI,MAAM,4DAA4D,aAAa,cAAc;AAAA,MACzG;AACA,aAAO,KAAK,UAAU;AAAA,IACxB;AAgBA,aAAS,+BAA+B,SAA4C,aAA2C,YAA6B,gBAAiC,MAAmB,aAA2C;AAEzP,UAAI,QAAQ,UAAU,EAAG,SAAQ,GAAG,OAAO,aAAa,QAAQ,UAAU,CAAC;AAE3E,UAAI,SAAS,QAAQ,cAAc;AACnC,YAAM,oBAAoB,gBAAgB,WAAW;AACrD,YAAM,mBAAmB,kBAAkB,MAAM,kBAAkB,QAAQ,UAAU,CAAC;AACtF,eAAS,IAAI,GAAG,MAAM,iBAAiB,QAAQ,IAAI,KAAK,KAAK;AAC3D,cAAM,IAAI,iBAAiB,CAAC;AAC5B,YAAI,QAAQ,CAAC,GAAG;AACd,mBAAS,QAAQ,CAAC;AAClB;AAAA,QACF;AAAA,MACF;AACA,gBAAU,GAAG,OAAO,aAAa,UAAU,CAAC,CAAC;AAC7C,cAAQ,GAAG,OAAO,UAAU,GAAG,OAAO,eAAe,QAAQ;AAAA,QAC3D;AAAA,MACF,CAAC,GAAG,aAAa,IAAI;AAAA,IACvB;AASA,aAAS,gBAAgB,aAAkE;AACzF,YAAM,OAA2B,OAAO,KAAK,WAAW;AACxD,aAAO,KAAK,KAAK,SAAU,GAAG,GAAG;AAC/B,eAAO,YAAY,CAAC,IAAI,YAAY,CAAC;AAAA,MACvC,CAAC;AAAA,IACH;AAAA;AAAA;;;ACpGA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,cAAc;AAClB,QAAI,SAAS;AACb,QAAI,mBAAmB;AACvB,QAAI,mBAAmB,uBAAuB,yBAA4B;AAC1E,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AACrmB,aAAS,WAAW;AAAE,aAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AAAE,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,cAAI,IAAI,UAAU,CAAC;AAAG,mBAAS,KAAK,EAAG,EAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAAI;AAAE,eAAO;AAAA,MAAG,GAAG,SAAS,MAAM,MAAM,SAAS;AAAA,IAAG;AACnR,aAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,cAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,IAAG;AACnL,aAAS,eAAe,GAAG;AAAE,UAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,aAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAA,IAAI;AAC1G,aAAS,aAAa,GAAG,GAAG;AAAE,UAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AAAG,UAAI,IAAI,EAAE,OAAO,WAAW;AAAG,UAAI,WAAW,GAAG;AAAE,YAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,YAAI,YAAY,OAAO,EAAG,QAAO;AAAG,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAA,IAAG;AAEvT,QAAM,OAAO,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG;AAStD,aAAS,oBAAkD,OAAsC,YAAgC;AAE/H,UAAI,SAAS,KAAM,QAAO;AAE1B,aAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,MAAM,UAAU;AAAA,IACxD;AA4CA,QAAM,4BAAN,cAAwC,MAAM,UAK9C;AAAA,MACE,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,wBAAgB,MAAM,SAAS,KAAK,qBAAqB,CAAC;AAE1D,wBAAgB,MAAM,kBAAkB,CAAC,WAAwB;AAC/D,eAAK,MAAM,eAAe,QAAQ;AAAA,YAChC,GAAG,KAAK,MAAM;AAAA,YACd,CAAC,KAAK,MAAM,UAAU,GAAG;AAAA,UAC3B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,MACA,uBAAkC;AAChC,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AACT,cAAM,cAAc,GAAG,iBAAiB,wBAAwB,aAAa,KAAK;AAClF,cAAM,SAAS,GAAG,iBAAiB,uBAAuB,YAAY,IAAI;AAE1E,cAAM,cAAc,KAAK,MAAM,oBAAoB,QAAQ,OAAO,KAAK,MAAM;AAG7E,cAAM,iBAAiB,GAAG,iBAAiB,gCAAgC,SAAS,aAAa,YAAY,YAAY,OAAO,WAAW;AAC3I,eAAO;AAAA,UACL,QAAQ;AAAA,UACR;AAAA,UACA,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA,OAAO,yBAAyB,WAA0B,WAA2C;AACnG,YAAI,EAAE,GAAG,YAAY,WAAW,UAAU,SAAS,UAAU,OAAO,GAAG;AAErE,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AAIJ,gBAAM,aAAa,GAAG,iBAAiB,gCAAgC,UAAU,SAAS,UAAU,aAAa,YAAY,YAAY,MAAM,UAAU,WAAW;AACpK,iBAAO;AAAA,YACL,QAAQ;AAAA,YACR,SAAS,UAAU;AAAA,UACrB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,mBAAmB,WAA0B;AAE3C,YAAI,KAAK,MAAM,SAAS,UAAU,SAAS,KAAK,MAAM,eAAe,UAAU,cAAc,EAAE,GAAG,YAAY,WAAW,KAAK,MAAM,aAAa,UAAU,WAAW,KAAK,EAAE,GAAG,YAAY,WAAW,KAAK,MAAM,MAAM,UAAU,IAAI,GAAG;AACvO,eAAK,cAAc,SAAS;AAAA,QAC9B;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,cAAc,WAA0B;AACtC,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AACT,cAAM,gBAAgB,KAAK,MAAM,eAAe,GAAG,iBAAiB,wBAAwB,KAAK,MAAM,aAAa,KAAK,MAAM,KAAK;AACpI,cAAM,iBAAiB,KAAK,MAAM;AAClC,cAAM,WAAwB,GAAG,iBAAiB,uBAAuB,eAAe,IAAI;AAC5F,cAAM,aAAa;AAAA,UACjB,GAAG;AAAA,QACL;AAGA,YAAI,mBAAmB,iBAAiB,UAAU,gBAAgB,eAAe,UAAU,SAAS,MAAM;AAExG,cAAI,EAAE,kBAAkB,YAAa,YAAW,cAAc,KAAK,GAAG,OAAO,aAAa,KAAK,MAAM,MAAM;AAG3G,cAAI,UAAU,GAAG,iBAAiB,gCAAgC,YAAY,aAAa,eAAe,gBAAgB,SAAS,WAAW;AAG9I,oBAAU,GAAG,OAAO,+BAA+B,QAAQ,KAAK,MAAM,UAAU,SAAS,aAAa,KAAK,MAAM,YAAY;AAG7H,qBAAW,aAAa,IAAI;AAG5B,eAAK,MAAM,mBAAmB,eAAe,OAAO;AACpD,eAAK,MAAM,eAAe,QAAQ,UAAU;AAC5C,eAAK,SAAS;AAAA,YACZ,YAAY;AAAA,YACZ;AAAA,YACA,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AACA,cAAM,SAAS,oBAAoB,KAAK,MAAM,QAAQ,aAAa;AACnE,cAAM,mBAAmB,oBAAoB,KAAK,MAAM,kBAAkB,aAAa;AAGvF,aAAK,MAAM,cAAc,KAAK,MAAM,OAAO,QAAQ,SAAS,gBAAgB;AAAA,MAC9E;AAAA,MACA,SAAoD;AAElD,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACL,IAAI,KAAK;AAGT,eAAoB,MAAM,cAAc,iBAAiB,SAAS,SAAS,CAAC,GAAG,OAAO;AAAA;AAAA,UAEpF,QAAQ,oBAAoB,QAAQ,KAAK,MAAM,UAAU;AAAA,UACzD,kBAAkB,oBAAoB,kBAAkB,KAAK,MAAM,UAAU;AAAA,UAC7E,gBAAgB,KAAK;AAAA,UACrB,QAAQ,KAAK,MAAM;AAAA,UACnB,MAAM,KAAK,MAAM;AAAA,QACnB,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,YAAQ,UAAU;AAGlB,oBAAgB,2BAA2B,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOtD,YAAY,WAAW,QAAQ;AAAA;AAAA,MAE/B,aAAa,WAAW,QAAQ;AAAA,MAChC,cAAc,WAAW,QAAQ;AAAA;AAAA,MAEjC,MAAM,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAKzB,QAAQ,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,OAAO,WAAW,QAAQ,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAK1F,kBAAkB,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,OAAO,WAAW,QAAQ,MAAM,CAAC;AAAA;AAAA;AAAA,MAGpG,QAAQ,OAAqB,UAAuB;AAClD,YAAI,KAAK,MAAM,QAAQ,CAAC,MAAM,mBAAmB;AAC/C,gBAAM,IAAI,MAAM,kDAAkD,KAAK,MAAM,QAAQ,CAAC,CAAC;AAAA,QACzF;AACA,eAAO,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,SAAO;AAC1C,cAAI,EAAE,OAAO,MAAM,cAAc;AAC/B,kBAAM,IAAI,MAAM,2DAA2D;AAAA,UAC7E;AACA,WAAC,GAAG,OAAO,gBAAgB,MAAM,QAAQ,GAAG,GAAG,aAAa,GAAG;AAAA,QACjE,CAAC;AAAA,MACH;AAAA;AAAA;AAAA,MAGA,OAAO,WAAW,QAAQ,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,MAMjC,oBAAoB,WAAW,QAAQ;AAAA;AAAA;AAAA,MAGvC,gBAAgB,WAAW,QAAQ;AAAA;AAAA,MAEnC,eAAe,WAAW,QAAQ;AAAA,IACpC,CAAC;AACD,oBAAgB,2BAA2B,gBAAgB;AAAA,MACzD,aAAa;AAAA,QACX,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,KAAK;AAAA,MACP;AAAA,MACA,MAAM;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,KAAK;AAAA,MACP;AAAA,MACA,kBAAkB;AAAA,QAChB,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,KAAK;AAAA,MACP;AAAA,MACA,SAAS,CAAC;AAAA,MACV,QAAQ,CAAC,IAAI,EAAE;AAAA,MACf,cAAc;AAAA,MACd,oBAAoB,OAAO;AAAA,MAC3B,gBAAgB,OAAO;AAAA,MACvB,eAAe,OAAO;AAAA,IACxB,CAAC;AAAA;AAAA;;;ACrSD;AAAA;AAAA;AAAA;AAwJA,SAAS,SAAU,UAAU,OAAO;AAChC,MAAI,cAAc,OAAO,eAAe,OAAO,eAAe;AAO9D,WAAS,iBAAiB;AACtB,QAAI,aAAa;AACb,oBAAc;AACd,eAAS;AAAA,IACb;AACA,QAAI,cAAc;AACd,YAAM;AAAA,IACV;AAAA,EACJ;AAQA,WAAS,kBAAkB;AACvB,4BAAwB,cAAc;AAAA,EAC1C;AAMA,WAAS,QAAQ;AACb,QAAI,YAAY,KAAK,IAAI;AACzB,QAAI,aAAa;AAEb,UAAI,YAAY,eAAe,iBAAiB;AAC5C;AAAA,MACJ;AAKA,qBAAe;AAAA,IACnB,OACK;AACD,oBAAc;AACd,qBAAe;AACf,iBAAW,iBAAiB,KAAK;AAAA,IACrC;AACA,mBAAe;AAAA,EACnB;AACA,SAAO;AACX;AA2PA,SAAS,QAAQ,OAAO;AACpB,SAAO,WAAW,KAAK,KAAK;AAChC;AAQA,SAAS,eAAe,QAAQ;AAC5B,MAAI,YAAY,CAAC;AACjB,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,cAAU,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EACpC;AACA,SAAO,UAAU,OAAO,SAAU,MAAM,UAAU;AAC9C,QAAI,QAAQ,OAAO,YAAY,WAAW,QAAQ;AAClD,WAAO,OAAO,QAAQ,KAAK;AAAA,EAC/B,GAAG,CAAC;AACR;AAOA,SAAS,YAAY,QAAQ;AACzB,MAAI,YAAY,CAAC,OAAO,SAAS,UAAU,MAAM;AACjD,MAAI,WAAW,CAAC;AAChB,WAAS,KAAK,GAAG,cAAc,WAAW,KAAK,YAAY,QAAQ,MAAM;AACrE,QAAI,WAAW,YAAY,EAAE;AAC7B,QAAI,QAAQ,OAAO,aAAa,QAAQ;AACxC,aAAS,QAAQ,IAAI,QAAQ,KAAK;AAAA,EACtC;AACA,SAAO;AACX;AAQA,SAAS,kBAAkB,QAAQ;AAC/B,MAAI,OAAO,OAAO,QAAQ;AAC1B,SAAO,eAAe,GAAG,GAAG,KAAK,OAAO,KAAK,MAAM;AACvD;AAOA,SAAS,0BAA0B,QAAQ;AAGvC,MAAI,cAAc,OAAO,aAAa,eAAe,OAAO;AAS5D,MAAI,CAAC,eAAe,CAAC,cAAc;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,SAAS,YAAY,MAAM,EAAE,iBAAiB,MAAM;AACxD,MAAI,WAAW,YAAY,MAAM;AACjC,MAAI,WAAW,SAAS,OAAO,SAAS;AACxC,MAAI,UAAU,SAAS,MAAM,SAAS;AAKtC,MAAI,QAAQ,QAAQ,OAAO,KAAK,GAAG,SAAS,QAAQ,OAAO,MAAM;AAGjE,MAAI,OAAO,cAAc,cAAc;AAOnC,QAAI,KAAK,MAAM,QAAQ,QAAQ,MAAM,aAAa;AAC9C,eAAS,eAAe,QAAQ,QAAQ,OAAO,IAAI;AAAA,IACvD;AACA,QAAI,KAAK,MAAM,SAAS,OAAO,MAAM,cAAc;AAC/C,gBAAU,eAAe,QAAQ,OAAO,QAAQ,IAAI;AAAA,IACxD;AAAA,EACJ;AAKA,MAAI,CAAC,kBAAkB,MAAM,GAAG;AAK5B,QAAI,gBAAgB,KAAK,MAAM,QAAQ,QAAQ,IAAI;AACnD,QAAI,iBAAiB,KAAK,MAAM,SAAS,OAAO,IAAI;AAMpD,QAAI,KAAK,IAAI,aAAa,MAAM,GAAG;AAC/B,eAAS;AAAA,IACb;AACA,QAAI,KAAK,IAAI,cAAc,MAAM,GAAG;AAChC,gBAAU;AAAA,IACd;AAAA,EACJ;AACA,SAAO,eAAe,SAAS,MAAM,SAAS,KAAK,OAAO,MAAM;AACpE;AAyBA,SAAS,kBAAkB,QAAQ;AAC/B,SAAO,WAAW,YAAY,MAAM,EAAE,SAAS;AACnD;AAOA,SAAS,eAAe,QAAQ;AAC5B,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,MAAI,qBAAqB,MAAM,GAAG;AAC9B,WAAO,kBAAkB,MAAM;AAAA,EACnC;AACA,SAAO,0BAA0B,MAAM;AAC3C;AAQA,SAAS,mBAAmB,IAAI;AAC5B,MAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,QAAQ,GAAG,OAAO,SAAS,GAAG;AAEtD,MAAI,SAAS,OAAO,oBAAoB,cAAc,kBAAkB;AACxE,MAAI,OAAO,OAAO,OAAO,OAAO,SAAS;AAEzC,qBAAmB,MAAM;AAAA,IACrB;AAAA,IAAM;AAAA,IAAM;AAAA,IAAc;AAAA,IAC1B,KAAK;AAAA,IACL,OAAO,IAAI;AAAA,IACX,QAAQ,SAAS;AAAA,IACjB,MAAM;AAAA,EACV,CAAC;AACD,SAAO;AACX;AAWA,SAAS,eAAe,GAAG,GAAG,OAAO,QAAQ;AACzC,SAAO,EAAE,GAAM,GAAM,OAAc,OAAe;AACtD;AA1oBA,IAOI,SAsGA,WAGA,UAoBA,yBAWA,iBAiEA,eAGA,gBAEA,2BAIA,0BA0MA,oBAmBA,aAWA,WAmIA,sBA4EA,mBAsDA,qBAoBA,mBAwJA,WAKA,gBAgCA,OAQG;AA/5BP;AAAA;AAOA,IAAI,UAAW,WAAY;AACvB,UAAI,OAAO,QAAQ,aAAa;AAC5B,eAAO;AAAA,MACX;AAQA,eAAS,SAAS,KAAK,KAAK;AACxB,YAAI,SAAS;AACb,YAAI,KAAK,SAAU,OAAOC,QAAO;AAC7B,cAAI,MAAM,CAAC,MAAM,KAAK;AAClB,qBAASA;AACT,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX,CAAC;AACD,eAAO;AAAA,MACX;AACA;AAAA;AAAA,QAAsB,WAAY;AAC9B,mBAAS,UAAU;AACf,iBAAK,cAAc,CAAC;AAAA,UACxB;AACA,iBAAO,eAAe,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,YAI7C,KAAK,WAAY;AACb,qBAAO,KAAK,YAAY;AAAA,YAC5B;AAAA,YACA,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AAKD,kBAAQ,UAAU,MAAM,SAAU,KAAK;AACnC,gBAAIA,SAAQ,SAAS,KAAK,aAAa,GAAG;AAC1C,gBAAI,QAAQ,KAAK,YAAYA,MAAK;AAClC,mBAAO,SAAS,MAAM,CAAC;AAAA,UAC3B;AAMA,kBAAQ,UAAU,MAAM,SAAU,KAAK,OAAO;AAC1C,gBAAIA,SAAQ,SAAS,KAAK,aAAa,GAAG;AAC1C,gBAAI,CAACA,QAAO;AACR,mBAAK,YAAYA,MAAK,EAAE,CAAC,IAAI;AAAA,YACjC,OACK;AACD,mBAAK,YAAY,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,YACtC;AAAA,UACJ;AAKA,kBAAQ,UAAU,SAAS,SAAU,KAAK;AACtC,gBAAI,UAAU,KAAK;AACnB,gBAAIA,SAAQ,SAAS,SAAS,GAAG;AACjC,gBAAI,CAACA,QAAO;AACR,sBAAQ,OAAOA,QAAO,CAAC;AAAA,YAC3B;AAAA,UACJ;AAKA,kBAAQ,UAAU,MAAM,SAAU,KAAK;AACnC,mBAAO,CAAC,CAAC,CAAC,SAAS,KAAK,aAAa,GAAG;AAAA,UAC5C;AAIA,kBAAQ,UAAU,QAAQ,WAAY;AAClC,iBAAK,YAAY,OAAO,CAAC;AAAA,UAC7B;AAMA,kBAAQ,UAAU,UAAU,SAAU,UAAU,KAAK;AACjD,gBAAI,QAAQ,QAAQ;AAAE,oBAAM;AAAA,YAAM;AAClC,qBAAS,KAAK,GAAG,KAAK,KAAK,aAAa,KAAK,GAAG,QAAQ,MAAM;AAC1D,kBAAI,QAAQ,GAAG,EAAE;AACjB,uBAAS,KAAK,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,YACzC;AAAA,UACJ;AACA,iBAAO;AAAA,QACX,EAAE;AAAA;AAAA,IACN,EAAG;AAKH,IAAI,YAAY,OAAO,WAAW,eAAe,OAAO,aAAa,eAAe,OAAO,aAAa;AAGxG,IAAI,WAAY,WAAY;AACxB,UAAI,OAAO,WAAW,eAAe,OAAO,SAAS,MAAM;AACvD,eAAO;AAAA,MACX;AACA,UAAI,OAAO,SAAS,eAAe,KAAK,SAAS,MAAM;AACnD,eAAO;AAAA,MACX;AACA,UAAI,OAAO,WAAW,eAAe,OAAO,SAAS,MAAM;AACvD,eAAO;AAAA,MACX;AAEA,aAAO,SAAS,aAAa,EAAE;AAAA,IACnC,EAAG;AAQH,IAAI,0BAA2B,WAAY;AACvC,UAAI,OAAO,0BAA0B,YAAY;AAI7C,eAAO,sBAAsB,KAAK,QAAQ;AAAA,MAC9C;AACA,aAAO,SAAU,UAAU;AAAE,eAAO,WAAW,WAAY;AAAE,iBAAO,SAAS,KAAK,IAAI,CAAC;AAAA,QAAG,GAAG,MAAO,EAAE;AAAA,MAAG;AAAA,IAC7G,EAAG;AAGH,IAAI,kBAAkB;AAiEtB,IAAI,gBAAgB;AAGpB,IAAI,iBAAiB,CAAC,OAAO,SAAS,UAAU,QAAQ,SAAS,UAAU,QAAQ,QAAQ;AAE3F,IAAI,4BAA4B,OAAO,qBAAqB;AAI5D,IAAI;AAAA,IAA0C,WAAY;AAMtD,eAASC,4BAA2B;AAMhC,aAAK,aAAa;AAMlB,aAAK,uBAAuB;AAM5B,aAAK,qBAAqB;AAM1B,aAAK,aAAa,CAAC;AACnB,aAAK,mBAAmB,KAAK,iBAAiB,KAAK,IAAI;AACvD,aAAK,UAAU,SAAS,KAAK,QAAQ,KAAK,IAAI,GAAG,aAAa;AAAA,MAClE;AAOA,MAAAA,0BAAyB,UAAU,cAAc,SAAU,UAAU;AACjE,YAAI,CAAC,CAAC,KAAK,WAAW,QAAQ,QAAQ,GAAG;AACrC,eAAK,WAAW,KAAK,QAAQ;AAAA,QACjC;AAEA,YAAI,CAAC,KAAK,YAAY;AAClB,eAAK,SAAS;AAAA,QAClB;AAAA,MACJ;AAOA,MAAAA,0BAAyB,UAAU,iBAAiB,SAAU,UAAU;AACpE,YAAIC,aAAY,KAAK;AACrB,YAAIF,SAAQE,WAAU,QAAQ,QAAQ;AAEtC,YAAI,CAACF,QAAO;AACR,UAAAE,WAAU,OAAOF,QAAO,CAAC;AAAA,QAC7B;AAEA,YAAI,CAACE,WAAU,UAAU,KAAK,YAAY;AACtC,eAAK,YAAY;AAAA,QACrB;AAAA,MACJ;AAOA,MAAAD,0BAAyB,UAAU,UAAU,WAAY;AACrD,YAAI,kBAAkB,KAAK,iBAAiB;AAG5C,YAAI,iBAAiB;AACjB,eAAK,QAAQ;AAAA,QACjB;AAAA,MACJ;AASA,MAAAA,0BAAyB,UAAU,mBAAmB,WAAY;AAE9D,YAAI,kBAAkB,KAAK,WAAW,OAAO,SAAU,UAAU;AAC7D,iBAAO,SAAS,aAAa,GAAG,SAAS,UAAU;AAAA,QACvD,CAAC;AAMD,wBAAgB,QAAQ,SAAU,UAAU;AAAE,iBAAO,SAAS,gBAAgB;AAAA,QAAG,CAAC;AAClF,eAAO,gBAAgB,SAAS;AAAA,MACpC;AAOA,MAAAA,0BAAyB,UAAU,WAAW,WAAY;AAGtD,YAAI,CAAC,aAAa,KAAK,YAAY;AAC/B;AAAA,QACJ;AAIA,iBAAS,iBAAiB,iBAAiB,KAAK,gBAAgB;AAChE,eAAO,iBAAiB,UAAU,KAAK,OAAO;AAC9C,YAAI,2BAA2B;AAC3B,eAAK,qBAAqB,IAAI,iBAAiB,KAAK,OAAO;AAC3D,eAAK,mBAAmB,QAAQ,UAAU;AAAA,YACtC,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,eAAe;AAAA,YACf,SAAS;AAAA,UACb,CAAC;AAAA,QACL,OACK;AACD,mBAAS,iBAAiB,sBAAsB,KAAK,OAAO;AAC5D,eAAK,uBAAuB;AAAA,QAChC;AACA,aAAK,aAAa;AAAA,MACtB;AAOA,MAAAA,0BAAyB,UAAU,cAAc,WAAY;AAGzD,YAAI,CAAC,aAAa,CAAC,KAAK,YAAY;AAChC;AAAA,QACJ;AACA,iBAAS,oBAAoB,iBAAiB,KAAK,gBAAgB;AACnE,eAAO,oBAAoB,UAAU,KAAK,OAAO;AACjD,YAAI,KAAK,oBAAoB;AACzB,eAAK,mBAAmB,WAAW;AAAA,QACvC;AACA,YAAI,KAAK,sBAAsB;AAC3B,mBAAS,oBAAoB,sBAAsB,KAAK,OAAO;AAAA,QACnE;AACA,aAAK,qBAAqB;AAC1B,aAAK,uBAAuB;AAC5B,aAAK,aAAa;AAAA,MACtB;AAQA,MAAAA,0BAAyB,UAAU,mBAAmB,SAAU,IAAI;AAChE,YAAI,KAAK,GAAG,cAAc,eAAe,OAAO,SAAS,KAAK;AAE9D,YAAI,mBAAmB,eAAe,KAAK,SAAU,KAAK;AACtD,iBAAO,CAAC,CAAC,CAAC,aAAa,QAAQ,GAAG;AAAA,QACtC,CAAC;AACD,YAAI,kBAAkB;AAClB,eAAK,QAAQ;AAAA,QACjB;AAAA,MACJ;AAMA,MAAAA,0BAAyB,cAAc,WAAY;AAC/C,YAAI,CAAC,KAAK,WAAW;AACjB,eAAK,YAAY,IAAIA,0BAAyB;AAAA,QAClD;AACA,eAAO,KAAK;AAAA,MAChB;AAMA,MAAAA,0BAAyB,YAAY;AACrC,aAAOA;AAAA,IACX,EAAE;AASF,IAAI,qBAAsB,SAAU,QAAQ,OAAO;AAC/C,eAAS,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC5D,YAAI,MAAM,GAAG,EAAE;AACf,eAAO,eAAe,QAAQ,KAAK;AAAA,UAC/B,OAAO,MAAM,GAAG;AAAA,UAChB,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,cAAc;AAAA,QAClB,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX;AAQA,IAAI,cAAe,SAAU,QAAQ;AAIjC,UAAI,cAAc,UAAU,OAAO,iBAAiB,OAAO,cAAc;AAGzE,aAAO,eAAe;AAAA,IAC1B;AAGA,IAAI,YAAY,eAAe,GAAG,GAAG,GAAG,CAAC;AAmIzC,IAAI,uBAAwB,WAAY;AAGpC,UAAI,OAAO,uBAAuB,aAAa;AAC3C,eAAO,SAAU,QAAQ;AAAE,iBAAO,kBAAkB,YAAY,MAAM,EAAE;AAAA,QAAoB;AAAA,MAChG;AAIA,aAAO,SAAU,QAAQ;AAAE,eAAQ,kBAAkB,YAAY,MAAM,EAAE,cACrE,OAAO,OAAO,YAAY;AAAA,MAAa;AAAA,IAC/C,EAAG;AAiEH,IAAI;AAAA,IAAmC,WAAY;AAM/C,eAASE,mBAAkB,QAAQ;AAM/B,aAAK,iBAAiB;AAMtB,aAAK,kBAAkB;AAMvB,aAAK,eAAe,eAAe,GAAG,GAAG,GAAG,CAAC;AAC7C,aAAK,SAAS;AAAA,MAClB;AAOA,MAAAA,mBAAkB,UAAU,WAAW,WAAY;AAC/C,YAAI,OAAO,eAAe,KAAK,MAAM;AACrC,aAAK,eAAe;AACpB,eAAQ,KAAK,UAAU,KAAK,kBACxB,KAAK,WAAW,KAAK;AAAA,MAC7B;AAOA,MAAAA,mBAAkB,UAAU,gBAAgB,WAAY;AACpD,YAAI,OAAO,KAAK;AAChB,aAAK,iBAAiB,KAAK;AAC3B,aAAK,kBAAkB,KAAK;AAC5B,eAAO;AAAA,MACX;AACA,aAAOA;AAAA,IACX,EAAE;AAEF,IAAI;AAAA,IAAqC,2BAAY;AAOjD,eAASC,qBAAoB,QAAQ,UAAU;AAC3C,YAAI,cAAc,mBAAmB,QAAQ;AAO7C,2BAAmB,MAAM,EAAE,QAAgB,YAAyB,CAAC;AAAA,MACzE;AACA,aAAOA;AAAA,IACX,EAAE;AAEF,IAAI;AAAA,IAAmC,WAAY;AAW/C,eAASC,mBAAkB,UAAU,YAAY,aAAa;AAO1D,aAAK,sBAAsB,CAAC;AAM5B,aAAK,gBAAgB,IAAI,QAAQ;AACjC,YAAI,OAAO,aAAa,YAAY;AAChC,gBAAM,IAAI,UAAU,yDAAyD;AAAA,QACjF;AACA,aAAK,YAAY;AACjB,aAAK,cAAc;AACnB,aAAK,eAAe;AAAA,MACxB;AAOA,MAAAA,mBAAkB,UAAU,UAAU,SAAU,QAAQ;AACpD,YAAI,CAAC,UAAU,QAAQ;AACnB,gBAAM,IAAI,UAAU,0CAA0C;AAAA,QAClE;AAEA,YAAI,OAAO,YAAY,eAAe,EAAE,mBAAmB,SAAS;AAChE;AAAA,QACJ;AACA,YAAI,EAAE,kBAAkB,YAAY,MAAM,EAAE,UAAU;AAClD,gBAAM,IAAI,UAAU,uCAAuC;AAAA,QAC/D;AACA,YAAI,eAAe,KAAK;AAExB,YAAI,aAAa,IAAI,MAAM,GAAG;AAC1B;AAAA,QACJ;AACA,qBAAa,IAAI,QAAQ,IAAI,kBAAkB,MAAM,CAAC;AACtD,aAAK,YAAY,YAAY,IAAI;AAEjC,aAAK,YAAY,QAAQ;AAAA,MAC7B;AAOA,MAAAA,mBAAkB,UAAU,YAAY,SAAU,QAAQ;AACtD,YAAI,CAAC,UAAU,QAAQ;AACnB,gBAAM,IAAI,UAAU,0CAA0C;AAAA,QAClE;AAEA,YAAI,OAAO,YAAY,eAAe,EAAE,mBAAmB,SAAS;AAChE;AAAA,QACJ;AACA,YAAI,EAAE,kBAAkB,YAAY,MAAM,EAAE,UAAU;AAClD,gBAAM,IAAI,UAAU,uCAAuC;AAAA,QAC/D;AACA,YAAI,eAAe,KAAK;AAExB,YAAI,CAAC,aAAa,IAAI,MAAM,GAAG;AAC3B;AAAA,QACJ;AACA,qBAAa,OAAO,MAAM;AAC1B,YAAI,CAAC,aAAa,MAAM;AACpB,eAAK,YAAY,eAAe,IAAI;AAAA,QACxC;AAAA,MACJ;AAMA,MAAAA,mBAAkB,UAAU,aAAa,WAAY;AACjD,aAAK,YAAY;AACjB,aAAK,cAAc,MAAM;AACzB,aAAK,YAAY,eAAe,IAAI;AAAA,MACxC;AAOA,MAAAA,mBAAkB,UAAU,eAAe,WAAY;AACnD,YAAI,QAAQ;AACZ,aAAK,YAAY;AACjB,aAAK,cAAc,QAAQ,SAAU,aAAa;AAC9C,cAAI,YAAY,SAAS,GAAG;AACxB,kBAAM,oBAAoB,KAAK,WAAW;AAAA,UAC9C;AAAA,QACJ,CAAC;AAAA,MACL;AAOA,MAAAA,mBAAkB,UAAU,kBAAkB,WAAY;AAEtD,YAAI,CAAC,KAAK,UAAU,GAAG;AACnB;AAAA,QACJ;AACA,YAAI,MAAM,KAAK;AAEf,YAAI,UAAU,KAAK,oBAAoB,IAAI,SAAU,aAAa;AAC9D,iBAAO,IAAI,oBAAoB,YAAY,QAAQ,YAAY,cAAc,CAAC;AAAA,QAClF,CAAC;AACD,aAAK,UAAU,KAAK,KAAK,SAAS,GAAG;AACrC,aAAK,YAAY;AAAA,MACrB;AAMA,MAAAA,mBAAkB,UAAU,cAAc,WAAY;AAClD,aAAK,oBAAoB,OAAO,CAAC;AAAA,MACrC;AAMA,MAAAA,mBAAkB,UAAU,YAAY,WAAY;AAChD,eAAO,KAAK,oBAAoB,SAAS;AAAA,MAC7C;AACA,aAAOA;AAAA,IACX,EAAE;AAKF,IAAI,YAAY,OAAO,YAAY,cAAc,oBAAI,QAAQ,IAAI,IAAI,QAAQ;AAK7E,IAAI;AAAA,IAAgC,2BAAY;AAO5C,eAASC,gBAAe,UAAU;AAC9B,YAAI,EAAE,gBAAgBA,kBAAiB;AACnC,gBAAM,IAAI,UAAU,oCAAoC;AAAA,QAC5D;AACA,YAAI,CAAC,UAAU,QAAQ;AACnB,gBAAM,IAAI,UAAU,0CAA0C;AAAA,QAClE;AACA,YAAI,aAAa,yBAAyB,YAAY;AACtD,YAAI,WAAW,IAAI,kBAAkB,UAAU,YAAY,IAAI;AAC/D,kBAAU,IAAI,MAAM,QAAQ;AAAA,MAChC;AACA,aAAOA;AAAA,IACX,EAAE;AAEF;AAAA,MACI;AAAA,MACA;AAAA,MACA;AAAA,IACJ,EAAE,QAAQ,SAAU,QAAQ;AACxB,qBAAe,UAAU,MAAM,IAAI,WAAY;AAC3C,YAAI;AACJ,gBAAQ,KAAK,UAAU,IAAI,IAAI,GAAG,MAAM,EAAE,MAAM,IAAI,SAAS;AAAA,MACjE;AAAA,IACJ,CAAC;AAED,IAAI,QAAS,WAAY;AAErB,UAAI,OAAO,SAAS,mBAAmB,aAAa;AAChD,eAAO,SAAS;AAAA,MACpB;AACA,aAAO;AAAA,IACX,EAAG;AAEH,IAAO,4BAAQ;AAAA;AAAA;;;AC/5Bf;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,0BAA0B,uBAAuB,mEAAmC;AACxF,QAAI,QAAQ,uBAAuB,cAAe;AAClD,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AACrmB,aAAS,WAAW;AAAE,aAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AAAE,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,cAAI,IAAI,UAAU,CAAC;AAAG,mBAAS,KAAK,EAAG,EAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAAI;AAAE,eAAO;AAAA,MAAG,GAAG,SAAS,MAAM,MAAM,SAAS;AAAA,IAAG;AACnR,aAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,cAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,IAAG;AACnL,aAAS,eAAe,GAAG;AAAE,UAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,aAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAA,IAAI;AAC1G,aAAS,aAAa,GAAG,GAAG;AAAE,UAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AAAG,UAAI,IAAI,EAAE,OAAO,WAAW;AAAG,UAAI,WAAW,GAAG;AAAE,YAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,YAAI,YAAY,OAAO,EAAG,QAAO;AAAG,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAA,IAAG;AAqBvT,QAAM,kBAAkB;AAQxB,aAAS,gBAAgC,mBAA6G;AACpJ,UAAI;AACJ,aAAO,iBAAiB,MAAM,sBAAsB,MAAM,UAK1D;AAAA,QACE,cAAc;AACZ,gBAAM,GAAG,SAAS;AAClB,0BAAgB,MAAM,SAAS;AAAA,YAC7B,OAAO;AAAA,UACT,CAAC;AACD,0BAAgB,MAAM,cAA2B,MAAM,UAAU,CAAC;AAClE,0BAAgB,MAAM,WAAW,KAAK;AACtC,0BAAgB,MAAM,kBAAkB,MAAM;AAAA,QAChD;AAAA,QACA,oBAAoB;AAClB,eAAK,UAAU;AACf,eAAK,iBAAiB,IAAI,wBAAwB,QAAQ,aAAW;AACnE,kBAAMC,QAAO,KAAK,WAAW;AAC7B,gBAAIA,iBAAgB,aAAa;AAC/B,oBAAM,QAAQ,QAAQ,CAAC,EAAE,YAAY;AACrC,mBAAK,SAAS;AAAA,gBACZ;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AACD,gBAAM,OAAO,KAAK,WAAW;AAC7B,cAAI,gBAAgB,aAAa;AAC/B,iBAAK,eAAe,QAAQ,IAAI;AAAA,UAClC;AAAA,QACF;AAAA,QACA,uBAAuB;AACrB,eAAK,UAAU;AACf,gBAAM,OAAO,KAAK,WAAW;AAC7B,cAAI,gBAAgB,aAAa;AAC/B,iBAAK,eAAe,UAAU,IAAI;AAAA,UACpC;AACA,eAAK,eAAe,WAAW;AAAA,QACjC;AAAA,QACA,SAAS;AACP,gBAAM;AAAA,YACJ;AAAA,YACA,GAAG;AAAA,UACL,IAAI,KAAK;AACT,cAAI,sBAAsB,CAAC,KAAK,SAAS;AACvC,mBAAoB,MAAM,cAAc,OAAO;AAAA,cAC7C,YAAY,GAAG,MAAM,SAAS,KAAK,MAAM,WAAW,eAAe;AAAA,cACnE,OAAO,KAAK,MAAM;AAAA,cAGlB,KAAK,KAAK;AAAA,YACZ,CAAC;AAAA,UACH;AACA,iBAAoB,MAAM,cAAc,mBAAmB,SAAS;AAAA,YAClE,UAAU,KAAK;AAAA,UACjB,GAAG,MAAM,KAAK,KAAK,CAAC;AAAA,QACtB;AAAA,MACF,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,QACjD,oBAAoB;AAAA,MACtB,CAAC,GAAG,gBAAgB,gBAAgB,aAAa;AAAA;AAAA;AAAA,QAG/C,oBAAoB,WAAW,QAAQ;AAAA,MACzC,CAAC,GAAG;AAAA,IACN;AAAA;AAAA;;;AC9GA;AAAA;AAAA,WAAO,UAAU,0BAAmC;AACpD,WAAO,QAAQ,QAAQ;AACvB,WAAO,QAAQ,iBAAiB;AAChC,WAAO,QAAQ,aACb,oCAA6C;AAC/C,WAAO,QAAQ,WAAW,QAAQ;AAClC,WAAO,QAAQ,gBACb,wBAA4C;AAAA;AAAA;", "names": ["areArraysEqual", "areDatesEqual", "areMapsEqual", "areObjectsEqual", "areRegExpsEqual", "areSetsEqual", "index", "e", "compactType", "collides", "Element", "i", "checker", "e", "t", "e", "t", "e", "t", "require_utils", "_getRequireWildcardCache", "nodeInterop", "_setPrototypeOf", "o", "p", "Resizable", "_getRequireWildcardCache", "nodeInterop", "_setPrototypeOf", "o", "p", "ResizableBox", "e", "t", "l", "e", "t", "index", "ResizeObserverController", "observers", "ResizeObservation", "ResizeObserverEntry", "ResizeObserverSPI", "ResizeObserver", "e", "t", "node"]}