import { useState } from 'react';
import {
  AppShell,
  Burger,
  Group,
  Text,
  NavLink,
  ScrollArea,
  Divider,
  ThemeIcon,
  useMantineColorScheme,
  ActionIcon,
  TextInput,
  Avatar,
  Menu,
  Button
} from '@mantine/core';
import {
  IconGauge,
  IconServer,
  IconChartBar,
  IconAlertCircle,
  IconDeviceDesktopAnalytics,
  IconCloudComputing,
  IconSettings,
  IconLogout,
  IconMenu2,
  IconSearch,
  IconSun,
  IconMoon,
  IconUser
} from '@tabler/icons-react';
import { useAppDispatch, useAppSelector } from '../store';
import { logout } from '../store/slices/authSlice';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

interface NavbarLinkProps {
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  onClick?: () => void;
}

function NavbarLink({ icon, label, active, onClick }: NavbarLinkProps) {
  return (
    <NavLink
      href="#"
      label={label}
      leftSection={icon}
      active={active}
      onClick={(event) => {
        event.preventDefault();
        onClick?.();
      }}
      style={{ borderRadius: '8px', marginBottom: '4px' }}
    />
  );
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { colorScheme, toggleColorScheme } = useMantineColorScheme();
  const [mobileOpened, setMobileOpened] = useState(false);
  const [desktopOpened, setDesktopOpened] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const dispatch = useAppDispatch();
  const user = useAppSelector(state => state.auth.user);

  // Toggle functions
  const toggleMobileSidebar = () => setMobileOpened((prev) => !prev);
  const toggleDesktopSidebar = () => setDesktopOpened((prev) => !prev);

  const navItems = [
    { icon: <IconGauge size={18} />, label: 'Dashboard', active: true },
    { icon: <IconServer size={18} />, label: 'Servers' },
    { icon: <IconChartBar size={18} />, label: 'Metrics' },
    { icon: <IconAlertCircle size={18} />, label: 'Alerts' },
    { icon: <IconDeviceDesktopAnalytics size={18} />, label: 'Analytics' },
    { icon: <IconCloudComputing size={18} />, label: 'AWS Resources' },
  ];

  return (
    <AppShell
      header={{ height: 60 }}
      navbar={{
        width: 260,
        breakpoint: 'sm',
        collapsed: { desktop: !desktopOpened, mobile: !mobileOpened }
      }}
      padding="md"
      styles={(theme) => ({
        main: {
          backgroundColor: colorScheme === 'dark' ? theme.colors.dark[8] : theme.colors.gray[0],
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          padding: theme.spacing.md,
          transition: 'margin-left 200ms ease'
        },
        navbar: {
          backgroundColor: colorScheme === 'dark' ? 'rgba(15, 23, 42, 0.95)' : theme.colors.gray[0],
          backdropFilter: 'blur(12px)',
          borderRight: colorScheme === 'dark'
            ? '1px solid rgba(100, 116, 139, 0.3)'
            : `1px solid ${theme.colors.gray[3]}`,
          transition: 'all 200ms ease'
        },
        root: {
          width: '100vw',
          height: '100vh'
        }
      })}
    >
      <AppShell.Header p="xs">
        <Group justify="space-between" style={{ height: '100%' }}>
          <Group>
            {/* Mobile burger menu */}
            <Burger
              opened={mobileOpened}
              onClick={toggleMobileSidebar}
              size="sm"
              hiddenFrom="sm"
            />

            {/* Desktop sidebar toggle */}
            <ActionIcon
              variant="subtle"
              onClick={toggleDesktopSidebar}
              size="lg"
              visibleFrom="sm"
              title={desktopOpened ? "Collapse Sidebar" : "Expand Sidebar"}
            >
              <IconMenu2 size={20} />
            </ActionIcon>

            <Text fw={700} size="lg">AWS Elastic DRS</Text>
          </Group>

          <Group>
            <TextInput
              placeholder="Search..."
              leftSection={<IconSearch size={16} />}
              value={searchValue}
              onChange={(event) => setSearchValue(event.currentTarget.value)}
              style={{ width: 200 }}
            />
            
            <ActionIcon
              variant="subtle"
              onClick={() => toggleColorScheme()}
              size="lg"
            >
              {colorScheme === 'dark' ? <IconSun size={20} /> : <IconMoon size={20} />}
            </ActionIcon>

            <Menu shadow="md" width={200} position="bottom-end">
              <Menu.Target>
                <Button variant="subtle" p={0}>
                  <Group gap="xs">
                    <Avatar color="blue" radius="xl" size="sm">
                      {user?.username?.[0]?.toUpperCase() || 'U'}
                    </Avatar>
                    <Text size="sm" fw={500}>{user?.username || 'User'}</Text>
                  </Group>
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item leftSection={<IconUser size={16} />}>
                  Profile
                </Menu.Item>
                <Menu.Item leftSection={<IconSettings size={16} />}>
                  Settings
                </Menu.Item>
                <Menu.Divider />
                <Menu.Item 
                  leftSection={<IconLogout size={16} />}
                  onClick={() => dispatch(logout())}
                  color="red"
                >
                  Logout
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Group>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar p="xs">
        <AppShell.Section>
          <Group justify="space-between" mb="md">
            <Group>
              <ThemeIcon size="lg" variant="gradient" gradient={{ from: 'indigo', to: 'cyan' }}>
                <IconCloudComputing size={20} />
              </ThemeIcon>
              <Text fw={700} size="lg">AWS DRS</Text>
            </Group>
          </Group>
        </AppShell.Section>

        <Divider my="sm" />

        <AppShell.Section grow component={ScrollArea}>
          <div>
            {navItems.map((item, index) => (
              <NavbarLink
                key={index}
                icon={item.icon}
                label={item.label}
                active={item.active}
              />
            ))}
          </div>
        </AppShell.Section>

        <AppShell.Section>
          <Divider my="sm" />
          <NavbarLink icon={<IconSettings size={18} />} label="Settings" />
          <NavbarLink 
            icon={<IconLogout size={18} />} 
            label="Logout" 
            onClick={() => dispatch(logout())}
          />
        </AppShell.Section>
      </AppShell.Navbar>

      <AppShell.Main>
        {children}
      </AppShell.Main>
    </AppShell>
  );
}
