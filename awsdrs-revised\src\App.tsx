import { MantineProvider, createTheme } from '@mantine/core';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Provider as ReduxProvider } from 'react-redux';
import { store } from './store';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Dashboard from './pages/Dashboard';
import '@mantine/core/styles.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

// Create a query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Create a custom theme for AWS DRS - Vision UI inspired
const theme = createTheme({
  primaryColor: 'cyan',
  colors: {
    // Vision UI Blue-Cyan color palette
    cyan: [
      '#ecfeff', // cyan-50
      '#cffafe', // cyan-100
      '#a5f3fc', // cyan-200
      '#67e8f9', // cyan-300
      '#22d3ee', // cyan-400
      '#06b6d4', // cyan-500 - Primary
      '#0891b2', // cyan-600
      '#0e7490', // cyan-700
      '#155e75', // cyan-800
      '#164e63'  // cyan-900
    ],
    dark: [
      '#e2e8f0', // slate-200 - text
      '#cbd5e1', // slate-300
      '#94a3b8', // slate-400
      '#64748b', // slate-500
      '#475569', // slate-600 - border
      '#334155', // slate-700
      '#1e293b', // slate-800 - header
      '#0f172a', // slate-900 - background
      '#020617', // slate-950
      '#000000'  // black
    ]
  },
  components: {
    Card: {
      defaultProps: {
        shadow: 'sm',
        withBorder: true,
        radius: 'md',
      },
      styles: (theme) => ({
        root: {
          backgroundColor: theme.colorScheme === 'dark'
            ? 'rgba(30, 41, 59, 0.5)'
            : theme.colors.gray[0],
          backdropFilter: theme.colorScheme === 'dark' ? 'blur(12px)' : 'none',
          border: theme.colorScheme === 'dark'
            ? '1px solid rgba(100, 116, 139, 0.3)'
            : `1px solid ${theme.colors.gray[3]}`,
          boxShadow: theme.colorScheme === 'dark'
            ? '0 0 20px rgba(6, 182, 212, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            : theme.shadows.sm,
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: theme.colorScheme === 'dark'
              ? '0 0 30px rgba(6, 182, 212, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.15)'
              : theme.shadows.md,
            transform: 'translateY(-1px)'
          }
        }
      })
    },
    Button: {
      defaultProps: {
        radius: 'md',
      },
      styles: (theme) => ({
        root: {
          background: theme.colorScheme === 'dark'
            ? 'linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%)'
            : undefined,
          boxShadow: theme.colorScheme === 'dark'
            ? '0 4px 15px rgba(6, 182, 212, 0.3)'
            : undefined,
          '&:hover': {
            background: theme.colorScheme === 'dark'
              ? 'linear-gradient(135deg, #0891b2 0%, #2563eb 100%)'
              : undefined,
            boxShadow: theme.colorScheme === 'dark'
              ? '0 6px 20px rgba(6, 182, 212, 0.4)'
              : undefined,
            transform: 'translateY(-1px)'
          }
        }
      })
    },
    ActionIcon: {
      styles: (theme) => ({
        root: {
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'scale(1.05)'
          }
        }
      })
    }
  },
});

function App() {
  return (
    <ReduxProvider store={store}>
      <QueryClientProvider client={queryClient}>
        <MantineProvider theme={theme} defaultColorScheme="auto">
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </BrowserRouter>
        </MantineProvider>
      </QueryClientProvider>
    </ReduxProvider>
  );
}

export default App;
