{"name": "agent-dashboard", "version": "0.2.0", "private": true, "type": "module", "dependencies": {"@heroicons/react": "^2.2.0", "@mantine/core": "^8.1.3", "@mantine/hooks": "^8.1.3", "@mantine/notifications": "^8.1.3", "@tabler/icons-react": "^3.34.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "^20.17.10", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/react-grid-layout": "^1.3.5", "lucide-react": "^0.468.0", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-grid-layout": "^1.5.2", "react-window": "^1.8.11", "typescript": "~5.8.3", "web-vitals": "^4.2.4"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.5.2", "@vitest/ui": "^2.1.8", "autoprefixer": "^10.4.20", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "jsdom": "^25.0.1", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "vitest": "^2.1.8"}}