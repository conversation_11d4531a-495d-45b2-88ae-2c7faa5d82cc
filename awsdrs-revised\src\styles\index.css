/* Vision UI Dark Theme - AWS DRS Dashboard */
:root {
  --mantine-color-body: var(--mantine-color-white);
  --mantine-color-text: var(--mantine-color-black);

  /* Vision UI Color Variables */
  --vision-gradient: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%);
  --vision-glass: rgba(30, 41, 59, 0.5);
  --vision-glow: 0 0 20px rgba(6, 182, 212, 0.3);
  --vision-border: rgba(100, 116, 139, 0.3);
  --vision-cyan: #06b6d4;
  --vision-blue: #3b82f6;

  /* Transition variables */
  --transition-duration: 300ms;
  --transition-timing: ease-in-out;
}

[data-mantine-color-scheme="dark"] {
  --mantine-color-body: #0f172a;
  --mantine-color-text: #e2e8f0;

  /* Apply Vision UI gradient background */
  background: var(--vision-gradient) !important;
}

html {
  background-color: var(--mantine-color-body) !important;
  transition: background-color var(--transition-duration) var(--transition-timing);
}

body {
  background-color: var(--mantine-color-body) !important;
  color: var(--mantine-color-text) !important;
  margin: 0;
  padding: 0;
  transition:
    background-color var(--transition-duration) var(--transition-timing),
    color var(--transition-duration) var(--transition-timing);
}

/* Ensure root div takes full height with Vision UI styling */
#root {
  min-height: 100vh;
  background-color: var(--mantine-color-body);
  transition: background-color var(--transition-duration) var(--transition-timing);
}

/* Vision UI Dark Mode Enhancements */
[data-mantine-color-scheme="dark"] {
  /* Enhanced glassy morphism effects */
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Vision UI Card Enhancements */
[data-mantine-color-scheme="dark"] .mantine-Card-root {
  background: var(--vision-glass) !important;
  border: 1px solid var(--vision-border) !important;
  backdrop-filter: blur(12px);
  box-shadow: var(--vision-glow), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all var(--transition-duration) var(--transition-timing);
}

[data-mantine-color-scheme="dark"] .mantine-Card-root:hover {
  box-shadow:
    0 0 30px rgba(6, 182, 212, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  transform: translateY(-1px);
}

/* Vision UI Button Enhancements */
[data-mantine-color-scheme="dark"] .mantine-Button-root {
  background: linear-gradient(135deg, var(--vision-cyan) 0%, var(--vision-blue) 100%) !important;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3) !important;
  border: none !important;
  transition: all var(--transition-duration) var(--transition-timing);
}

[data-mantine-color-scheme="dark"] .mantine-Button-root:hover {
  background: linear-gradient(135deg, #0891b2 0%, #2563eb 100%) !important;
  box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4) !important;
  transform: translateY(-1px);
}

/* Vision UI ActionIcon Enhancements */
[data-mantine-color-scheme="dark"] .mantine-ActionIcon-root {
  transition: all 200ms ease-in-out;
}

[data-mantine-color-scheme="dark"] .mantine-ActionIcon-root:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.2);
}

/* React Grid Layout styles */
.react-grid-layout {
  position: relative;
}

.react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top;
}

.react-grid-item.cssTransforms {
  transition-property: transform;
}

.react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI2IiB2aWV3Qm94PSIwIDAgNiA2IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxnIGZpbGw9IiM0NDRBNTU4IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Im0wIDZoNnYtNmgtNnoiLz48L2c+PC9zdmc+');
  background-position: bottom right;
  padding: 0 3px 3px 0;
  background-repeat: no-repeat;
  background-origin: content-box;
  box-sizing: border-box;
  cursor: se-resize;
}

.react-grid-item.react-grid-placeholder {
  background: red;
  opacity: 0.2;
  transition-duration: 100ms;
  z-index: 2;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.react-grid-item > .react-resizable-handle::after {
  content: "";
  position: absolute;
  right: 3px;
  bottom: 3px;
  width: 5px;
  height: 5px;
  border-right: 2px solid rgba(0, 0, 0, 0.4);
  border-bottom: 2px solid rgba(0, 0, 0, 0.4);
}
