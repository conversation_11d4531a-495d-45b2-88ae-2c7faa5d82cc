import { useEffect, useState, useCallback } from 'react';
import { Group, Button, ActionIcon, Text, Title, Card, Stack } from '@mantine/core';
import {
  IconRefresh,
  IconLock,
  IconLockOpen,
  IconSettings,
  IconMenu2,
  IconRestore,
  IconEye,
  IconEyeOff
} from '@tabler/icons-react';
import { useAppDispatch, useAppSelector } from '../store';
import { fetchServers } from '../store/slices/serverSlice';
import DashboardLayout from '../layouts/DashboardLayout';
import { DashboardWidget } from '../types/dashboard';
import { Responsive, WidthProvider } from 'react-grid-layout';
import type { Layout } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Import widget components
import ServerMetricsWidget from '../components/widgets/ServerMetricsWidget';
import ServerStatusWidget from '../components/widgets/ServerStatusWidget';
import LagTrendWidget from '../components/widgets/LagTrendWidget';
import RegionDistributionWidget from '../components/widgets/RegionDistributionWidget';
import ServerTableWidget from '../components/widgets/ServerTableWidget';
import AlertsWidget from '../components/widgets/AlertsWidget';

// Default widgets for AWS DRS dashboard
const defaultWidgets: DashboardWidget[] = [
  { id: 'server-metrics', title: 'Server Metrics Overview', type: 'server-metrics' },
  { id: 'server-status', title: 'Server Status', type: 'server-status' },
  { id: 'lag-trend', title: 'Replication Lag Trend', type: 'lag-trend' },
  { id: 'region-distribution', title: 'Region Distribution', type: 'region-distribution' },
  { id: 'server-table', title: 'Server Details', type: 'server-table' },
  { id: 'alerts', title: 'Alerts & Issues', type: 'alerts' }
];

// Default layout configuration
const defaultLayouts = {
  lg: [
    { i: 'server-metrics', x: 0, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'server-status', x: 3, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'lag-trend', x: 6, y: 0, w: 6, h: 3, minW: 4, minH: 2 },
    { i: 'region-distribution', x: 0, y: 2, w: 4, h: 3, minW: 3, minH: 2 },
    { i: 'alerts', x: 4, y: 3, w: 4, h: 2, minW: 3, minH: 2 },
    { i: 'server-table', x: 0, y: 5, w: 12, h: 6, minW: 6, minH: 4 }
  ],
  md: [
    { i: 'server-metrics', x: 0, y: 0, w: 5, h: 2, minW: 3, minH: 2 },
    { i: 'server-status', x: 5, y: 0, w: 5, h: 2, minW: 3, minH: 2 },
    { i: 'lag-trend', x: 0, y: 2, w: 10, h: 3, minW: 6, minH: 2 },
    { i: 'region-distribution', x: 0, y: 5, w: 5, h: 3, minW: 4, minH: 2 },
    { i: 'alerts', x: 5, y: 5, w: 5, h: 3, minW: 4, minH: 2 },
    { i: 'server-table', x: 0, y: 8, w: 10, h: 6, minW: 8, minH: 4 }
  ],
  sm: [
    { i: 'server-metrics', x: 0, y: 0, w: 6, h: 2, minW: 4, minH: 2 },
    { i: 'server-status', x: 0, y: 2, w: 6, h: 2, minW: 4, minH: 2 },
    { i: 'lag-trend', x: 0, y: 4, w: 6, h: 3, minW: 6, minH: 2 },
    { i: 'region-distribution', x: 0, y: 7, w: 6, h: 3, minW: 6, minH: 2 },
    { i: 'alerts', x: 0, y: 10, w: 6, h: 3, minW: 6, minH: 2 },
    { i: 'server-table', x: 0, y: 13, w: 6, h: 6, minW: 6, minH: 4 }
  ]
};

// Widget factory function
const renderWidget = (widget: DashboardWidget) => {
  switch (widget.type) {
    case 'server-metrics':
      return <ServerMetricsWidget widget={widget} />;
    case 'server-status':
      return <ServerStatusWidget widget={widget} />;
    case 'lag-trend':
      return <LagTrendWidget widget={widget} />;
    case 'region-distribution':
      return <RegionDistributionWidget widget={widget} />;
    case 'server-table':
      return <ServerTableWidget widget={widget} />;
    case 'alerts':
      return <AlertsWidget widget={widget} />;
    default:
      return (
        <Card h="100%" p="md">
          <Text c="dimmed" ta="center">
            Widget: {widget.type}
          </Text>
        </Card>
      );
  }
};

const getInitialMenuPosition = () => ({
  x: Math.max(50, window.innerWidth - 370),
  y: 100
});

const Dashboard = () => {
  const dispatch = useAppDispatch();
  const { loading } = useAppSelector((state) => state.servers);
  const [isEditing, setIsEditing] = useState(false);
  const [layouts, setLayouts] = useState(defaultLayouts);
  const [widgets, setWidgets] = useState(defaultWidgets);
  const [visibleWidgets, setVisibleWidgets] = useState(new Set(defaultWidgets.map(w => w.id)));
  const [showWidgetMenu, setShowWidgetMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState(getInitialMenuPosition());
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  useEffect(() => {
    dispatch(fetchServers());
  }, [dispatch]);

  const handleRefresh = useCallback(() => {
    dispatch(fetchServers());
  }, [dispatch]);

  const handleLayoutChange = useCallback((currentLayout: Layout[], allLayouts: { [key: string]: Layout[] }) => {
    setLayouts(allLayouts);
  }, []);

  const toggleWidgetVisibility = (widgetId: string) => {
    setVisibleWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      return newSet;
    });
  };

  const resetToDefault = () => {
    setLayouts(defaultLayouts);
    setWidgets(defaultWidgets);
    setVisibleWidgets(new Set(defaultWidgets.map(w => w.id)));
    setIsEditing(false);
    setShowWidgetMenu(false);
    setMenuPosition(getInitialMenuPosition());
  };

  const closeWidgetMenu = () => {
    setShowWidgetMenu(false);
  };

  // Drag handlers for floating menu
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragOffset({
      x: e.clientX - menuPosition.x,
      y: e.clientY - menuPosition.y
    });
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        const newX = Math.max(0, Math.min(window.innerWidth - 320, e.clientX - dragOffset.x));
        const newY = Math.max(0, Math.min(window.innerHeight - 400, e.clientY - dragOffset.y));
        setMenuPosition({ x: newX, y: newY });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset]);

  const allWidgets = widgets.map(widget => ({
    ...widget,
    isVisible: visibleWidgets.has(widget.id)
  }));

  return (
    <DashboardLayout>
      <div style={{ width: '100%', maxWidth: '1400px', position: 'relative' }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px',
          padding: '16px 0'
        }}>
          <div>
            <Title order={1} style={{ color: 'var(--mantine-color-blue-6)', marginBottom: '4px' }}>
              CHCIT DASHBOARD
            </Title>
            <Text c="dimmed">Monitor your Elastic Disaster Recovery metrics</Text>
          </div>

          <Group gap="sm">
            <ActionIcon
              variant={showWidgetMenu ? 'filled' : 'subtle'}
              color={showWidgetMenu ? 'cyan' : 'gray'}
              onClick={() => {
                if (!showWidgetMenu) {
                  setMenuPosition(getInitialMenuPosition());
                }
                setShowWidgetMenu(!showWidgetMenu);
              }}
              title="Widget Menu"
            >
              <IconMenu2 size={20} />
            </ActionIcon>

            <ActionIcon
              variant="subtle"
              color="gray"
              onClick={resetToDefault}
              title="Reset to Default Layout"
            >
              <IconRestore size={20} />
            </ActionIcon>

            <ActionIcon
              variant={isEditing ? 'filled' : 'subtle'}
              color={isEditing ? 'red' : 'gray'}
              onClick={() => setIsEditing(!isEditing)}
              title={isEditing ? "Lock Layout" : "Edit Layout"}
            >
              {isEditing ? <IconLockOpen size={20} /> : <IconLock size={20} />}
            </ActionIcon>

            <Button
              leftSection={<IconRefresh size={16} />}
              onClick={handleRefresh}
              loading={loading}
              variant="light"
            >
              Refresh
            </Button>

            <ActionIcon variant="subtle" color="gray" title="Settings">
              <IconSettings size={20} />
            </ActionIcon>
          </Group>
        </div>

        {/* Dashboard Grid */}
        <div style={{ flex: 1, padding: '16px 0', overflow: 'auto' }}>
          <ResponsiveGridLayout
            className="layout"
            layouts={layouts}
            onLayoutChange={handleLayoutChange}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
            cols={{ lg: 9, md: 10, sm: 6, xs: 4, xxs: 2 }}
            rowHeight={60}
            isDraggable={isEditing}
            isResizable={isEditing}
            margin={[16, 16]}
            containerPadding={[16, 16]}
            useCSSTransforms={true}
            compactType="vertical"
            preventCollision={false}
            allowOverlap={false}
          >
            {allWidgets.map((widget) => {
              const isVisible = visibleWidgets.has(widget.id);
              return (
                <div
                  key={widget.id}
                  className="widget-container"
                  style={{
                    display: isVisible ? 'block' : 'none'
                  }}
                >
                  <Card
                    h="100%"
                    p={0}
                    style={{
                      position: 'relative',
                      overflow: 'hidden',
                      border: isEditing ? '2px solid var(--mantine-color-blue-4)' : '1px solid var(--mantine-color-gray-3)',
                      borderRadius: '8px',
                      backgroundColor: 'var(--mantine-color-body)'
                    }}
                  >
                    {renderWidget(widget)}
                  </Card>
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>

        {/* Floating Widget Selection Menu */}
        {showWidgetMenu && (
          <>
            {/* Backdrop */}
            <div
              style={{
                position: 'fixed',
                inset: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                backdropFilter: 'blur(2px)',
                zIndex: 40
              }}
              onClick={closeWidgetMenu}
            />

            {/* Draggable Floating Menu */}
            <div
              style={{
                position: 'fixed',
                left: `${menuPosition.x}px`,
                top: `${menuPosition.y}px`,
                minWidth: '320px',
                maxWidth: '90vw',
                cursor: isDragging ? 'grabbing' : 'default',
                zIndex: 50
              }}
            >
              <Card
                shadow="lg"
                p="md"
                style={{
                  backgroundColor: 'var(--mantine-color-body)',
                  border: '1px solid var(--mantine-color-gray-3)'
                }}
              >
                {/* Menu Header */}
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '16px',
                    cursor: 'grab',
                    padding: '8px',
                    borderRadius: '4px',
                    backgroundColor: 'var(--mantine-color-gray-1)'
                  }}
                  onMouseDown={handleMouseDown}
                >
                  <Text fw={600} size="sm">Widget Manager</Text>
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    onClick={closeWidgetMenu}
                    size="sm"
                  >
                    <IconEyeOff size={16} />
                  </ActionIcon>
                </div>

                {/* Widget List */}
                <Stack gap="xs">
                  {widgets.map((widget) => {
                    const isVisible = visibleWidgets.has(widget.id);
                    return (
                      <Group key={widget.id} justify="space-between" align="center">
                        <Text size="sm" fw={500}>{widget.title}</Text>
                        <Button
                          variant={isVisible ? "filled" : "outline"}
                          color={isVisible ? "blue" : "red"}
                          size="xs"
                          leftSection={isVisible ? <IconEye size={12} /> : <IconEyeOff size={12} />}
                          onClick={() => toggleWidgetVisibility(widget.id)}
                          onMouseDown={(e) => e.stopPropagation()}
                        >
                          {isVisible ? 'Hide' : 'Show'}
                        </Button>
                      </Group>
                    );
                  })}
                </Stack>
              </Card>
            </div>
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;